package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sEvent")
@Tag(name = "v3. K8S Event 管理", description = "K8S Event 管理")//, hidden = true)
public interface V3K8sEvent {

    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/events", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Event 列表")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getEvents(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/events/{eventName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Event 详情")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getEvent(@PathVariable String ip, @PathVariable String namespace, @PathVariable String eventName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/events/{eventName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Event Yaml")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getEventYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String eventName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/events/{eventName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 Event")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deleteEvent(@PathVariable String ip, @PathVariable String namespace, @PathVariable String eventName) throws Exception;
}
