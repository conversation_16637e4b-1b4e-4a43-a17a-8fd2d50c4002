package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.*;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sStatefulSet")
@Tag(name = "v3. K8S StatefulSet 管理", description = "K8S StatefulSet 管理")//, hidden = true)
public interface V3K8sStatefulSet {

    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/statefulsets", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 StatefulSet 列表")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getStatefulSets(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 StatefulSet 详情")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getStatefulSet(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 StatefulSet Yaml")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getStatefulSetYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 StatefulSet")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deleteStatefulSet(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}/restart", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "重启")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> restartStatefulSet(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}/resize", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "伸缩")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> updateStatefulSetReplicas(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName, @RequestBody K8sUpdateReplica k8sUpdateReplica) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}/images", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "调整镜像版本")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> updateStatefulSetImages(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName, @RequestBody K8sUpdateImage k8sUpdateImage) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/statefulsets/{statefulSetName}/strategy", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "修改更新策略")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> updateStatefulSetStrategy(@PathVariable String ip, @PathVariable String namespace, @PathVariable String statefulSetName, @RequestBody K8sUpdateStatefulSetStrategy k8sUpdateStatefulSetStrategy) throws Exception;
}
