package skynet.platform.feign.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class PluginNotExistException extends ApiRequestException {


    /**
     * -- GETTER --
     *
     * @return the actionPoint
     */
    private final String plugin;


    public PluginNotExistException(String plugin) {
        super(ApiRequestErrorCode.PLUGIN_NOT_EXIST);
        this.plugin = plugin;
    }

    @Override
    public String getMessage() {
        return String.format("%s[plugin=%s]", super.getMessage(), plugin);
    }
}

