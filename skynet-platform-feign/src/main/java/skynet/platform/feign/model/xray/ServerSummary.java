package skynet.platform.feign.model.xray;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
public class ServerSummary extends Jsonable {

    private String ip;
    /**
     * CPU总逻辑核数
     */
    private int cpuCores;
    /**
     * CPU使用率
     */
    private double cpuUsePerc;

    /**
     * 内存总容量
     */
    private long memTotal;

    /**
     * 内存总使用量
     */
    private long memUsed;

    /**
     * 交换区总容量
     */
    private long swapTotal;

    /**
     * 交换区总使用量
     */
    private long swapUsed;
    /**
     * 磁盘总容量
     */
    private long diskCapTotal;
    /**
     * 磁盘总使用量
     */
    private long diskCapUsed;
    /**
     * 连入总数
     */
    private int connectionsIn;
    /**
     * 连出总数
     */
    private int connectionsOut;

    /**
     * 显卡总数
     */
    private int gpuNum;

    /**
     * 显存总容量
     */
    private long gpuMemTotal;
    /**
     * 显存总使用量
     */
    private long gpuMemUsed;
    /**
     * 显存平均使用率
     */
    private double gpuUsePerc;

    @Override
    public String toString() {
        return super.toString();
    }

}
