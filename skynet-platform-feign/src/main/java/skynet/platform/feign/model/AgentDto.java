package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@Setter
@Schema(title = "Agent信息")
public class AgentDto extends Jsonable {

    @Schema(title = "序号")
    private int index = 0;

    @Schema(title = "Agent类型")//, position = 5)
    private String agentType = AgentType.SERVER;

    @Schema(title = "服务器IP")//, position = 10)
    private String ip;

    @Schema(title = "agent端口")//, position = 11)
    private int serverPort;

    @Schema(title = "SSH端口")//, position = 20)
    private int sshPort;

    @Schema(title = "SSH用户名")//, position = 30)
    private String sshUser;

    @Schema(title = "SSH密码")//, position = 40)
    private String sshPassword;

    @Schema(title = "SSH超时时间")//, position = 50)
    private int timeout = 8;

    @Schema(title = "KubeConfig内容")//, position = 40)
    private String kubeConfig;

    @Schema(title = "k8sNamespace命名空间")//, position = 41)
    private String k8sNamespace;

    @Schema(title = "镜像仓库地址")//, position = 42)
    private String registryUrl;

    @Schema(title = "镜像仓库路由地址")//, position = 43)
    private String registryContextPath;

    @Schema(title = "镜像仓库用户名")//, position = 44)
    private String registryUsername;

    @Schema(title = "镜像仓库密码")//, position = 46)
    private String registryPassword;

    @Schema(title = "描述")//, position = 50)
    private String description;

    @Schema(title = "版本信息， 如3.0.0-SNAPSHOT")//, position = 60)
    private AgentVersionDto version;

    @Schema(title = "服务器标签列表")//, position = 70)
    private List<String> serverTags;

    @Schema(title = "服务器信息")//, position = 80)
    private Map<String, Object> serverInfo;

    @Schema(title = "服务器状态, 在线，离线，分发中")//, position = 90)
    private String status;

    @Schema(title = "是否创建时自动安装")//, position = 100)
    private boolean autoInstall;

    @Schema(title = "是否启用docker")//, position = 110)
    private boolean dockerEnabled = true;

    @Schema(name = "依赖配置块(code)列表")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 120)
    private List<String> configBlockCodes;

    @Override
    public String toString() {
        return super.toString();
    }

    public String getSort() {
        //如果存在标签按标签第一个属性排序、不存在则按INDEX排序
        return CollectionUtils.isEmpty(this.getServerTags()) ? String.valueOf(this.getIndex()) : this.getServerTags().stream().sorted().collect(Collectors.joining("/"));
    }
}
