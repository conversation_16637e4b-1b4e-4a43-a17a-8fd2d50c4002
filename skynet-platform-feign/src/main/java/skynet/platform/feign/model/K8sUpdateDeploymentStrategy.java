package skynet.platform.feign.model;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
public class K8sUpdateDeploymentStrategy extends Jsonable {

    /**
     * 更新策略类型：
     * - 重新创建 Recreate
     * - 滚动更新 RollingUpdate
     */
    private String type;

    /**
     * 滚动更新配置（仅 type == 'RollingUpdate' 时有效）
     */
    private RollingUpdate rollingUpdate;

    @Getter
    @Setter
    public static class RollingUpdate {

        /**
         * 最大超出副本数
         */
        private String maxSurge;

        /**
         * 最大不可用副本数
         */
        private String maxUnavailable;
    }
}
