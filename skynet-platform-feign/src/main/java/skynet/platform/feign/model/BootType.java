package skynet.platform.feign.model;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum BootType {

    BaseBoot, JavaBoot, SpringBoot, SkynetBoot, DockerBoot, K8sBoot;


    public static BootType parse(String bootType) {
        BootType ret = null;
        try {
            ret = BootType.valueOf(bootType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ret;
    }
}
