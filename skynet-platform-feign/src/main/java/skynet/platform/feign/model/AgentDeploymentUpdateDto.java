package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.List;

@Getter
@Setter
@Schema(title = "部署请求-服务器节点")
public class AgentDeploymentUpdateDto extends Jsonable {

    @Schema(title = "服务器IP")//, position = 10)
    private String ip;

    @Schema(title = "服务部署列表")//, position = 20)
    private List<ActionDeploymentUpdateDto> actions;

    @Override
    public String toString() {
        return super.toString();
    }
}
