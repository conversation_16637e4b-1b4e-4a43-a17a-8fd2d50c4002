package version

import (
	"fmt"
	"github.com/spf13/cobra"
	"runtime"
)

var (
	version      string
	gitBranch    string
	gitTag       string
	gitCommit    string
	gitTreeState string
	buildDate    string
)

type Info struct {
	Version      string `json:"version"`
	GitBranch    string `json:"gitBranch"`
	GitTag       string `json:"gitTag"`
	GitCommit    string `json:"gitCommit"`
	GitTreeState string `json:"gitTreeState"`
	BuildDate    string `json:"buildDate"`
	GoVersion    string `json:"goVersion"`
	Compiler     string `json:"compiler"`
	Platform     string `json:"platform"`
}

func (info Info) String() string {
	return info.GitCommit
}

func GetVersion() Info {
	return Info{
		Version:      version,
		GitBranch:    gitBranch,
		GitTag:       gitTag,
		GitCommit:    gitCommit,
		GitTreeState: gitTreeState,
		BuildDate:    buildDate,
		GoVersion:    runtime.Version(),
		Compiler:     runtime.Compiler,
		Platform:     fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}

var (
	versionExample = `
	# Print the client and server versions for the current context
	skynet-init version
	`
)

func Version() {
	info := GetVersion()
	fmt.Printf("Versionn:  %v\n", info.Version)
	fmt.Printf("GitBranch:  %v\n", info.GitBranch)
	fmt.Printf("GitTag:  %v\n", info.GitTag)
	fmt.Printf("GitCommit:  %v\n", info.GitCommit)
	fmt.Printf("GitTreeState:  %v\n", info.GitTreeState)
	fmt.Printf("BuildDate:  %v\n", info.BuildDate)
	fmt.Printf("GoVersion:  %v\n", info.GoVersion)
	fmt.Printf("Compiler:  %v\n", info.Compiler)
	fmt.Printf("Platform:  %v\n", info.Platform)
}

func NewCmdVersion() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "version",
		Short:   "Print the client and server version information",
		Long:    "Print the client and server version information for the current context",
		Example: versionExample,
		Run: func(cmd *cobra.Command, args []string) {
			Version()
		},
	}
	return cmd
}
