package hub_client

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"

	log "github.com/cihub/seelog"
	"github.com/google/go-containerregistry/pkg/authn"
	"github.com/google/go-containerregistry/pkg/crane"
	"github.com/google/go-containerregistry/pkg/name"
	"github.com/google/go-containerregistry/pkg/v1/remote"
	"github.com/google/go-containerregistry/pkg/v1/tarball"
	"github.com/spf13/viper"
)

type DockerHubProperties struct {
	HubHost  string `mapstructure:"k8s_docker_hub_host"`
	Username string `mapstructure:"k8s_docker_hub_username"`
	Password string `mapstructure:"k8s_docker_hub_password"`
}

var properties DockerHubProperties

func init() {
	// init config from init.properties file
	viper.SetConfigName("init")
	viper.SetConfigType("properties")
	viper.AddConfigPath(".")
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("fatal error config file: %w", err))
	}
	err = viper.Unmarshal(&properties)
	if err != nil {
		panic(fmt.Errorf("fatal error config file: %w", err))
	}
}

func pathOpener(path string) tarball.Opener {
	return func() (io.ReadCloser, error) {
		return os.Open(path)
	}
}

// 解析 tar 包的 manifest，获取镜像的 Tag
func getRepoTag(tarFile string) string {
	manifests, err := tarball.LoadManifest(pathOpener(tarFile))
	if err != nil {
		panic("Loading tarball manifests error, tarFile = " + tarFile + ", error = " + err.Error())
	}
	for _, manifest := range manifests {
		for _, repoTag := range manifest.RepoTags {
			return repoTag
		}
	}
	panic("Parsing tarball repoTag empty, tarFile = " + tarFile)
}

func Push(tarFile, dst string) error {
	log.Info("Pushing image, tarFile = ", tarFile, ", dst = ", dst)

	img, err := crane.Load(tarFile)
	if err != nil {
		return fmt.Errorf("loading %s as tarball: %w", tarFile, err)
	}

	basic := &authn.Basic{Username: properties.Username, Password: properties.Password}
	options := crane.GetOptions(crane.Insecure, crane.WithTransport(http.DefaultTransport), crane.WithAuth(basic))

	if dst == "" {
		dst = getRepoTag(tarFile)
		log.Info("dst is blank, get repoTag from tarball = ", dst)
	}
	//替换 tag
	tag, err := name.NewTag(dst)
	if err != nil {
		return fmt.Errorf("parsing reference %q: %w", dst, err)
	}
	log.Info("Before replace, dst = ", dst, ", tag.Registry = ", tag.Registry.String())
	if tag.Registry.String() == name.DefaultRegistry {
		dst = fmt.Sprintf("%s/%s", properties.HubHost, dst)
	} else {
		hostname := getHostname(properties.HubHost)
		log.Info("Replace " + tag.Registry.String() + " with " + hostname)
		dst = strings.Replace(dst, tag.Registry.String(), hostname, 1)
	}
	log.Info("After replace, dst = ", dst)

	tag, err = name.NewTag(dst, options.Name...)
	if err != nil {
		return fmt.Errorf("parsing reference %q: %w", dst, err)
	}

	log.Info("Push img to: ", tag)
	return remote.Write(tag, img, options.Remote...)

	//err = crane.Push(img, tag, crane.Insecure, crane.WithTransport(http.DefaultTransport), crane.WithAuth(basic))
	//if err != nil {
	//	return err
	//}
	//return nil
}

// 获取镜像仓库的主机名部分
func getHostname(registryUrl string) string {
	re := regexp.MustCompile(`^(?:(?:https?://)?([^/]+))`)
	matches := re.FindStringSubmatch(registryUrl)
	if len(matches) > 1 {
		return matches[1]
	}
	return registryUrl
}
