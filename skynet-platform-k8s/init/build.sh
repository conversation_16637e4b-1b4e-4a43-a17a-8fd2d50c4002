docker rmi alpine:3.17
docker load -i /root/alpine-amd64.tar
docker build -t skynet/init .
docker save skynet/init -o skynet-init.tar
docker rmi skynet/init

docker rmi alpine:3.17
docker load -i /root/alpine-arm64.tar
docker build --platform linux/arm64 --build-arg GOOS=linux --build-arg GOARCH=arm64 -t  skynet/init:arm64 .
docker save skynet/init:arm64 -o skynet-init-arm64.tar
docker rmi skynet/init:arm64
