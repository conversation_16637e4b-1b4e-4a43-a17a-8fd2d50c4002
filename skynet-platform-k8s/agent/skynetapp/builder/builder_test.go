package builder

import (
	"testing"

	"github.com/spf13/viper"
)

func TestParseCmdArgs(t *testing.T) {
	dockerRunCmdAndArgs := "sh -c \"/usr/local/openresty/bin/openresty && cd /prometheus && ./prometheus --web.enable-lifecycle --storage.tsdb.retention.time=${storage.tsdb.retention.time} --storage.tsdb.path=${storage.tsdb.path}\""
	command := parseCmdArgs(dockerRunCmdAndArgs)
	for _, c := range command {
		t.Log(c)
	}
}

func TestParsePorts(t *testing.T) {
	dockerRunCmdAndArgs := "-p 30080:80\n-e SKYNET_URI=http://127.0.0.1:2230\n-e COMPONENT_URI=http://localhost:9090\n-e SKYNET_AUTH_IGNORE_PATTERNS=/api/.*,/-/reload\n-e NGINX_LOG_LEVEL=${skynet.auth.nginx.log.level}\n-v /iflytek/server/skynet/prometheus-2.7.2:/prometheus\n-v /iflytek/volume/turing:/turing"
	ports := parsePorts(dockerRunCmdAndArgs)
	for _, p := range ports {
		t.Log(p.ContainerPort, p.NodePort)
	}
}

func TestParseEnvs(t *testing.T) {
	dockerRunCmdAndArgs := "-p 30080:80\n-e SKYNET_URI=http://127.0.0.1:2230\n-e COMPONENT_URI=http://localhost:9090\n-e SKYNET_AUTH_IGNORE_PATTERNS=/api/.*,/-/reload\n-e NGINX_LOG_LEVEL=${skynet.auth.nginx.log.level}\n-v /iflytek/server/skynet/prometheus-2.7.2:/prometheus\n-v /iflytek/volume/turing:/turing"
	envs := parseEnvs(dockerRunCmdAndArgs)
	for _, env := range envs {
		t.Log(env.Name, env.Value)
	}
}

func TestParseMounts(t *testing.T) {
	viper.Set("skynet.home", "/iflytek/server/skynet")
	dockerRunCmdAndArgs := "-p 30080:80\n-e SKYNET_URI=http://127.0.0.1:2230\n-e COMPONENT_URI=http://localhost:9090\n-e SKYNET_AUTH_IGNORE_PATTERNS=/api/.*,/-/reload\n-e NGINX_LOG_LEVEL=${skynet.auth.nginx.log.level}\n-v /iflytek/server/skynet/prometheus-2.7.2:/prometheus\n-v /iflytek/volume/turing:/turing"
	mounts := parseMounts("skyline-mysql-skyline", dockerRunCmdAndArgs)
	for _, env := range mounts {
		t.Log(env.Name, env.MountPath, env.SubPath)
	}
}
