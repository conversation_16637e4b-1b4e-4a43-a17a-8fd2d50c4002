package builder

import (
	"fmt"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/go-containerregistry/pkg/name"
	"github.com/mattn/go-shellwords"
	"github.com/spf13/viper"
	"iflytek.com/skynet/agent/controller"
	"iflytek.com/skynet/agent/skynet"
	skynetappv1alpha1 "iflytek.com/skynet/agent/skynetapp/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type SkynetAppBuilder struct {
	BootType            string
	ActionPortsOriginal []int32
	ActionPortsValid    []int32
	ActionInfo          controller.ActionInfo
	ActionDefinition    skynet.ActionDefinitionDto
}

func NewBuilder(bootType string, actionPorts []int32, actionInfo controller.ActionInfo) *SkynetAppBuilder {

	validPorts := getValidActionPorts(actionPorts)
	return &SkynetAppBuilder{
		BootType:            bootType,
		ActionPortsOriginal: actionPorts,
		ActionPortsValid:    validPorts,
		ActionInfo:          actionInfo,
		ActionDefinition:    fetchDefinition(actionInfo, validPorts),
	}
}

// request xmanager to get the decoded definition
func fetchDefinition(actionInfo controller.ActionInfo, actionPorts []int32) skynet.ActionDefinitionDto {
	skynet_client := skynet.NewClient(skynet.ClientProperties{
		ManagerUrl:  viper.GetString("skynet.manager.url"),
		ApiKey:      viper.GetString("skynet.auth.api-key"),
		ApiSecret:   viper.GetString("skynet.auth.api-secret"),
		ActionPoint: actionInfo.Code + "@" + actionInfo.Plugin,
		Index:       actionInfo.Index,
		IP:          viper.GetString("server.ip"),
	})

	port := int(actionPorts[0])
	ports := []int{}
	if len(actionPorts) > 1 {
		for _, p := range actionPorts[1:] {
			ports = append(ports, int(p))
		}
	}
	response := skynet_client.FetchDefinition(port, ports)
	return response
}

// build SkynetApps by the action info and definition
func (builder *SkynetAppBuilder) BuildSkynetApp() skynetappv1alpha1.SkynetApp {

	name := builder.getSkynetAppName()
	fmt.Printf("Build skynet app %s\n", name)

	replicas := int32(builder.ActionInfo.Replicas)
	return skynetappv1alpha1.SkynetApp{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "skynet.iflytek.com/v1alpha1",
			Kind:       "SkynetApp",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: viper.GetString("skynet.k8s.namespace"),
		},
		Spec: skynetappv1alpha1.SkynetAppSpec{
			ActionCode:   builder.ActionInfo.Code,
			PluginCode:   builder.ActionInfo.Plugin,
			Index:        int32(builder.ActionInfo.Index),
			Replicas:     &replicas,
			BootType:     builder.BootType,
			SkynetHome:   viper.GetString("skynet.home"),
			InitOption:   builder.buildInitOption(builder.ActionInfo.Index),
			BootOption:   builder.buildBootOption(builder.ActionInfo.Index),
			MeshOption:   builder.buildMeshOption(),
			NodeSelector: builder.ActionInfo.NodeSelector,
			Props:        builder.buildProps(),
		},
	}
}

// 扩展参数
func (builder *SkynetAppBuilder) buildProps() map[string]string {
	props := make(map[string]string)
	if builder.ActionDefinition.SwitchLabels != nil {
		for _, switchLabel := range builder.ActionDefinition.SwitchLabels {
			if switchLabel.Code == "enablePrometheusTarget" && switchLabel.Value {
				// 开启 Prometheus 监控采集
				props["prometheusTarget"] = switchLabel.ExtProperty
			}
		}
	}
	return props
}

// Get the name of this skynet app
func (builder *SkynetAppBuilder) getSkynetAppName() string {
	name := builder.ActionInfo.Code + "-" + builder.ActionInfo.Plugin
	if builder.ActionInfo.Index > 0 {
		name = builder.ActionInfo.Code + "-" + builder.ActionInfo.Plugin + "-" + strconv.Itoa(builder.ActionInfo.Index)
	}
	return name
}

// build the mesh container options
func (builder *SkynetAppBuilder) buildMeshOption() *skynetappv1alpha1.BootOption {
	if !builder.ActionDefinition.IntegrationConfig.MeshEnabled {
		fmt.Printf("Skynet app mesh is disabled.\n")
		return nil
	}
	fmt.Printf("Building Skynet app mesh options.\n")
	image := fmt.Sprintf("%s/%s/mesh:%s-%s",
		viper.GetString("registry.url"), viper.GetString("registry.context-path"), viper.GetString("agent.project-version"), viper.GetString("agent.build-sid"))
	return &skynetappv1alpha1.BootOption{
		Image: image,
		Command: []string{
			"/skynet/skynet-mesh",
			"server",
			"-c",
			fmt.Sprintf("%s/mesh.conf", viper.GetString("skynet.home")),
		},
		Mounts: builder.buildMeshMounts(builder.getSkynetAppName()),
	}
}

func (builder *SkynetAppBuilder) buildMeshMounts(name string) []skynetappv1alpha1.MountOption {
	mountOptions := []skynetappv1alpha1.MountOption{}
	mountOptions = append(mountOptions, skynetappv1alpha1.MountOption{
		Name:      fmt.Sprintf("%s-resources", name),
		MountPath: viper.GetString("skynet.home"),
		MountType: "emptyDir",
		SubPath:   fmt.Sprintf(".%s", viper.GetString("skynet.home")),
	})
	return mountOptions
}

// build the init container options
func (builder *SkynetAppBuilder) buildInitOption(index int) skynetappv1alpha1.InitOption {
	image := fmt.Sprintf("%s/%s/init:%s-%s",
		viper.GetString("registry.url"), viper.GetString("registry.context-path"), viper.GetString("agent.project-version"), viper.GetString("agent.build-sid"))
	return skynetappv1alpha1.InitOption{
		Image: image,
		Command: []string{
			"/init",
			"--action-code", builder.ActionInfo.Code,
			"--plugin-code", builder.ActionInfo.Plugin,
			"--index", strconv.Itoa(index),
			"--port", builder.convertAppPort(),
			"--ports", builder.convertExtPorts(),
		},
	}
}

// 获取 SkynetApp 主端口
func (builder *SkynetAppBuilder) convertAppPort() string {
	return strconv.Itoa(int(builder.ActionPortsValid[0]))
}

// 获取 SkynetApp 扩展端口
func (builder *SkynetAppBuilder) convertExtPorts() string {
	if len(builder.ActionPortsValid) == 1 {
		return ""
	}
	extPortsInt := builder.ActionPortsValid[1:]
	extPorts := make([]string, len(extPortsInt))
	for i, port := range extPortsInt {
		extPorts[i] = strconv.Itoa(int(port))
	}
	return strings.Join(extPorts, ",")
}

// build the main container options
func (builder *SkynetAppBuilder) buildBootOption(index int) skynetappv1alpha1.BootOption {
	if builder.BootType == "SpringBoot" {
		return builder.buildSpringBootOption()
	} else if builder.BootType == "BaseBoot" {
		return builder.buildBaseBootOption()
	} else if builder.BootType == "K8sBoot" {
		return builder.buildK8sBootOption()
	} else {
		return builder.buildDockerBootOption()
	}
}

func (builder *SkynetAppBuilder) buildSpringBootOption() skynetappv1alpha1.BootOption {
	image := fmt.Sprintf("%s/%s/openjdk:8", viper.GetString("registry.url"), viper.GetString("registry.context-path"))
	return skynetappv1alpha1.BootOption{
		Image: image,
		Ports: builder.checkPorts(builder.getDefinedPorts()),
		Command: []string{
			"/bin/bash", "-c", viper.GetString("skynet.home") + "/target.sh",
		},
	}
}

func (builder *SkynetAppBuilder) buildBaseBootOption() skynetappv1alpha1.BootOption {
	image := fmt.Sprintf("%s/%s/centos:7", viper.GetString("registry.url"), viper.GetString("registry.context-path"))
	return skynetappv1alpha1.BootOption{
		Image: image,
		Ports: builder.checkPorts(builder.getDefinedPorts()),
		Command: []string{
			"/bin/bash", "-c", viper.GetString("skynet.home") + "/target.sh",
		},
	}
}

func (builder *SkynetAppBuilder) buildK8sBootOption() skynetappv1alpha1.BootOption {
	return skynetappv1alpha1.BootOption{
		Ports: builder.checkPorts(builder.getDefinedPorts()),
		Yaml:  builder.ActionDefinition.Yaml,
	}
}

func (builder *SkynetAppBuilder) buildDockerBootOption() skynetappv1alpha1.BootOption {
	runnableJar := builder.ActionDefinition.StartupConfig.RunnableJar
	dockerRunOptions := builder.ActionDefinition.StartupConfig.DockerRunOptions
	dockerRunCmdAndArgs := builder.ActionDefinition.StartupConfig.DockerRunCmdAndArgs

	image, err := builder.formatImage(runnableJar)
	if err != nil {
		fmt.Println("Format image error, image = ", runnableJar, err.Error())
		image = fmt.Sprintf("%s/%s", viper.GetString("registry.url"), runnableJar)
	}
	ports := parsePorts(dockerRunOptions)
	if len(ports) <= 0 {
		ports = builder.getDefinedPorts()
	}
	return skynetappv1alpha1.BootOption{
		Image:       image,
		Args:        parseCmdArgs(dockerRunCmdAndArgs),
		Ports:       builder.checkPorts(ports),
		Envs:        parseEnvs(dockerRunOptions),
		Mounts:      parseMounts(builder.getSkynetAppName(), dockerRunOptions),
		Privileged:  parsePrivileged(dockerRunOptions),
		HostNetwork: parseHostNetwork(dockerRunOptions),
		Gpus:        parseGpus(dockerRunOptions),
	}
}

// 重新格式化镜像名称，如果镜像名称中带有自己的 Registry 地址，需要统一替换
func (builder *SkynetAppBuilder) formatImage(image string) (string, error) {

	tag, err := name.NewTag(image)
	if err != nil {
		return "", err
	}

	fmt.Println("Before replace, image = ", image, ", tag.Registry = ", tag.Registry.String())
	if tag.Registry.String() == name.DefaultRegistry {
		image = fmt.Sprintf("%s/%s", viper.GetString("registry.url"), image)
	} else {
		hostname := getHostname(viper.GetString("registry.url"))
		fmt.Println("Replace " + tag.Registry.String() + " with " + hostname)
		image = strings.Replace(image, tag.Registry.String(), hostname, 1)
	}
	fmt.Println("After replace, image = ", image)

	tag, err = name.NewTag(image)
	if err != nil {
		return "", err
	}
	return tag.String(), nil
}

// 获取镜像仓库的主机名部分
func getHostname(registryUrl string) string {
	re := regexp.MustCompile(`^(?:(?:https?://)?([^/]+))`)
	matches := re.FindStringSubmatch(registryUrl)
	if len(matches) > 1 {
		return matches[1]
	}
	return registryUrl
}

func (builder *SkynetAppBuilder) getDefinedPorts() []skynetappv1alpha1.PortOption {
	ports := []skynetappv1alpha1.PortOption{}
	for _, actionPortValid := range builder.ActionPortsValid {
		ports = append(ports, skynetappv1alpha1.PortOption{
			NodePort:      actionPortValid,
			ContainerPort: actionPortValid,
		})
	}
	return ports
}

// check and replace invalid ports
func (builder *SkynetAppBuilder) checkPorts(portOptions []skynetappv1alpha1.PortOption) []skynetappv1alpha1.PortOption {
	ports := []skynetappv1alpha1.PortOption{}
	for _, port := range portOptions {
		nodePort := port.NodePort
		for i, original := range builder.ActionPortsOriginal {
			if nodePort == original {
				nodePort = builder.ActionPortsValid[i]
			}
		}
		if !isPortValid(nodePort) {
			nodePort = getValidPort()
		}
		ports = append(ports, skynetappv1alpha1.PortOption{
			NodePort:      nodePort,
			ContainerPort: port.ContainerPort,
		})
	}
	return ports
}

func parseCmdArgs(dockerRunCmdAndArgs string) []string {
	args, _ := shellwords.Parse(dockerRunCmdAndArgs)
	return args
}

func parsePorts(dockerRunOptions string) []skynetappv1alpha1.PortOption {
	portOptions := []skynetappv1alpha1.PortOption{}
	options, _ := shellwords.Parse(dockerRunOptions)
	for i, o := range options {
		if o == "-p" && i < len(options)-1 {
			portStr := options[i+1]
			port := strings.Split(portStr, ":")
			if len(port) == 2 {
				nodePort, _ := strconv.Atoi(port[0])
				containerPort, _ := strconv.Atoi(port[1])
				portOptions = append(portOptions, skynetappv1alpha1.PortOption{
					NodePort:      int32(nodePort),
					ContainerPort: int32(containerPort),
				})
			}
		}
	}
	return portOptions
}

func parseEnvs(dockerRunOptions string) []skynetappv1alpha1.EnvOption {
	envOptions := []skynetappv1alpha1.EnvOption{}
	options, _ := shellwords.Parse(dockerRunOptions)
	for i, o := range options {
		if o == "-e" && i < len(options)-1 {
			envStr := options[i+1]
			env := strings.Split(envStr, "=")
			if len(env) == 2 {
				envOptions = append(envOptions, skynetappv1alpha1.EnvOption{
					Name:  env[0],
					Value: env[1],
				})
			}
		}
	}
	return envOptions
}

func parseMounts(name string, dockerRunOptions string) []skynetappv1alpha1.MountOption {
	volumeCnt := 0
	mountOptions := []skynetappv1alpha1.MountOption{}
	options, _ := shellwords.Parse(dockerRunOptions)
	for i, o := range options {
		if o == "-v" && i < len(options)-1 {
			volumeStr := options[i+1]
			volume := strings.Split(volumeStr, ":")
			if len(volume) == 2 || len(volume) == 3 {
				if strings.HasPrefix(volume[0], viper.GetString("skynet.home")) {
					// If the dir is in skynet home, this is a resource ( emptyDir )
					mountOptions = append(mountOptions, skynetappv1alpha1.MountOption{
						Name:             fmt.Sprintf("%s-resources", name),
						MountPath:        volume[1],
						MountType:        "emptyDir",
						MountPropagation: getMountPropagation(volume),
						SubPath:          fmt.Sprintf(".%s", volume[0]),
					})
				} else {
					// Otherwise, this is a volume ( hostPath )
					volumeCnt = volumeCnt + 1
					pathType := "FileOrCreate"
					if strings.HasSuffix(volume[0], "/") {
						pathType = "DirectoryOrCreate"
					}
					if strings.HasSuffix(volume[0], ".sock") {
						pathType = "Socket"
					}
					mountOptions = append(mountOptions, skynetappv1alpha1.MountOption{
						Name:             fmt.Sprintf("%s-volume-%d", name, volumeCnt),
						MountPath:        volume[1],
						MountType:        "hostPath",
						MountPropagation: getMountPropagation(volume),
						Path:             volume[0],
						PathType:         pathType,
					})
				}
			}
		}
	}
	return mountOptions
}

func getMountPropagation(volume []string) string {
	if len(volume) == 3 {
		if volume[2] == "rslave" {
			return "HostToContainer"
		}
		if volume[2] == "rshared" {
			return "Bidirectional"
		}
	}
	return "None"
}

func parsePrivileged(dockerRunOptions string) *bool {
	privileged := true
	options, _ := shellwords.Parse(dockerRunOptions)
	for _, o := range options {
		if o == "--privileged" || o == "--privileged=true" {
			return &privileged
		}
	}
	return nil
}

func parseHostNetwork(dockerRunOptions string) bool {
	options, _ := shellwords.Parse(dockerRunOptions)
	for _, o := range options {
		if o == "--net=host" || o == "--network=host" {
			return true
		}
	}
	return false
}

func parseGpus(dockerRunOptions string) int {
	options, _ := shellwords.Parse(dockerRunOptions)
	for i, o := range options {
		if o == "--gpus" && i < len(options)-1 {
			gpuStr := options[i+1]
			if gpuStr == "all" {
				return 8 // TODO 如何获取 GPU 的数量？
			}
			if strings.Contains(gpuStr, ",") {
				ss := strings.Split(gpuStr, ",")
				return len(ss)
			}
			if strings.Contains(gpuStr, "-") {
				ss := strings.Split(gpuStr, "-")
				if len(ss) == 2 {
					from, _ := strconv.Atoi(ss[0])
					to, _ := strconv.Atoi(ss[1])
					return to - from + 1
				}
			}
			return 1
		}
	}
	return 0
}

// 端口自动修正功能，检查端口是否在 k8s 允许的端口范围内，如果不在，则随机生成一个
func getValidActionPort(actionPort int32) int32 {

	if viper.GetString("builder.auto-fix-port") != "true" {
		return actionPort
	}

	if isPortValid(actionPort) {
		return actionPort
	} else {
		return getValidPort()
	}
}

// replace actionPorts with valid ports
func getValidActionPorts(actionPorts []int32) []int32 {
	results := []int32{}
	for _, port := range actionPorts {
		results = append(results, getValidActionPort(port))
	}
	return results
}

func isPortValid(port int32) bool {
	return port >= 30000 && port < 32768
}

func getValidPort() int32 {
	rand.Seed(time.Now().UnixNano())
	return int32(30000 + rand.Intn(2768))
}
