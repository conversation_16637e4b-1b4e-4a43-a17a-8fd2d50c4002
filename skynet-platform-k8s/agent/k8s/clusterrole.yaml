apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: skynet-agent-role
rules:
- apiGroups:
  - ""
  resources:
  - services
  - pods
  - pods/log
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  - statefulsets
  - daemonsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - skynet.iflytek.com
  resources:
  - skynetapps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - skynet.iflytek.com
  resources:
  - skynetapps/finalizers
  verbs:
  - update
- apiGroups:
  - skynet.iflytek.com
  resources:
  - skynetapps/status
  verbs:
  - get
  - patch
  - update
