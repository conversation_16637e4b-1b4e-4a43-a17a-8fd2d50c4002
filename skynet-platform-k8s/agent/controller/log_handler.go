package controller

import (
	"bufio"
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/spf13/viper"
	"iflytek.com/skynet/agent/skynetapp/client"
	corev1 "k8s.io/api/core/v1"
)

func LogHandler(c *gin.Context) {

	replacer := strings.NewReplacer("@", "-", "_", "-")
	aid := replacer.Replace(c.Param("aid"))

	upGrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}

	ws, err := upGrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		panic(err)
	}

	defer func() {
		closeSocketErr := ws.Close()
		if closeSocketErr != nil {
			panic(err)
		}
	}()

	isPause := false
	go sendLog(aid, ws, &isPause)

	for {
		_, msg, err := ws.ReadMessage()
		if err != nil {
			fmt.Println("Error in read message", err.Error())
			break
		}
		isPause = string(msg) == "pause"
		if isPause {
			err = ws.WriteMessage(websocket.TextMessage, []byte("--[pause trace]--------------"))
		} else {
			err = ws.WriteMessage(websocket.TextMessage, []byte("--[continue track]--------------"))
		}
		if err != nil {
			fmt.Println("Error in write message", err.Error())
			break
		}
	}
}

func sendLog(aid string, ws *websocket.Conn, isPause *bool) {

	// There maybe many pods with the label, get a running pod here
	namespace := viper.GetString("skynet.k8s.namespace")
	runningPod, err := GetRunningPodByLabel(aid, namespace)
	if err != nil {
		fmt.Println("Get running pod error: ", err.Error())
		return
	}

	// Get the pod logs
	clientset := client.NewClientset()
	tailLines := int64(200)
	podLogOpts := corev1.PodLogOptions{
		Follow:    true,
		TailLines: &tailLines,
	}
	req := clientset.CoreV1().Pods(namespace).GetLogs(runningPod.Name, &podLogOpts)
	podLogs, err := req.Stream(context.TODO())
	if err != nil {
		fmt.Println("Error in opening stream", err.Error())
		return
	}
	defer podLogs.Close()

	reader := bufio.NewReader(podLogs)

	// Read and write logs forever
	for {
		log, err := reader.ReadString('\n')
		if err != nil {
			fmt.Println("Error in read logs", err.Error())
			break
		}

		err = ws.WriteMessage(websocket.TextMessage, []byte(log))
		if err != nil {
			fmt.Println("Error in write logs", err.Error())
			break
		}

		for *isPause {
			time.Sleep(time.Second)
		}
	}
}
