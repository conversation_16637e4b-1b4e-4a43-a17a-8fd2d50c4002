package controllers

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"

	skynetv1alpha1 "iflytek.com/skynet/operator/api/v1alpha1"
)

// 创建 or 更新 configMap
func (r *SkynetAppReconciler) updateConfigMap(skynetApp *skynetv1alpha1.SkynetApp, configMapToUpdate *corev1.ConfigMap) error {

	configMapName := types.NamespacedName{
		Namespace: configMapToUpdate.Namespace,
		Name:      configMapToUpdate.Name,
	}

	// 查询 configMap 是否存在
	fmt.Printf("Get configMap [%s] for SkynetApp [%s] ...\n", configMapToUpdate.Name, skynetApp.Name)
	configMap := &corev1.ConfigMap{}
	if err := r.Get(context.TODO(), configMapName, configMap); err != nil {
		if errors.IsNotFound(err) {
			// configMap 不存在，则创建一个新的
			fmt.Printf("Get configMap [%s] for SkynetApp [%s] not found\n", configMapToUpdate.Name, skynetApp.Name)
			fmt.Printf("Create configMap [%s] for SkynetApp [%s] ...\n", configMapToUpdate.Name, skynetApp.Name)
			ctrl.SetControllerReference(skynetApp, configMapToUpdate, r.Scheme)
			if err := r.Create(context.TODO(), configMapToUpdate); err != nil {
				fmt.Printf("Create configMap [%s] for SkynetApp [%s] error\n", configMapToUpdate.Name, skynetApp.Name)
				return err
			}
			return nil
		} else {
			// 获取 configMap 出错
			fmt.Printf("Get configMap [%s] for SkynetApp [%s] error\n", configMapToUpdate.Name, skynetApp.Name)
			return err
		}
	} else {
		// configMap 已存在，则更新
		fmt.Printf("Get configMap [%s] for SkynetApp [%s] success\n", configMapToUpdate.Name, skynetApp.Name)
		configMap.Data = configMapToUpdate.Data
		ctrl.SetControllerReference(skynetApp, configMap, r.Scheme)
		fmt.Printf("Update configMap [%s] for SkynetApp [%s] ...\n", configMapToUpdate.Name, skynetApp.Name)
		if err := r.Update(context.TODO(), configMap); err != nil {
			fmt.Printf("Update configMap [%s] for SkynetApp [%s] error\n", configMapToUpdate.Name, skynetApp.Name)
			return err
		}
	}
	return nil
}
