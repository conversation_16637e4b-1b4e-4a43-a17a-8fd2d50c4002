package controllers

import (
	"context"
	"fmt"

	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"

	skynetv1alpha1 "iflytek.com/skynet/operator/api/v1alpha1"
)

// 创建 or 更新 roleBinding
func (r *SkynetAppReconciler) updateRoleBinding(skynetApp *skynetv1alpha1.SkynetApp, roleBindingToUpdate *rbacv1.RoleBinding) error {

	roleBindingName := types.NamespacedName{
		Namespace: roleBindingToUpdate.Namespace,
		Name:      roleBindingToUpdate.Name,
	}

	// 查询 roleBinding 是否存在
	fmt.Printf("Get roleBinding [%s] for SkynetApp [%s] ...\n", roleBindingToUpdate.Name, skynetApp.Name)
	roleBinding := &rbacv1.RoleBinding{}
	if err := r.Get(context.TODO(), roleBindingName, roleBinding); err != nil {
		if errors.IsNotFound(err) {
			// roleBinding 不存在，则创建一个新的
			fmt.Printf("Get roleBinding [%s] for SkynetApp [%s] not found\n", roleBindingToUpdate.Name, skynetApp.Name)
			fmt.Printf("Create roleBinding [%s] for SkynetApp [%s] ...\n", roleBindingToUpdate.Name, skynetApp.Name)
			ctrl.SetControllerReference(skynetApp, roleBindingToUpdate, r.Scheme)
			if err := r.Create(context.TODO(), roleBindingToUpdate); err != nil {
				fmt.Printf("Create roleBinding [%s] for SkynetApp [%s] error\n", roleBindingToUpdate.Name, skynetApp.Name)
				return err
			}
			return nil
		} else {
			// 获取 roleBinding 出错
			fmt.Printf("Get roleBinding [%s] for SkynetApp [%s] error\n", roleBindingToUpdate.Name, skynetApp.Name)
			return err
		}
	} else {
		// roleBinding 已存在，则更新
		fmt.Printf("Get roleBinding [%s] for SkynetApp [%s] success\n", roleBindingToUpdate.Name, skynetApp.Name)
		roleBinding.Subjects = roleBindingToUpdate.Subjects
		roleBinding.RoleRef = roleBindingToUpdate.RoleRef
		ctrl.SetControllerReference(skynetApp, roleBinding, r.Scheme)
		fmt.Printf("Update roleBinding [%s] for SkynetApp [%s] ...\n", roleBindingToUpdate.Name, skynetApp.Name)
		if err := r.Update(context.TODO(), roleBinding); err != nil {
			fmt.Printf("Update roleBinding [%s] for SkynetApp [%s] error\n", roleBindingToUpdate.Name, skynetApp.Name)
			return err
		}
	}
	return nil
}
