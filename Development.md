## Skynet2.1快速入门


### 开发调试

#### xManager
>
    project:
        skynet-platform-xmanager
    main class
        Bootstrap
    Proc args:
        --ip=127.0.0.1 (可选 默认本机第一个IP)
        --server.port=2230 (可选，默认6230)
    VM args:
        -Dskynet.zookeeper.server_list=**************:2181 （必选）

#### xAgent
>
    project:
        skynet-platform-xagent
    main class
        skynet.boot.Bootstrap
    Proc args:
        --skynet.ipAddress=127.0.0.1 (可选 默认本机第一个IP)
        --server.port=6230 (可选，默认6230)
    VM args:
        -Dskynet.zookeeper.server_list=**************:2181 （必选）
        -Dskynet.home=${worke_home}/skynet-v2.1/skynet-boot-build/target/skynet-boot-build-2.1.1008-SNAPSHOT-RELEASE/skynet-boot-build-2.1.1008-SNAPSHOT 

#### antWorker
>
    project:
        ${二次开发项目}
    main class
        skynet.boot.App
    Proc args:
			--skynet.actionName=ant-demo-v20@ant
			--skynet.ipAddress=127.0.0.1 (可选 默认本机第一个IP)
			--server.port=0 (自动义)
			--eureka.client.serviceUrl.defaultZone=http://**************:7070/eureka/  (可选)
			--skynet.config.location.uri=http://**************:6230/skynet/config/demo-host-skynet@ant （优先级最高）或
			--spring.config.location=application.properties
    VM args:
        -Dskynet.zookeeper.server_list=**************:2181 （可选）

### 服务启动

#### xManager
##### 启动
``` bash
./ant-xmanager.sh start
```
##### 停止
``` bash
./ant-xmanager.sh stop
```
#### xAgent

##### 启动
``` bash
./ant-server.sh start  **************
```
##### 停止
``` bash
./ant-server.sh stop
```
#### antWorker(针对SpringBoot)

在skynet托管中启动脚本说明：

``` bash
/bin/sh -c cd /iflytek/server/skynet/plugin/sample/lib && 
java 
-Dskynet.jagent.config.location.uri=http://**************:6230/skynet/config/sample-skynet-sample@sample?actionId=sample-skynet-sample@sample 
-jar 
/iflytek/server/skynet/lib/skynet-platform-xloader-2.1.1008-SNAPSHOT.jar 
--skynet.action.boot.file=skynet-sample-skynetboot-2.1.1008-SNAPSHOT.jar
--skynet.actionName=sample-skynet-sample@sample 
--skynet.action.jagent.file=/iflytek/server/skynet/lib/jagent
--server.port=9292
```

> 备注

> - skynet.jagent.config.location.uri：远程配置地址，目前支持HTTP和本地配置文件，格式是与SpringBoot的application.properties一致，采用key=value格式
> - skynet-platform-xloader*.jar: ActionBoot jar 加载器
> - skynet.action.boot.file：ActionBoot 主jar（必须是fat jar）
> - skynet.actionName：Action服务名称 
> - skynet.action.jagent.file：非侵入式代理 jar文件列表（可以是文件或目录） 主要功能：注入日志采集、远程配置加载、[服务注册发现]、[性能指标采集]。
                

> 以上脚本是自动生成的

SkynetBoot独立运行脚本形式：

``` bash
java -jar skynet-sample-skynetboot-2.1.1008-SNAPSHOT.jar
--spring.config.location=application.properties
```

application.properties 需要包含以下配置：

```
#skynet base setting 
skynet.actionName=sample-skynetboot@sample （必选）
skynet.actionId=sample-skynetboot@sample (可选)
skynet.ipAddress=************** (可选)

skynet.jsondoc.enabled=true (可选)
skynet.swagger2.enabled=true (可选)

#skynet.action.service （必选）
skynet.action.service.name=skynet.rest.sample
skynet.action.service.desc=
skynet.action.service.context={"key":"value"}

#skynet.zookeeper (可选)
skynet.zookeeper.cluster_name=skynet
skynet.zookeeper.server_list=**************:2181
skynet.zookeeper.session_timeout=20000
skynet.zookeeper.connection_timeout=30000

#eureka servers setting (可选)
eureka.client.serviceUrl.defaultZone=
eureka.instance.preferIpAddress=true
eureka.instance.ipAddress=**************
eureka.client.fetchRegistry=false
eureka.client.registerWithEureka=false
logging.config=classpath:logback-spring.xml

#skynet props and logger
logging.path=../log
logging.path=sample-skynetboot.log

logging.level.com.iflytek=INFO
```

如果此Action服务已经在skynet中进行了服务定义，可以通过以下URL 自动生成 上述配置文件
如：
http://{skynet.agent.ip}:6230/skynet/config/sample-skynet-sample@sample?actionId=sample-skynet-sample@sample 


### 日志配置管理
 
#### 日志文件配置

只能配置日志文件路径：
zkPath=/skynet/setting/_properties
logging.file.path	= ../log

日志文件名命名规则：{logging.path}/{skynet.actionName}.log 
 
#### 级别配置

根据作用范围分三级配置：
- 集群级：zkPath=/skynet/setting/_logger
- 系统级：zkPath=/skynet/plugin/{系统插件}/setting/_logger
- 服务级：zkPath=/skynet/plugin/{系统插件}/action/{Action服务}/_logger
采用命名空间=日志级别
如：skynet.boot.core.xray=ERROR

 
#### 日志输出管道
 Kafka配置：
 zkPath=/skynet/setting/_properties
 
 - skynet.logging.kafka.enabled	= true
 - skynet.logging.kafka.targetTopic	= _ant_logger
 - skynet.logging.kafka.brokerList	= **************:9092
 
 写Kafka性能Metrics查看  
 
 ```   
	  http://{skynet.agent.ip}:{port}/metrics        
	 {
	    "skynet.log2kafka.cost.ms.min": 0,
	    "skynet.log2kafka.cost.ms.max": 34,
	    "skynet.log2kafka.cost.ms.avg.10": 0,
	    "skynet.log2kafka.cost.ms.avg.50": 1,
	    "skynet.log2kafka.cost.ms.avg.150": 1
	}
```

 
### Skynet 插件开发
 

开发步骤

1. 引入 Skynet起步依赖
2. 实现插件接口
3. 插件Action配置
4. 编译打包
5. 部署测试

#### Skynet起步依赖

目前skynet所依赖的包全部放在Maven仓库上面，配置仓库地址，则可以获得skynet全部依赖。

> pom.xml maven 依赖

```xml
<parent>
	<groupId>com.iflytek.skynet</groupId>
	<artifactId>skynet-boot-starter-parent</artifactId>
	<version>2.1.0.1003</version>		
</parent>

<properties>
		<mvn-repo>http://maven.iflytek.com:8081/nexus</mvn-repo>
</properties>

<repositories>
	<repository>
		<id>repo1-cache</id>
		<name>repo1-cache</name>
		<url>${mvn-repo}/content/groups/public</url>
		<snapshots>
			<enabled>false</enabled>
		</snapshots>
	</repository>
	<repository>
		<id>repo2-cache</id>
		<name>repo2-cache</name>
		<url>${mvn-repo}/content/repositories/RS-odeon-snapshots</url>
		<snapshots>
			<enabled>true</enabled>
		</snapshots>
	</repository>
	<repository>
		<id>repo3-cache</id>
		<name>repo3-cache</name>
		<url>${mvn-repo}/content/repositories/RS-odeon-releases</url>
		<snapshots>
			<enabled>false</enabled>
		</snapshots>
	</repository>
</repositories>

<dependencies>
	<dependency>
		<groupId>com.alibaba</groupId>
		<artifactId>fastjson</artifactId>
	</dependency> 
	<!-- ... -->	 
</dependencies>
<build>
	<plugins>
		<plugin>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-maven-plugin</artifactId>
			<executions>
				<execution>
					<goals>
						<goal>repackage</goal>
					</goals>
					<!-- <configuration> <classifier>with-dependencies</classifier> </configuration> -->
				</execution>
			</executions>
		</plugin>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-jar-plugin</artifactId>
			<configuration>
				<archive>
					<manifest>
						<addClasspath>true</addClasspath>
						<mainClass>skynet.boot.App</mainClass>
					</manifest>
					<manifestEntries>
						<project-name>${project.name}</project-name>
						<project-version>${project.version}</project-version>
						<build-number>${build.number}</build-number>
						<build-time>${maven.build.timestamp}</build-time>
					</manifestEntries>
				</archive>
			</configuration>
		</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptors>
						<descriptor>assembly.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>        
	</plugins>
</build>

```
上述示例代码可参考 skynet-boot-samples/skynet-sample-skynetboot

#### 实现插件接口

需要实现 skynet 插件唯一接口 IAntService，才可使用skynetboot加载，接口定义

```java
package skynet.boot.service;

import java.util.Map;
import skynet.boot.AppContext;

/**
 * AntService 接口契约
 */
public interface IAntService extends AutoCloseable {

	/**
	 * 服务名称
	 * 
	 * @return 服务名称
	 */
	String getSvcName();

	/**
	 * 运行服务
	 * 
	 * @param appContext
	 *            App上下文
	 * @param svcContext
	 *            SVC初始化参数
	 * @throws Exception
	 */
	void run(AppContext appContext, Map<String, Object> svcContext) throws Exception;

	/**
	 * 获取状态
	 * 
	 * @return 获取服务状态，用于汇报到Zk节点
	 */
	Map<String, Object> getSvcState() throws Exception;
}

```

实现类示例

```java
@SkynetAntService(SampleAppSvc.SVC_NAME)
public final class SampleAppSvc implements IAntService {

	private static final Logger LOGGER = LoggerFactory.getLogger(RestHost.class);

	public static final String SVC_NAME = "skynet.rest.sample";

	@Autowired(required = false)
	private OnlineMenu onlineMenu;

	@Override
	public String getSvcName() {
		return SVC_NAME;
	}

	/**
	 * 进行Action服务的初始化工作，如相关引擎加载等
	 *
	 * @param appContext
	 * @param svcContext
	 * @throws Exception
	 */
	@Override
	public void run(AppContext appContext, Map<String, Object> svcContext) throws Exception {
		LOGGER.info("skynet.action.service.context:{}", svcContext);

		LOGGER.info("汇报Sample示例导航菜单");

		// TODO: 业务初始化，相当于 服务 main函数的入口

		// 读取本地配置 汇报Menus
		if (onlineMenu != null) {
			onlineMenu.report("/static/sample.ui.menu.json");
		}
	}

	@Override
	public Map<String, Object> getSvcState() throws Exception {

		// 返回服务的状态， 可以通过

		Map<String, Object> status = new HashMap<>();
		return status;
	}

	@Override
	public void close() throws Exception {
		if (onlineMenu != null) {
			onlineMenu.close();
		}
	}
}
```

## maven build 配置


assembly.xml

```xml
<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/mavenassembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
	<id>${build.number}</id>
	<formats>
		<format>dir</format>
		<format>zip</format>
	</formats>
	<!-- 插件名称(系统名称) -->
	<baseDirectory>skynetboot</baseDirectory>
	<includeBaseDirectory>true</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>misc</directory>
			<outputDirectory>/</outputDirectory>
			<includes>
				<include>**/*</include>
			</includes>
		</fileSet>
		<fileSet><!-- 根据需要，有conf可以独立运行 --> 
			<outputDirectory>./conf</outputDirectory>
			<directory>target/classes</directory>
			<includes>
				<include>*.properties</include>
				<include>logback-spring.xml</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>./target</directory>
			<outputDirectory>./lib</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
			<excludes>
				<exclude>*javadoc.jar</exclude>
				<exclude>*sources.jar</exclude>
			</excludes>
		</fileSet>
	</fileSets>
</assembly>

```

## Action服务定义
  
