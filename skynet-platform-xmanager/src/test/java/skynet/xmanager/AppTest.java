package skynet.xmanager;

import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.jasypt.util.text.BasicTextEncryptor;

import java.io.IOException;
import java.util.regex.Pattern;

/**
 * Unit skynet.boot.xmanager.test for simple App.
 */
public class AppTest extends TestCase {
    /**
     * Create the skynet.boot.xmanager.test case
     *
     * @param testName name of the skynet.boot.xmanager.test case
     */
    public AppTest(String testName) {
        super(testName);
    }

    /**
     * @return the suite of tests being tested
     */
    public static Test suite() {
        return new TestSuite(AppTest.class);
    }

    /**
     * Rigourous Test :-)
     *
     * @throws IOException
     */
    public void testApp() {

        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        textEncryptor.setPassword("iflytek"); // 设置密码

        // BasicBinaryEncryptor basicBinaryEncryptor=new BasicBinaryEncryptor();
        // basicBinaryEncryptor.setPassword("admin");// 设置密码
        // basicBinaryEncryptor.decrypt(encryptedBinary);// 解密
        // basicBinaryEncryptor.encrypt(binary);// 加密
        // boolean is=
        Pattern.matches("^[0-9a-zA-Z-]+$", "tete-43543");

        String myEncryptedText = textEncryptor.encrypt("admin"); // 加密

        System.out.println(myEncryptedText);

        String plainText = textEncryptor.decrypt(myEncryptedText); // 解密

        System.out.println(plainText);

        assertTrue(true);
    }
}
