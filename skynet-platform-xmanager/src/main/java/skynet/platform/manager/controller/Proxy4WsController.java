package skynet.platform.manager.controller;

import com.alibaba.fastjson2.JSON;
import jakarta.websocket.*;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.websocket.HttpSessionConfigurator;
import skynet.platform.common.auth.AuthWsClient;
import skynet.platform.common.auth.WsMessageHandler;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * 代理 agent ws 接口
 *
 * <AUTHOR>
 */

@Slf4j
@Component
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@ServerEndpoint(value = Proxy4WsController.WS_PROXY_CONTEXT_PATH, configurator = HttpSessionConfigurator.class)
public class Proxy4WsController {
    public static final String WS_PROXY_CONTEXT_PATH = "/skynet/proxy/ws";
    private final AuthWsClient authWsClient;

    private final SkynetEncryption skynetEncryption;

    public Proxy4WsController(AuthWsClient authWsClient, SkynetEncryption skynetEncryption) {
        this.authWsClient = authWsClient;
        this.skynetEncryption = skynetEncryption;
    }

    @OnOpen
    public void onOpen(Session session) throws Exception {
        List<String> urlList = session.getRequestParameterMap().get("u");
        if (urlList == null || urlList.isEmpty() || StringUtils.isBlank(urlList.getFirst())) {
            session.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, "url is blank"));
            return;
        }
        String url = urlList.getFirst();
        log.debug("ws proxy on open. url={}", url);
        if (url.length() > 300) {
            url = skynetEncryption.decrypt(url);
            log.debug("decrypt url={}", url);
        }
        URI uri;
        try {
            uri = new URI(url);
            if (WS_PROXY_CONTEXT_PATH.equals(uri.getPath())) {
                log.info("url equals to ws proxy context path, return");
                session.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, "url equals to context path"));
                return;
            }
        } catch (URISyntaxException e) {
            log.error("ws proxy on open url to uri illegal", e);
            session.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, "url is invalid"));
            return;
        }
        WsMessageHandler wsMessageHandler = new WsMessageHandler() {
            @Override
            public void onOpen() {
                log.debug("Remote ws open.. url={}", uri);
            }

            @Override
            public void onMessage(String message) {
                log.debug("MessageHandler.onMessage message={}", message);
                try {
                    session.getBasicRemote().sendText(message);
                } catch (IOException e) {
                    log.error("sendText error={}", e.getMessage());
                }
            }

            @Override
            public void onClose(int code, String reason, boolean remote) {
                log.debug("MessageHandler.onClose code={}, reason={}, remote={}", code, reason, remote);
                try {
                    session.close();
                } catch (IOException e) {
                    log.error("onClose error={}", e.getMessage());
                }
            }

            @Override
            public void onError(Exception e) {
                log.error("MessageHandler.onError err={}", e.getMessage());
                try {
                    session.close();
                } catch (IOException e1) {
                    log.error("onError error={}", e1.getMessage());
                }
            }
        };

        try {
            authWsClient.connect(uri, wsMessageHandler);
        } catch (Exception e) {
            log.error("ws proxy on open err", e);
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("OnMessage. message={} sessionId={}", message, session.getId());
        try {
            authWsClient.send(message);
        } catch (Exception e) {
            log.error("onMessage send err={}", e.getMessage());
        }
    }

    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        if (log.isDebugEnabled()) {
            log.debug("OnClose. closeReason={} sessionId={}", JSON.toJSONString(closeReason), session.getId());
        }
        try {
            authWsClient.close();

        } catch (Exception e) {
            log.error("OnClose err={}", e.getMessage());
        }

    }

    @OnError
    public void onError(Session session, Throwable t) {
        if (log.isErrorEnabled()) {
            log.error("OnError. sessionId={}, t={}", session.getId(), ExceptionUtils.getStackTrace(t));
        }
        onClose(session, new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, t.getMessage()));
    }
}
