package skynet.platform.manager.controller;


import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.SkynetEncryption;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

/**
 * 代理 agent http 接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = Proxy4HttpController.HTTP_PROXY_CONTEXT_PATH, produces = {MediaType.APPLICATION_JSON_VALUE})
public class Proxy4HttpController {
    public static final String HTTP_PROXY_CONTEXT_PATH = "/skynet/proxy/http";
    private final RestTemplate authRestTemplate;
    private final SkynetEncryption skynetEncryption;

    public Proxy4HttpController(@Qualifier("authRestTemplate") RestTemplate authRestTemplate, SkynetEncryption skynetEncryption) {
        this.authRestTemplate = authRestTemplate;
        this.skynetEncryption = skynetEncryption;
    }

    /**
     * http代理 支持 文件下载
     * 参数示例 url=http://172.31.98.237:6230/skynet/agent/log/download/lb-v1111@ant
     *
     * @param response http response
     */
    @GetMapping
    public Object get(@RequestParam(name = "u") String url, HttpServletResponse response) throws Exception {
        return this.call(url, HttpMethod.GET, response);
    }

    @PostMapping
    public Object post(@RequestParam(name = "u") String url, HttpServletResponse response) throws Exception {
        return this.call(url, HttpMethod.POST, response);
    }

    @DeleteMapping
    public Object delete(@RequestParam(name = "u") String url, HttpServletResponse response) throws Exception {
        return this.call(url, HttpMethod.DELETE, response);
    }

    private ResponseEntity<?> call(String url, HttpMethod method, HttpServletResponse response) throws Exception {
        //优先使用 signAuth授权，然后使用 BaseAuth。（如 /prometheus 端点）
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.debug("encrypt url={}", url);
        if (url.length() > 300) {
            url = skynetEncryption.decrypt(url);
            log.debug("decrypt url={}", url);
        }

        //response.resetBuffer();
        try {
            return call(authRestTemplate, url, method, response);
        } catch (HttpClientErrorException e) {
            try {
                if (e.getStatusCode().value() == HttpStatus.UNAUTHORIZED.value()) {
                    log.debug("Swatch BaseAuth Call...");
                    return call(authRestTemplate, url, method, response);
                }
            } catch (HttpClientErrorException ex) {
                e = ex;
            }
            response.setStatus(e.getStatusCode().value());
            response.getOutputStream().write(e.getResponseBodyAsByteArray());
            response.getOutputStream().flush();
            return new ResponseEntity<>(String.format("<h1>Proxy request error.[URL=%s]</h1><br/>%s", url, e.getResponseBodyAsString()), e.getStatusCode());
        } catch (Exception e) {
            log.error("proxy call url={} err:{}", url, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            log.debug("proxy call url={} cost={}", url, stopWatch);
        }
    }

    /***
     * 简单的 代理，参数主要是通过URL
     * @param url
     * @param method
     * @param response
     * @return
     * @throws IOException
     */
    private ResponseEntity<?> call(RestTemplate restTemplate, String url, HttpMethod method, HttpServletResponse response) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        URI uri;
        try {
            uri = new URI(url);
        } catch (URISyntaxException e) {
            log.error("url parameter is not illegal, url={}", url);
            throw new ApiRequestException((ApiRequestErrorCode.PARAM_ILLEGAL));
        }

        if (HTTP_PROXY_CONTEXT_PATH.equalsIgnoreCase(uri.getPath())) {
            // 排除自身 否则一直循环
            log.warn("url equals to http proxy context path, return");
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }

        restTemplate.execute(uri, method, null, clientHttpResponse -> {

            try {
                for (Map.Entry<String, List<String>> item : clientHttpResponse.getHeaders().entrySet()) {
                    if (!item.getValue().isEmpty()) {
                        response.addHeader(item.getKey(), item.getValue().getFirst());
                    }
                }
            } catch (Exception e) {
                log.error("add header error:{}", e.getMessage());
            }

            try (BufferedInputStream in = new BufferedInputStream(clientHttpResponse.getBody())) {
                try (OutputStream out = response.getOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead = -1;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                    out.flush();
                }
            }
            return true;
        });
        log.debug("proxy call url={} cost:{}", url, stopWatch);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

