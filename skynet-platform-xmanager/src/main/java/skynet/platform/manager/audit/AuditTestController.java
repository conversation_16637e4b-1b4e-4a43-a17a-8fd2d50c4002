package skynet.platform.manager.audit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.common.domain.RestResponse;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.ArrayList;
import java.util.List;


@RestController
@RequestMapping(value = "/audit")
@ConditionalOnProperty(value = "skynet.audit.test.enabled")
public class AuditTestController {

    @Autowired
    private AuditLogTest auditLogTest;

    @GetMapping(value = "/test")
    @AuditLog(module = "TEST", operation = "操作", message = "正常测试")
    public RestResponse<String> test1() throws Exception {

        auditLogTest.test1();
        try {
            auditLogTest.test2();
        } catch (Exception e) {
        }

        auditLogTest.test30(null);
        AuditLogTest.LoginInfo user = new AuditLogTest.LoginInfo();
        auditLogTest.test31(user);
        user.setUsername("lyhu");
        auditLogTest.test32(user);

        auditLogTest.test40(null);
        auditLogTest.test41("127.0.0.1");

        List<AuditLogTest.LoginInfo> userList = new ArrayList<>();
        userList.add(user);
        auditLogTest.test50(null);
        auditLogTest.test51(userList);

        return new RestResponse<>();
    }
}
