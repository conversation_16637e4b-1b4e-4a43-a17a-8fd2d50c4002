package skynet.platform.manager.admin.deploy;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import skynet.boot.common.FileZipUtil;
import skynet.platform.common.shell.Shell;
import skynet.platform.feign.model.AgentVersionDto;
import skynet.platform.manager.exception.DeployException;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * skynet 包部署
 *
 * <AUTHOR>
 */
@Slf4j
public class SkynetPackageDeployer implements AutoCloseable {

    private static final SimpleDateFormat DATA_FORMAT = new SimpleDateFormat("yyyyMMdd-HHmmss");
    private final String ip;
    private final Shell shell;
    private final String skynetHome;

    private static final String[] SKYNET_PACKAGE_DIR_LIST = "bin;doc;conf;lib;version.txt".split(";");
    //排除 runtime 目录，因为 会影响正在运行的 依赖此 jdk的 服务。 by lyhu 2021年09月14日10:28:38
    private static final String[] SKYNET_UPGRADE_DIR_LIST = "bin;doc;conf;lib;version.txt".split(";");

    public SkynetPackageDeployer(String ip, int port, String user, String pwd, String skynetHome, int timeout) throws Exception {
        this.shell = Shell.build(ip, port, user, pwd, timeout);
        this.ip = ip;
        this.skynetHome = skynetHome;
    }

    public static synchronized File buildVersionFile(String skynetHome) throws Exception {
        return new File(skynetHome, "version.txt");
    }

    /**
     * 构建 skynet部署包，
     *
     * <pre>
     * </pre>
     *
     * @param skynetHome
     * @return 签名文件名
     * @throws Exception
     */
    private static synchronized File buildLocalDeployPackage(String skynetHome) throws Exception {
        File skynetPackageFile = new File(skynetHome, "skynet.zip");
        log.debug("buildSkynetPackage {}..", skynetPackageFile);

        // 本地文件处理
        // 验证文件不存在 删除存在的文件，重新压缩
        if (skynetPackageFile.exists()) {
            log.info("the local deployment package already exists.");
        } else {
            log.info("The local deployment package does not exist, start building{} ...", skynetPackageFile.getAbsolutePath());

            List<String> targetDirArray = new ArrayList<>();

            for (String dir : SKYNET_PACKAGE_DIR_LIST) {
                File file = new File(skynetHome, dir);
                if (file.exists()) {
                    log.info("Zip add {} file or directory", file.getName());
                    targetDirArray.add(file.getAbsolutePath());
                } else {
                    throw new DeployException(String.format("The [%s] not exist", file));
                }
            }

            //排除K8s目录
            File file = new File(skynetHome, "runtime");
            File tmpRuntime = null;
            try {
                tmpRuntime = createTmpRuntime(file.getAbsolutePath());
                if (file.exists()) {
                    log.info("Zip add {} file or directory", file.getName());
                    targetDirArray.add(tmpRuntime.getAbsolutePath());
                } else {
                    throw new DeployException(String.format("The [%s] not exist", file));
                }

                log.info("Zip {} begin...", skynetPackageFile);
                FileZipUtil.zip(targetDirArray.toArray(new String[0]), skynetPackageFile.getAbsolutePath());
                log.info("Build {} OK.", skynetPackageFile);

            } finally {
                if (tmpRuntime != null) {
                    FileUtils.forceDelete(tmpRuntime.getParentFile());
                }
            }
        }
        return skynetPackageFile;
    }

    private static File createTmpRuntime(String sourceDirPath) throws IOException {
        // 定义源目录和临时目录
        String tempDirPath = System.getProperty("java.io.tmpdir") + "/SkynetPackageDeployer/runtime";
        try {
            // 创建临时目录
            File tempDir = new File(tempDirPath);
            if (tempDir.exists()) {
                FileUtils.forceDelete(tempDir);
            }
            log.info("crate temporary runtime directory={}", tempDir);
            File[] files = getFiles(sourceDirPath, tempDir);

            for (File file : files) {
                if (file.isDirectory() && file.getName().equals("k8s")) {
                    continue;
                }

                Path sourcePath = file.toPath();
                Path targetPath = Paths.get(tempDirPath, file.getName());

                if (file.isDirectory()) {
                    copyDirectory(sourcePath, targetPath);
                } else {
                    // 复制文件
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                }
            }

            System.out.println("Files and directories copied to temporary directory: " + tempDirPath);
            return tempDir;
        } catch (IOException e) {
            log.error("createTmpRuntime error.", e);
            throw e;
        }
    }

    private static File @NotNull [] getFiles(String sourceDirPath, File tempDir) throws IOException {
        if (!tempDir.exists() && !tempDir.mkdirs()) {
            throw new IOException("Failed to create temporary directory: " + tempDir);
        }

        // 获取源目录的第一层子目录和文件
        File sourceDir = new File(sourceDirPath);
        if (!sourceDir.isDirectory()) {
            throw new IOException("Source path is not a directory: " + sourceDirPath);
        }

        File[] files = sourceDir.listFiles();
        if (files == null) {
            throw new IOException("Failed to list files in source directory: " + sourceDirPath);
        }
        return files;
    }


    /**
     * 复制目录的第一层内容到目标路径
     */
    private static void copyDirectory(Path sourceDir, Path targetDir) throws IOException {
        log.info("copyDirectory {} to {} ...", sourceDir, targetDir);
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }
        try {
            // 使用 Files.walkFileTree 递归复制
            Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
                @Override
                public @NotNull FileVisitResult preVisitDirectory(@NotNull Path dir, @NotNull BasicFileAttributes attrs) throws IOException {
                    // 在目标位置创建目录
                    Path targetPath = targetDir.resolve(sourceDir.relativize(dir));
                    if (!Files.exists(targetPath)) {
                        Files.createDirectories(targetPath);
                    }
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public @NotNull FileVisitResult visitFile(@NotNull Path file, @NotNull BasicFileAttributes attrs) throws IOException {
                    // 复制文件
                    Files.copy(file, targetDir.resolve(sourceDir.relativize(file)), StandardCopyOption.REPLACE_EXISTING);
                    return FileVisitResult.CONTINUE;
                }
            });
            log.info("copyDirectory {} to {} end.", sourceDir, targetDir);
        } catch (IOException e) {
            log.error(String.format("copyDirectory sourceDir=%s, targetDir=%s", sourceDir, targetDir), e);
            throw e;
        }
    }

    /**
     * 部署
     * <p>
     * 如果已经存在，将备份以前的版本
     *
     * @throws Exception
     */
    public void deploy() throws Exception {
        String targetDir = skynetHome;
        log.info("start deploying skynet, ip={} targetDir={} ...", ip, targetDir);

        //构建部署包
        File skynetPackageFile = buildLocalDeployPackage(targetDir);

        //移动
        List<String> cmdList = new ArrayList<>();
        if (shell.isDirExist(targetDir)) {
            //先尝试 停止已经在运行的agent服务，再备份
            log.info("[{}]try to stop the running agent..", ip);

            cmdList.add(String.format("cd %s/bin/", skynetHome));
            cmdList.add("./ant-xagent.sh kill");

            String bakFile = String.format(" %s_%s", targetDir, DATA_FORMAT.format(new Date()));
            log.info("[{}]already exist skynet，back up to={}", ip, bakFile);

            cmdList.add(String.format("mkdir -p %s", bakFile));
            for (String dir : SKYNET_UPGRADE_DIR_LIST) {
                cmdList.add(String.format("mv %s/%s %s", targetDir, dir, bakFile));
            }
        } else {
            log.info("[{}]create folder：{},", ip, targetDir);
            cmdList.add(String.format("mkdir -p %s", targetDir));
        }

        for (String cmd : cmdList) {
            String response = shell.execCmd(cmd);
            log.info("{}", cmd);
            log.debug("Response：{}", response);
        }

        StopWatch begin = StopWatch.createStarted();
        shell.copy(skynetPackageFile, targetDir);
        log.info("upload successfully (cost time={}). start decompressing...", begin);

        String response = shell.execCmd("cd " + targetDir, "unzip -n skynet.zip", "chmod -R 777 bin/*.sh");
        log.debug("{}", response);
        log.info("Skynet ackage distribution deployment is complete.");
    }

    public void installDocker(String dPath) throws Exception {
        log.info("install the docker environment begin ...");
        log.info("start the docker environment and execute\t./runtime/docker-install.sh -i");
        String response = shell.execCmd("cd " + dPath, "chmod -R 777 runtime/*.sh", "./runtime/docker-install.sh -i");
        log.info(response);
        log.info("install the docker environment end.");
    }

    public AgentVersionDto getVersion() throws Exception {
        log.debug("Fetch the remote server version file.[ip={}]", ip);
        File remoteFilePath = buildVersionFile(skynetHome);

        String versionFilePath = remoteFilePath.getAbsolutePath();
        String content = null;
        if (shell.isFileExist(versionFilePath)) {
            content = shell.readFile(versionFilePath);
            log.debug("The remote server version info={}", content);
            return JSON.parseObject(JSON.parseObject(content).getString("ant-xagent"), AgentVersionDto.class);
        } else {
            log.info("The remote server version file={} not exist", versionFilePath);
            return null;
        }
    }

    public boolean isLocal() throws IOException {
        log.debug("Fetch the remote server temp file.[ip={}]", ip);

        File tempFile = new File(skynetHome, "tmp");
        tempFile = new File(tempFile, "agent_check_" + UUID.randomUUID());
        boolean local = true;
        try {
            FileUtils.forceMkdir(tempFile);
            local = shell.isFileExist(tempFile.getAbsolutePath());
            FileUtils.forceDelete(tempFile);
        } catch (Exception e) {
            if (tempFile.exists()) {
                FileUtils.forceDelete(tempFile);
            }
        }
        return local;
    }

    @Override
    public void close() throws Exception {
        if (shell != null) {
            shell.close();
        }
    }
}
