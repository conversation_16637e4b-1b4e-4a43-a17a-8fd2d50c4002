package skynet.platform.manager.admin.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.common.domain.AntActionRegist;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.ActionDeploymentUpdateDto;
import skynet.platform.manager.admin.service.V3ActionDeployService;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@Service

public class V3ActionDeployServiceImpl extends V3BaseServiceImpl implements V3ActionDeployService {

    private final IAntConfigService antConfigService;

    public V3ActionDeployServiceImpl(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    /**
     * 移除指定的应用系统中的所有服务
     *
     * @param plugin
     */
    @Override
    public void removeDeployPlugin(String plugin) {
        List<String> ipList = this.antConfigService.getServers().stream().map(AntServerParam::getIp).toList();
        for (String ip : ipList) {
            removeDeployPlugin(ip, plugin);
        }
    }

    /**
     * 移除指定的应用系统中的所有服务
     *
     * @param ip     ip
     * @param plugin plugin
     */
    @Override
    @AuditLog(module = "服务分配", operation = "移除分配", message = "IP=#{#ip};Plugin=#{#plugin}")
    public void removeDeployPlugin(String ip, String plugin) {
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AntException(String.format("server [%s] is not registered", ip));
        }
        boolean isRemoved = false;
        for (int index = antServerParam.getActions().size() - 1; index >= 0; index--) {
            AntActionRegist action = antServerParam.getActions().get(index);
            if (action.getPlugin().equalsIgnoreCase(plugin)) {
                antServerParam.getActions().remove(index);
                isRemoved = true;
            }
        }
        if (isRemoved) {
            this.antConfigService.setServerParam(antServerParam);
        }
    }

    /**
     * 移除 集群上所有已经部署的 服务
     *
     * @param actionPoint
     */
    @Override
    @AuditLog(module = "服务分配", operation = "移除服务", message = "Remove All Form Cluster ActionPoint=#{#actionPoint}")
    public void removeDeployAction(String actionPoint) {
        log.info("remove the assigned service:{}", actionPoint);
        List<String> ipList = this.antConfigService.getServers().stream().map(AntServerParam::getIp).toList();
        for (String ip : ipList) {
            removeDeployAction(ip, actionPoint);
        }
    }

    /**
     * 移除指定服务上的 已经分配的服务
     *
     * @param ip
     * @param actionPoint
     */
    @Override
    @AuditLog(module = "服务分配", operation = "移除服务", message = "Remove ActionPoint=#{#actionPoint};From=#{#ip}")
    public void removeDeployAction(String ip, String actionPoint) {
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new ApiRequestException(ApiRequestErrorCode.AGENT_NOT_REGISTER);
        }
        for (int index = antServerParam.getActions().size() - 1; index >= 0; index--) {
            AntActionRegist action = antServerParam.getActions().get(index);
            if (action.getPoint().equalsIgnoreCase(actionPoint)) {
                antServerParam.getActions().remove(index);
                this.antConfigService.setServerParam(antServerParam);
                break;
            }
        }
    }

    /**
     * 减少 指定服务上的 已经分配的服务(减少实例数)
     *
     * @param ip
     * @param actionPoint
     */
    @Override
    @AuditLog(module = "服务分配", operation = "移除服务", message = "Reduce ActionPoint=#{#actionPoint};From=#{#ip}")
    public void reduceDeployAction(String ip, String actionPoint) {
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new ApiRequestException(ApiRequestErrorCode.AGENT_NOT_REGISTER);
        }

        // 更新已经存在的服务 如果存在，直接-1
        for (int index = antServerParam.getActions().size() - 1; index >= 0; index--) {
            AntActionRegist action = antServerParam.getActions().get(index);
            if (action.getPoint().equalsIgnoreCase(actionPoint)) {
                if (action.getNum() == 1) {
                    antServerParam.getActions().remove(index);
                } else {
                    action.setNum(action.getNum() - 1);
                }
                this.antConfigService.setServerParam(antServerParam);
                break;
            }
        }
    }

    /**
     * 增加 服务分配
     *
     * @param ip
     * @param adu
     */
    @Override
    public void incrementDeployAction(String ip, ActionDeploymentUpdateDto adu) {

        int num = StringUtils.isNotBlank(adu.getNum()) ? Integer.parseInt(adu.getNum()) : 1;
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AntException(String.format("server [%s] is not registered", ip));
        }
        boolean notExist = true;
        // 更新已经存在的服务 如果数量存在，直接+num
        for (AntActionRegist action : antServerParam.getActions()) {
            if (action.getPoint().equalsIgnoreCase(adu.getActionPoint())) {
                action.setNum(action.getNum() + num);
                action.setReplicas(adu.getReplicas());
                action.setNodeSelector(adu.getNodeSelector());
                if (action.getNum() > 16) {
                    throw new AntException(String.format("server [%s] number is more than 16", action.getPoint()));
                }
                notExist = false;
                break;
            }
        }
        // 从服务定义中获取服务，再分配
        if (notExist) {
            ActionNameContract actionNameContract = new ActionNameContract(adu.getActionPoint());
            NodeDescription nodeDescription = this.antConfigService.getAction(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
            if (nodeDescription == null) {
                throw new AntException(String.format("server [%s] does not exist", adu.getActionPoint()));
            }
            AntActionRegist antActionRegist = new AntActionRegist();
            antActionRegist.setIndex(antServerParam.getActions().size());
            antActionRegist.setCode(actionNameContract.getActionCode());
            antActionRegist.setPlugin(actionNameContract.getPluginCode());
            antActionRegist.setName(nodeDescription.getName());
            antActionRegist.setNum(num);
            antActionRegist.setReplicas(adu.getReplicas());
            antActionRegist.setNodeSelector(adu.getNodeSelector());
            antServerParam.getActions().add(antActionRegist);
        }
        this.antConfigService.setServerParam(antServerParam);
    }
}
