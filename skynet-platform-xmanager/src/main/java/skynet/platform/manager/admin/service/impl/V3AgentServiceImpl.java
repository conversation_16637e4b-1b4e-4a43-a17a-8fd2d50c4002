package skynet.platform.manager.admin.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.concurrent.ParallelService;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.AgentConfigBlocksDto;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.AgentType;
import skynet.platform.feign.model.AgentVersionDto;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.admin.service.k8s.K8sAgentService;
import skynet.platform.manager.exception.AgentNotRegisterException;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> by jianwu6 on 2020/7/28 15:09
 */
@Slf4j
@Service

public class V3AgentServiceImpl extends V3BaseServiceImpl implements V3AgentService {

    private static final String START = "start";
    private static final String STOP = "stop";

    private static final Comparator<AgentDto> SERVER_COMPARE = Comparator.comparingInt(AgentDto::getIndex);
    private final V3ServerAgentService v3ServerAgentService;
    private final K8sAgentService k8sAgentService;

    @Autowired
    public V3AgentServiceImpl(V3ServerAgentService v3ServerAgentService, K8sAgentService k8sAgentService) {
        this.v3ServerAgentService = v3ServerAgentService;
        this.k8sAgentService = k8sAgentService;
    }

    /**
     * 获取 agent 列表
     */
    @Override
    public List<AgentDto> getAgents() throws Exception {

        // 0. 获取 agent 服务定义
        List<AntServerParam> servers = this.antConfigService.getServers();
        if (CollectionUtils.isEmpty(servers)) {
            return new ArrayList<>(0);
        }
        // 1. 获取 agent 在线状态
        Map<String, AgentVersionDto> onlineAgentVersionMap = getOnlineAgentVersion();

        // 2. 并发查询 agent 详细信息
        List<AgentDto> agentDtoList = new ArrayList<>(servers.size());
        int nThreads = Math.min(16, servers.size());
        log.debug("Parallel get agent info. threads={}. begin ...", nThreads);
        try (ParallelService<AntServerParam, AgentDto> parallelService = new ParallelService<>(nThreads)) {
            parallelService.submit(servers, antServerParam -> {
                try {
                    return getAgentService(antServerParam.getType()).getAgent(antServerParam, onlineAgentVersionMap);
                } catch (Exception e) {
                    log.error("get agent info Error", e);
                    throw e;
                }
            });
            agentDtoList = parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
            agentDtoList.sort(SERVER_COMPARE);
            log.debug("Parallel get agent info. threads={}. end.", nThreads);
        }
        // 增加依次根据标签、INDEX进行排、节点类型、IP
//        return agentDtoList.stream().sorted(Comparator.comparing(AgentDto::getSort)
//                        .thenComparing(AgentDto::getAgentType)
//                        .thenComparing(AgentDto::getIp)
//                ).toList();
        return agentDtoList.stream().sorted(Comparator.comparing(AgentDto::getIndex)
                .thenComparing(AgentDto::getAgentType)
                .thenComparing(AgentDto::getIp)
        ).toList();
    }

    /**
     * 获取 agent 详情
     */
    @Override
    public AgentDto getAgent(String ip) {
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AgentNotRegisterException(ip);
        }
        Map<String, AgentVersionDto> onlineAgentVersionMap = getOnlineAgentVersion();
        return getAgentService(antServerParam.getType()).getAgent(antServerParam, onlineAgentVersionMap);
    }

    /**
     * 新增 agent
     */
    @Override
    public void create(AgentDto dto) throws Exception {
        getAgentService(dto.getAgentType()).create(dto);
    }

    /**
     * 修改 agent
     */
    @Override
    public void update(String ip, AgentDto dto) throws Exception {
        getAgentService(dto.getAgentType()).update(ip, dto);
    }

    /**
     * 修改配置块信息
     *
     * @param ip
     * @param dto
     * @throws Exception
     */
    @Override
    public void update(String ip, AgentConfigBlocksDto dto) throws Exception {
        getAgentService(dto.getAgentType()).update(ip, dto);
    }

    /**
     * 删除 agent
     */
    @Override
    public void delete(String ip, boolean withStopAction) throws Exception {
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AgentNotRegisterException(ip);
        }
        getAgentService(antServerParam.getType()).delete(antServerParam, withStopAction);
    }

    /**
     * 测试 agent 是否可以连接
     */
    @Override
    public void testAgentConnection(AgentDto dto) throws Exception {
        getAgentService(dto.getAgentType()).testAgentConnection(dto);
    }

    /**
     * 部署 agent
     */
    @Override
    public String deployAgent(String ip, boolean dockerEnabled, boolean isForce) throws Exception {
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AgentNotRegisterException(ip);
        }
        return getAgentService(antServerParam.getType()).deployAgent(ip, dockerEnabled, isForce);
    }

    @Override
    public void status(String ip, String operate, boolean stopAction) throws Exception {
        if (StringUtils.isBlank(operate)) {
            log.error("operate is blank");
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AgentNotRegisterException(ip);
        }
        if (operate.equals(START)) {
            getAgentService(antServerParam.getType()).startAgent(antServerParam);
        } else if (operate.equals(STOP)) {
            getAgentService(antServerParam.getType()).stopAgent(antServerParam, stopAction);
        } else {
            log.error("action is illegal");
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }
    }

    /**
     * 获取在线 agent 版本信息
     */
    private Map<String, AgentVersionDto> getOnlineAgentVersion() {
        Map<String, AgentVersionDto> agentVersionDtoMap = new HashMap<>(0);
        Map<String, String> onlineActionNodes = this.antConfigService.getOnlineActionNodes(AppBootEnvironment.AGENT_ACTION_POINT, null);
        onlineActionNodes.forEach((k, v) -> {
            if (!k.startsWith("_")) {
                try {
                    JSONObject jsonObject = JSON.parseObject(v);
                    AgentVersionDto metadata = JSON.parseObject(jsonObject.getString("metadata"), AgentVersionDto.class);
                    agentVersionDtoMap.put(jsonObject.getString("ip"), metadata);
                } catch (Exception e) {
                }
            }
        });
        return agentVersionDtoMap;
    }

    /**
     * 根据 agentType 获取相应的 service
     */
    private V3BaseAgentService getAgentService(String agentType) {
        return AgentType.KUBERNETES.equals(agentType) ? this.k8sAgentService : this.v3ServerAgentService;
    }
}