package skynet.platform.manager.admin.deploy;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.security.client.BaseAuthRestTemplateBuilder;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.common.domain.ServerLoginParam;
import skynet.platform.feign.model.AgentVersionDto;
import skynet.platform.manager.config.ManagerProperties;

/**
 * 分发客户端
 */
@Slf4j
public class KubernetesDeployClient extends DeployClient implements AutoCloseable {

    private final KubernetesPackageDeployer kubernetesPackageDeployer;

    public KubernetesDeployClient(ServerLoginParam serverLoginParam, String skynetHome, BaseAuthRestTemplateBuilder baseAuthRestTemplateBuilder, ManagerEncryptor managerEncryptor, ManagerProperties managerProperties) throws Exception {
        super(serverLoginParam.getIp(), skynetHome);
        log.debug("new KubernetesDeployClient ip={};", serverLoginParam.getIp());
        this.kubernetesPackageDeployer = new KubernetesPackageDeployer(serverLoginParam, skynetHome, baseAuthRestTemplateBuilder, managerEncryptor, managerProperties);
    }

    /**
     * 判断是否为本机
     */
    @Override
    protected boolean isLocal() throws Exception {
        return false;
    }

    /**
     * 部署 agent
     */
    @Override
    protected void deployAgent() throws Exception {
        String localVersion = getLocalVersionStr();
        kubernetesPackageDeployer.deploy(localVersion);
    }

    /**
     * 安装 Docker
     */
    @Override
    protected void installDocker() throws Exception {

    }

    /**
     * 获取远程 agent 版本信息
     */
    @Override
    public AgentVersionDto getRemoteVersion() throws Exception {
        String versionStr = getRemoteVersionStr();
        if (StringUtils.isBlank(versionStr)) {
            return null;
        }
        // 剥除版本号中的平台信息，版本号格式应该为：projectVersion-buildSid
        versionStr = stripPlatform(versionStr);
        AgentVersionDto dto = new AgentVersionDto();
        int index = versionStr.lastIndexOf("-");
        if (index >= 0) {
            dto.setProjectVersion(versionStr.substring(0, index));
            dto.setBuildSid(versionStr.substring(index + 1));
        } else {
            dto.setProjectVersion(versionStr);
        }
        return dto;
    }

    /**
     * 剥除镜像版本中可能带有的平台信息，如镜像版本为 3.4.4-SNAPSHOT-20221216-linux-amd64 剥除后返回 3.4.4-SNAPSHOT-20221216
     *
     * @param versionStr
     * @return
     */
    private String stripPlatform(String versionStr) {
        String[] osList = new String[]{"windows", "linux"};
        for (String os : osList) {
            String separator = String.format("-%s-", os);
            int index = versionStr.indexOf(separator);
            if (index > 0) {
                return versionStr.substring(0, index);
            }
        }
        return versionStr;
    }

    /**
     * 获取远程 agent 版本字符串，格式为 projectVersion-buildSid
     */
    @Override
    public String getRemoteVersionStr() throws Exception {
        return kubernetesPackageDeployer.getVersion();
    }

    @Override
    public void close() throws Exception {
        log.debug("Close KubernetesDeployClient");
        if (kubernetesPackageDeployer != null) {
            kubernetesPackageDeployer.close();
        }
    }
}
