package skynet.platform.manager.admin.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import skynet.boot.common.concurrent.ParallelService;
import skynet.platform.common.domain.*;
import skynet.platform.common.repository.config.setting.SkynetSettingManager;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.AntActionLabel;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ActionAlreadyExistException;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.ActionDefinitionDto;
import skynet.platform.feign.model.BootType;
import skynet.platform.feign.model.SwitchLabelValueDto;
import skynet.platform.manager.admin.service.V3ActionDefinitionService;
import skynet.platform.manager.admin.service.V3ActionDeployService;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Slf4j
@Service

public class V3ActionDefinitionServiceImpl extends V3BaseServiceImpl implements V3ActionDefinitionService {

    private final SkynetSettingManager skynetSettingManager;
    private final V3ActionDeployService actionDeployService;

    public V3ActionDefinitionServiceImpl(SkynetSettingManager skynetSettingManager,
                                         V3ActionDeployService actionDeployService) {
        this.skynetSettingManager = skynetSettingManager;
        this.actionDeployService = actionDeployService;
    }


    private List<ActionDefinitionDto> list(String pluginCode) throws Exception {

        List<ActionNameContract> actionNameContracts = new CopyOnWriteArrayList<>();
        Map<String, NodeDescription> pluginCode2Name = new ConcurrentHashMap<>();

        if (StringUtils.isBlank(pluginCode)) {
            List<NodeDescription> plugins = this.antConfigService.getPlugins();
            if (plugins != null) {
                //并发获取
                plugins.parallelStream().forEach(plugin -> {
                    pluginCode2Name.put(plugin.getCode(), plugin);
                    List<NodeDescription> actions = this.antConfigService.getActions(plugin.getCode());
                    if (actions != null) {
                        for (NodeDescription action : actions) {
                            actionNameContracts.add(new ActionNameContract(plugin.getCode(), action.getCode()));
                        }
                    }
                });
            }
        } else {
            NodeDescription plugin = this.antConfigService.getPlugin(pluginCode);
            if (plugin != null) {
                pluginCode2Name.put(plugin.getCode(), plugin);
            }
            List<NodeDescription> actions = this.antConfigService.getActions(pluginCode);
            if (actions != null) {
                for (NodeDescription action : actions) {
                    actionNameContracts.add(new ActionNameContract(pluginCode, action.getCode()));
                }
            }
        }

        if (CollectionUtils.isEmpty(actionNameContracts)) {
            return new ArrayList<>(0);
        }

        List<ActionDefinitionDto> ret = new ArrayList<>(0);
        // 最大为CPU核数
        int nThreads = Math.min(actionNameContracts.size(), 16);
        try (ParallelService<ActionNameContract, ActionDefinitionDto> parallelService = new ParallelService<>(nThreads)) {
            parallelService.submit(actionNameContracts, actionNameContract ->
                    getActionDefinitionDto(actionNameContract.getPluginCode(), actionNameContract.getActionCode()));

            ret = parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
        }

        for (ActionDefinitionDto dtoItem : ret) {
            NodeDescription plugin = pluginCode2Name.get(dtoItem.getPluginCode());
            dtoItem.setPluginName(plugin.getName());
            //同时设置上级的Index 放大，达到plugin优先以及整体有序
            dtoItem.setIndex(plugin.getIndex() * 1000 + dtoItem.getIndex());
        }
        ret.sort(Comparator.comparingInt(ActionDefinitionDto::getIndex));
        return ret;
    }

    /**
     * 通过
     *
     * @param pluginCode      为空，将是所有服务定义
     * @param actionLabelCode 功能标签编码，不为空，将是所有的服务定义
     * @return
     * @throws Exception
     */
    @Override
    public List<ActionDefinitionDto> list(String pluginCode, String actionLabelCode) throws Exception {
        List<ActionDefinitionDto> objList = list(pluginCode);

        if (org.springframework.util.StringUtils.hasText(actionLabelCode)) {
            List<ActionDefinitionDto> resultList =
                    new ArrayList<>((objList.size() > 40) ?
                            objList.parallelStream().filter(x ->
                                    x.getSwitchLabels().stream().anyMatch(y -> actionLabelCode.equalsIgnoreCase(y.getCode()) && y.isValue())
                            ).toList()
                            :
                            objList.stream().filter(x ->
                                    x.getSwitchLabels().stream().anyMatch(y -> actionLabelCode.equalsIgnoreCase(y.getCode()) && y.isValue())
                            ).toList());

            resultList.sort(Comparator.comparingInt(ActionDefinitionDto::getIndex));
            objList = resultList;
        }

        //获取当前已经分配的服务列表，设置到  objList 中，让前端有明显的标记。
        List<String> deployedActionList = new ArrayList<>();
        // 获取所有服务器节点信息
        List<AntServerParam> serverList = this.antConfigService.getServers();
        serverList.forEach(server -> {
            server.getActions().stream().filter(x -> x.getPlugin().equalsIgnoreCase(pluginCode)).forEach(action -> {
                deployedActionList.add(action.getPoint());
            });
        });
        objList.forEach(actionDefinitionDto -> {
            actionDefinitionDto.setDeployed(deployedActionList.contains(actionDefinitionDto.getActionPoint()));
        });

        return objList;
    }


    @Override
    public ActionDefinitionDto get(String actionPoint) throws IOException {
        ActionNameContract contract = new ActionNameContract(actionPoint);
        NodeDescription plugin = this.antConfigService.getPlugin(contract.getPluginCode());
        ActionDefinitionDto ret = getActionDefinitionDto(contract.getPluginCode(), contract.getActionCode());
        ret.setPluginName(plugin.getName());
        return ret;
    }

    @Override
    @AuditLog(module = "服务定义", operation = "创建服务", message = "Detail=#{#dto}")
    public void create(ActionDefinitionDto dto) {
        //校验action是否存在，存在报错
        NodeDescription nodeDescription = this.antConfigService.getAction(dto.getPluginCode(), dto.getActionCode());
        if (nodeDescription != null) {
            throw new ActionAlreadyExistException(dto.getActionPoint());
        }
        saveActionDefinitionDto(dto);
    }

    @Override
    @AuditLog(module = "服务定义", operation = "删除服务", message = "ActionPoint=#{#actionPoint}")
    public void delete(String actionPoint) {
        ActionNameContract contract = new ActionNameContract(actionPoint);
        checkAction(contract.getPluginCode(), contract.getActionCode());
        // 同时删除已经分配的服务
        String actionNodePath = this.antConfigService.getActionPath(contract.getPluginCode(), contract.getActionCode());
        this.antConfigService.delData(actionNodePath);
        actionDeployService.removeDeployAction(actionPoint);
    }

    @Override
    @AuditLog(module = "服务定义", operation = "更新服务", message = "ActionPoint=#{#actionPoint};Detail=#{#dto}")
    public void update(String actionPoint, ActionDefinitionDto dto) {
        //校验action是否存在，不存在报错
        ActionNameContract contract = new ActionNameContract(actionPoint);
        checkAction(contract.getPluginCode(), contract.getActionCode());

        saveActionDefinitionDto(dto);
    }

    @Override
    public List<NodeDescription> fetchActionDescList(List<String> actionPointList) {
        log.debug("fetchActionDescList={}", actionPointList);
        Assert.notNull(actionPointList, "actionPointList is null");

        List<NodeDescription> objList = new ArrayList<>(actionPointList.size());
        for (String actionPoint : actionPointList) {
            NodeDescription nodeDescription = antConfigService.getAction(actionPoint);
            if (nodeDescription != null) {
                nodeDescription.setCode(actionPoint);
                objList.add(nodeDescription);
            }
        }
        log.debug("fetchActionDescList={}", objList);
        return objList;
    }

    /**
     * 归档服务定义
     *
     * @param actionPoint
     */
    @Override
    public void archive(String actionPoint) throws IOException {
        log.debug("archive={}", actionPoint);
        ActionDefinitionDto dto = get(actionPoint);
        dto.setArchived(true);
        saveActionDefinitionDto(dto);
        //让用户主动  归档
//        //查找依赖的文件，并且移动到 archive 目录
//        dto.getReferencedFiles().forEach(file -> {
//            try {
//                repoService.archiveFile(dto.getPluginCode(), new URI(file.getFileName()).getSchemeSpecificPart());
//            } catch (URISyntaxException e) {
//                log.error("archiveFile={} error.{}", file.getFileName(), e.getMessage());
//            }
//        });
    }

    /**
     * 恢复服务定义
     *
     * @param actionPoint
     */
    @Override
    public void restore(String actionPoint) throws IOException {
        log.debug("restore={}", actionPoint);
        ActionDefinitionDto dto = get(actionPoint);
        dto.setArchived(false);
        saveActionDefinitionDto(dto);
        //让用户主动  恢复
//        //查找依赖的文件，将依赖的文件从 archive 目录 移动到 repo 插件的目录
//        dto.getReferencedFiles().forEach(file -> {
//            //new URI("skynet:iShot_2025-06-15_10.08.41.png").getSchemeSpecificPart()
//            try {
//                repoService.restoreFile(dto.getPluginCode(), new URI(file.getFileName()).getSchemeSpecificPart());
//            } catch (URISyntaxException e) {
//                log.error("restoreFile={} error.{}", file.getFileName(), e.getMessage());
//            }
//        });
    }

    private void checkActionDefinitionDto(ActionDefinitionDto dto) {
        if (StringUtils.isBlank(dto.getActionCode()) || StringUtils.isBlank(dto.getPluginCode()) ||
                StringUtils.isBlank(dto.getActionName()) || StringUtils.isBlank(dto.getType()) ||
                dto.getStartupConfig() == null || dto.getHealthCheckConfig() == null || dto.getIntegrationConfig() == null) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }

        boolean isOk = Pattern.matches("^[0-9a-zA-Z-_]+$", dto.getActionCode());
        if (!isOk) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ACTION_CODE_ILLEGAL);
        }
        //服务编码与应用系统编码不能相同
        if (dto.getActionCode().equalsIgnoreCase(dto.getPluginCode())) {
            throw new ApiRequestException(ApiRequestErrorCode.ACTION_WITH_PLUGIN_EQUAL);
        }
    }

    /**
     * 保存服务定义
     *
     * @param dto
     */
    private void saveActionDefinitionDto(ActionDefinitionDto dto) {
        checkActionDefinitionDto(dto);

        BootParam xBootParam = new BootParam();

        ActionDefinitionDto.StartupConfigDto startupConfig = dto.getStartupConfig();
        xBootParam.setArchived(dto.isArchived());
        xBootParam.setWorkCmd(startupConfig.getCmd());
        xBootParam.setLogFile(startupConfig.getLogFile());

        xBootParam.setIndexPageUrl(dto.getHomePageURL());
        xBootParam.setWorkHome(startupConfig.getWorkingDir());
        xBootParam.setPorts(dto.getPorts());
        xBootParam.setReplicas(dto.getReplicas());
        xBootParam.setInstances(dto.getInstances());

        if (StringUtils.isNotEmpty(startupConfig.getRunnableJar())) {
            xBootParam.setMainJar(startupConfig.getRunnableJar().trim());
        } else {
            xBootParam.setMainJar("");
        }

        xBootParam.setActionProtocol(StringUtils.isNotBlank(dto.getProtocol()) ? dto.getProtocol() : "http");

        if (StringUtils.isNotBlank(dto.getType()) && dto.getType().trim().equals(BootType.DockerBoot.toString())) {
            xBootParam.setDockerEnv(startupConfig.getDockerRunOptions());
            startupConfig.setProgramArguments(startupConfig.getDockerRunCmdAndArgs());
        }

        // workArgs
        if (StringUtils.isNotBlank(startupConfig.getProgramArguments())) {
            String[] workArgs = startupConfig.getProgramArguments().split("\n");
            for (String arg : workArgs) {
                if (StringUtils.isNoneBlank(arg.trim())) {
                    xBootParam.getWorkArgs().add(arg.trim());
                }
            }
        } else {
            xBootParam.getWorkArgs().clear();
        }

        // workEnvs
        xBootParam.setWorkEnvs(startupConfig.getSysEnvironments());

        if (StringUtils.isNotBlank(String.valueOf(startupConfig.getSignalToStop()))) {
            xBootParam.setKillSignal(startupConfig.getSignalToStop());
        }

        ActionDefinitionDto.IntegrationConfigDto integrationConfigDto = dto.getIntegrationConfig();
        if (StringUtils.isNotBlank(String.valueOf(integrationConfigDto.isLogbackLogCollection()))) {
            xBootParam.setLogCollection(integrationConfigDto.isLogbackLogCollection());
        }
        if (StringUtils.isNotBlank(String.valueOf(integrationConfigDto.isMeshEnabled()))) {
            xBootParam.setMeshEnabled(integrationConfigDto.isMeshEnabled());
        }

        // 文件更新参数设置, 只保存不为空的参数
        List<ActionDefinitionDto.ReferencedFileDto> referencedFiles = dto.getReferencedFiles();
        if (!CollectionUtils.isEmpty(referencedFiles)) {
            for (ActionDefinitionDto.ReferencedFileDto item : referencedFiles) {
                if (StringUtils.isNoneBlank(item.getTargetDir()) && StringUtils.isNoneBlank(item.getFileName())) {
                    xBootParam.getUpdateParams().add(new UpdateParam(item.getTargetDir(), item.getFileName(), item.getMode(), item.getOwner()));
                }
            }
        }

        //扩展自定义配置
        List<ActionDefinitionDto.ExtConfigItemDto> extConfigItems = dto.getExtConfigItems();
        if (!CollectionUtils.isEmpty(extConfigItems)) {
            for (ActionDefinitionDto.ExtConfigItemDto item : extConfigItems) {
                if (StringUtils.isNoneBlank(item.getTargetFile()) && StringUtils.isNoneBlank(item.getText())) {
                    ExtConfigItem target = new ExtConfigItem();
                    BeanUtils.copyProperties(item, target);
                    xBootParam.getExtConfigItems().add(target);
                }
            }
        }

        xBootParam.setMeshConfigText(dto.getMeshConfigText());
        xBootParam.setYaml(dto.getYaml());
        //add  by 2023年10月07日16:47:49
        xBootParam.setConfigBlockCodes(dto.getConfigBlockCodes());

        //add  by  2023年10月27日14:26:49
        xBootParam.setDependActions(JSON.parseArray(JSON.toJSONString(dto.getDependActions()), DependAction.class));

        ActionDefinitionDto.HealthCheckConfig healthCheckConfig = dto.getHealthCheckConfig();
        HealthParam healthParam = new HealthParam();
        healthParam.setCheckEnabled("protocol".equals(healthCheckConfig.getType()));
        healthParam.setInitialDelaySeconds(healthCheckConfig.getDelaySeconds());
        healthParam.setIntervalSeconds(healthCheckConfig.getIntervalSeconds());
        healthParam.setTimeoutSeconds(healthCheckConfig.getTimeoutSeconds());
        healthParam.setPath(healthCheckConfig.getUrl());
        healthParam.setRetryTimes(healthCheckConfig.getRetryTimes());
        xBootParam.setHealthParam(healthParam);

        if (dto.getSwitchLabels() != null && !dto.getSwitchLabels().isEmpty()) {
            List<AntActionLabel> labels = new ArrayList<>();
            for (SwitchLabelValueDto switchLabelValueDto : dto.getSwitchLabels()) {
                AntActionLabel label = new AntActionLabel();
                label.setCode(switchLabelValueDto.getCode());
                label.setExtProperty(switchLabelValueDto.getExtProperty());
                label.setValue(switchLabelValueDto.isValue());
                labels.add(label);
            }
            xBootParam.setActionLabels(labels);
        }
        // ZK持久化
        String actionNodePath = this.antConfigService.getActionPath(dto.getPluginCode(), dto.getActionCode());
        if (!this.antConfigService.exists(actionNodePath)) {
            this.antConfigService.setData(actionNodePath, dto.getActionCode());
        }

        this.antConfigService.setData(String.format("%s/_atitle", actionNodePath), dto.getActionName().trim());
        this.antConfigService.setData(String.format("%s/_desc", actionNodePath), dto.getDescription().trim());
        this.antConfigService.setData(String.format("%s/_xboot_type", actionNodePath), dto.getType());
        this.antConfigService.setData(String.format("%s/_param", actionNodePath), dto.getStartupConfig().getSkynetRunParam());
        this.antConfigService.setData(String.format("%s/_java_opts", actionNodePath), dto.getStartupConfig().getJavaCmdOptions());
        this.antConfigService.setData(String.format("%s/_port", actionNodePath), String.valueOf(dto.getPort()));
        this.antConfigService.setData(String.format("%s/_xboot_param", actionNodePath), JSON.toJSONString(xBootParam));
        this.antConfigService.setData(String.format("%s/_index", actionNodePath), String.valueOf(dto.getIndex()));
        this.antConfigService.setData(String.format("%s/_tags", actionNodePath), dto.getTags() != null ? JSON.toJSONString(dto.getTags()) : "[]");

        // 设置日志,属性
        skynetSettingManager.getPropertiesService().setProps(dto.getPluginCode(), dto.getActionCode(), dto.getProperties());
        skynetSettingManager.getLoggerServices().setProps(dto.getPluginCode(), dto.getActionCode(), dto.getLoggingLevels());
    }


    /**
     * 获取服务定义
     *
     * @param plugin
     * @param action
     * @return
     * @throws IOException
     */
    private ActionDefinitionDto getActionDefinitionDto(String plugin, String action) throws IOException {
        try {
            this.checkAction(plugin, action);
            String actionPath = this.antConfigService.getActionPath(plugin, action);
            Map<String, String> nodeList = this.antConfigService.getChildrenWithData(actionPath, null);
            String name = nodeList.get(String.format("%s/_atitle", actionPath));
            String desc = nodeList.get(String.format("%s/_desc", actionPath));
            String type = nodeList.get(String.format("%s/_xboot_type", actionPath));
            String port = nodeList.get(String.format("%s/_port", actionPath));
            String javaOpts = nodeList.get(String.format("%s/_java_opts", actionPath));
            String xBootParam = nodeList.get(String.format("%s/_xboot_param", actionPath));

            String param = nodeList.get(String.format("%s/_param", actionPath));
            String index = nodeList.get(String.format("%s/_index", actionPath));
            String tags = nodeList.get(String.format("%s/_tags", actionPath));

            ActionDefinitionDto dto = new ActionDefinitionDto();
            dto.setActionCode(action);
            dto.setPluginCode(plugin);
            dto.setActionName(name);
            dto.setDescription(desc);
            dto.setType(type);

            if (StringUtils.isNotBlank(tags)) {
                dto.setTags(JSON.parseObject(tags, new TypeReference<>() {
                }));
            }

            if (StringUtils.isNumeric(index)) {
                dto.setIndex(Integer.parseInt(index));
            }

            if (StringUtils.isNumeric(port)) {
                dto.setPort(Integer.parseInt(port));
            }

            BootParam bootParam = JSON.parseObject(xBootParam, BootParam.class);
            if (bootParam != null) {
                dto.setProtocol(bootParam.getActionProtocol());
                dto.setHomePageURL(bootParam.getIndexPageUrl());
                dto.setPorts(bootParam.getPorts());
                dto.setYaml(bootParam.getYaml());
                dto.setReplicas(bootParam.getReplicas());
                dto.setInstances(bootParam.getInstances());
                dto.setConfigBlockCodes(bootParam.getConfigBlockCodes());
                dto.setArchived(bootParam.isArchived());

                //add  by  2023年10月27日14:26:49
                dto.setDependActions(JSON.parseArray(JSON.toJSONString(bootParam.getDependActions()), ActionDefinitionDto.DependActionDto.class));

                // ReferencedFileDto
                List<UpdateParam> updateParams = bootParam.getUpdateParams();
                if (!ObjectUtils.isEmpty(updateParams)) {
                    List<ActionDefinitionDto.ReferencedFileDto> referencedFiles = new ArrayList<>();
                    for (UpdateParam updateParam : updateParams) {
                        ActionDefinitionDto.ReferencedFileDto referencedFileDto = new ActionDefinitionDto.ReferencedFileDto();
                        referencedFileDto.setFileName(updateParam.getFileUrl());
                        referencedFileDto.setTargetDir(updateParam.getTargetDir());
                        referencedFileDto.setMode(updateParam.getMode());
                        referencedFileDto.setOwner(updateParam.getOwner());
                        referencedFiles.add(referencedFileDto);
                    }
                    dto.setReferencedFiles(referencedFiles);
                }

                // ExtConfigItemDto
                List<ExtConfigItem> extConfigItems = bootParam.getExtConfigItems();
                if (!ObjectUtils.isEmpty(extConfigItems)) {
                    List<ActionDefinitionDto.ExtConfigItemDto> itemDtoList = new ArrayList<>();
                    for (ExtConfigItem extConfigItem : extConfigItems) {
                        ActionDefinitionDto.ExtConfigItemDto target = new ActionDefinitionDto.ExtConfigItemDto();
                        BeanUtils.copyProperties(extConfigItem, target);
                        itemDtoList.add(target);
                    }
                    dto.setExtConfigItems(itemDtoList);
                }

                // StartupConfigDto
                ActionDefinitionDto.StartupConfigDto startupConfigDto = new ActionDefinitionDto.StartupConfigDto();
                startupConfigDto.setWorkingDir(bootParam.getWorkHome());
                startupConfigDto.setRunnableJar(bootParam.getMainJar());
                startupConfigDto.setJavaCmdOptions(javaOpts);
                if (bootParam.getWorkArgs() != null) {
                    startupConfigDto.setProgramArguments(String.join("\n", bootParam.getWorkArgs()));
                }
                startupConfigDto.setCmd(bootParam.getWorkCmd());
                startupConfigDto.setSkynetRunParam(param);

                //BaseBoot
                startupConfigDto.setLogFile(bootParam.getLogFile());

                //docker boot
                if (type.trim().equals(BootType.DockerBoot.toString())) {
                    if (bootParam.getWorkArgs() != null) {
                        startupConfigDto.setDockerRunCmdAndArgs(String.join("\n", bootParam.getWorkArgs()));
                    }
                    startupConfigDto.setDockerRunOptions(bootParam.getDockerEnv());
                }

                startupConfigDto.setSysEnvironments(bootParam.getWorkEnvs());
                startupConfigDto.setSignalToStop(bootParam.getKillSignal());

                dto.setStartupConfig(startupConfigDto);

                // HealthCheckConfig
                if (bootParam.getHealthParam() != null) {
                    ActionDefinitionDto.HealthCheckConfig healthCheckConfig = new ActionDefinitionDto.HealthCheckConfig();
                    healthCheckConfig.setType(bootParam.getHealthParam().isCheckEnabled() ? "protocol" : "pid");
                    healthCheckConfig.setUrl(bootParam.getHealthParam().getPath());
                    healthCheckConfig.setDelaySeconds(bootParam.getHealthParam().getInitialDelaySeconds());
                    healthCheckConfig.setIntervalSeconds(bootParam.getHealthParam().getIntervalSeconds());
                    healthCheckConfig.setTimeoutSeconds(bootParam.getHealthParam().getTimeoutSeconds());
                    healthCheckConfig.setRetryTimes(bootParam.getHealthParam().getRetryTimes());
                    dto.setHealthCheckConfig(healthCheckConfig);
                }

                ActionDefinitionDto.IntegrationConfigDto integrationConfigDto = new ActionDefinitionDto.IntegrationConfigDto();
                integrationConfigDto.setMeshEnabled(bootParam.isMeshEnabled());
                integrationConfigDto.setLogbackLogCollection(bootParam.isLogCollection());
                dto.setIntegrationConfig(integrationConfigDto);
                dto.setMeshConfigText(bootParam.getMeshConfigText());

                List<AntActionLabel> actionLabels = bootParam.getActionLabels();
                List<SwitchLabelValueDto> switchLabels = Collections.emptyList();
                if (actionLabels != null) {
                    switchLabels = actionLabels.stream().map(actionLabel -> {
                        SwitchLabelValueDto switchLabelValueDto = new SwitchLabelValueDto();
                        switchLabelValueDto.setCode(actionLabel.getCode());
                        switchLabelValueDto.setExtProperty(actionLabel.getExtProperty());
                        switchLabelValueDto.setValue(actionLabel.getValue());
                        return switchLabelValueDto;
                    }).toList();
                }
                dto.setSwitchLabels(switchLabels);
            }

            dto.setProperties(skynetSettingManager.getPropertiesService().getProps(plugin, action, null));
            dto.setLoggingLevels(skynetSettingManager.getLoggerServices().getProps(plugin, action, null));

            return dto;
        } catch (ApiRequestException e) {
            throw e;
        } catch (Exception e) {
            log.error("", e);
            throw e;
        }
    }
}
