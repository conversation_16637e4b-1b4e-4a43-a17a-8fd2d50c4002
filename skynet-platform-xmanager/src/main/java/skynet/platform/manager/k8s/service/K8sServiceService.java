package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sServiceService {

    /**
     * 获取 Service 列表
     */
    List<JSONObject> getServices(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Service 详情
     */
    JSONObject getService(String ip, String namespace, String serviceName) throws Exception;

    /**
     * 获取 Service Yaml
     */
    String getServiceYaml(String ip, String namespace, String serviceName) throws Exception;

    /**
     * 删除 Service
     */
    JSONObject deleteService(String ip, String namespace, String serviceName) throws Exception;
}
