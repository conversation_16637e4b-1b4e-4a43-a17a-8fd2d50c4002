package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sNode;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sNodeService;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sNodeController implements V3K8sNode {

    private final K8sNodeService k8sNodeService;

    public K8sNodeController(K8sNodeService k8sNodeService) {
        this.k8sNodeService = k8sNodeService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getNodes(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sNodeService.getNodes(ip, k8sQuery);
        response.setData(results);
        log.debug("getNodes response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getNode(String ip, String nodeName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.getNode(ip, nodeName);
        response.setData(result);
        log.debug("getNode response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<String> getNodeYaml(String ip, String nodeName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sNodeService.getNodeYaml(ip, nodeName);
        response.setData(result);
        log.debug("getNodeYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "更新 Node", message = "ip=#{#ip}, nodeName=#{#nodeName}, node=#{#node}")
    public SkynetApiResponse<JSONObject> updateNode(String ip, String nodeName, JSONObject node) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.updateNode(ip, nodeName, node);
        response.setData(result);
        log.debug("updateNode response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "删除 Node", message = "ip=#{#ip}, nodeName=#{#nodeName}")
    public SkynetApiResponse<JSONObject> deleteNode(String ip, String nodeName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.deleteNode(ip, nodeName);
        response.setData(result);
        log.debug("deleteNode response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "更新 Node 标签", message = "ip=#{#ip}, nodeName=#{#nodeName}, labels=#{#labels}")
    public SkynetApiResponse<JSONObject> updateNodeLabels(String ip, String nodeName, Map<String, String> labels) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.updateNodeLabels(ip, nodeName, labels);
        response.setData(result);
        log.debug("updateNodeLabels response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "更新 Node 注解", message = "ip=#{#ip}, nodeName=#{#nodeName}, annotations=#{#annotations}")
    public SkynetApiResponse<JSONObject> updateNodeAnnotations(String ip, String nodeName, Map<String, String> annotations) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.updateNodeAnnotations(ip, nodeName, annotations);
        response.setData(result);
        log.debug("updateNodeAnnotations response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "封锁调度", message = "ip=#{#ip}, nodeName=#{#nodeName}")
    public SkynetApiResponse<JSONObject> cordonNode(String ip, String nodeName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.cordonNode(ip, nodeName);
        response.setData(result);
        log.debug("cordonNode response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "解除封锁", message = "ip=#{#ip}, nodeName=#{#nodeName}")
    public SkynetApiResponse<JSONObject> uncordonNode(String ip, String nodeName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.uncordonNode(ip, nodeName);
        response.setData(result);
        log.debug("uncordonNode response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Node 管理", operation = "排空节点", message = "ip=#{#ip}, nodeName=#{#nodeName}")
    public SkynetApiResponse<JSONObject> drainNode(String ip, String nodeName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeService.drainNode(ip, nodeName);
        response.setData(result);
        log.debug("drainNode response:{}", response);
        return response;
    }
}
