package skynet.platform.manager.k8s.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.SkynetProperties;

@Configuration(proxyBeanMethods = false)
public class K8sConfiguration {

    @Bean
    @ConfigurationProperties("skynet.k8s")
    public K8sConfigProperties k8sConfigProperties(SkynetProperties skynetProperties) {
        return new K8sConfigProperties(skynetProperties);
    }
}
