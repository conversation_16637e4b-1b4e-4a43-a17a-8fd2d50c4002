package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sPodService {

    /**
     * 获取 Pod 列表
     */
    List<JSONObject> getPods(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Pod 详情
     */
    JSONObject getPod(String ip, String namespace, String podName) throws Exception;

    /**
     * 获取 Pod Yaml
     */
    String getPodYaml(String ip, String namespace, String podName) throws Exception;

    /**
     * 删除 Pod
     */
    JSONObject deletePod(String ip, String namespace, String podName) throws Exception;
}
