package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.custom.NodeMetrics;
import io.kubernetes.client.custom.NodeMetricsList;
import io.kubernetes.client.util.generic.GenericKubernetesApi;
import io.kubernetes.client.util.generic.options.ListOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sNodeMetricService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sNodeMetricServiceImpl extends K8sBaseService implements K8sNodeMetricService {

    public K8sNodeMetricServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 NodeMetric 列表
     */
    @Override
    public List<JSONObject> getNodeMetrics(String ip, K8sQuery k8sQuery) throws Exception {

        List<JSONObject> nodeMetricsDtoList = new ArrayList<>();
        GenericKubernetesApi<NodeMetrics, NodeMetricsList> metricsClient = new GenericKubernetesApi<>(
                NodeMetrics.class,
                NodeMetricsList.class,
                "metrics.k8s.io",
                "v1beta1",
                "nodes",
                initApiClient(ip));
        ListOptions listOptions = new ListOptions();
        listOptions.setFieldSelector(k8sQuery.getFieldSelector());
        listOptions.setLabelSelector(k8sQuery.getLabelSelector());
        NodeMetricsList nodeMetricsList = metricsClient.list(listOptions).throwsApiException().getObject();
        for (NodeMetrics nodeMetrics : nodeMetricsList.getItems()) {
            nodeMetricsDtoList.add(toJSON(nodeMetrics));
        }
        return nodeMetricsDtoList;
    }

    /**
     * 获取 NodeMetric 详情
     */
    @Override
    public JSONObject getNodeMetric(String ip, String nodeName) throws Exception {

        GenericKubernetesApi<NodeMetrics, NodeMetricsList> metricsClient = new GenericKubernetesApi<>(
                NodeMetrics.class,
                NodeMetricsList.class,
                "metrics.k8s.io",
                "v1beta1",
                "nodes",
                initApiClient(ip));
        NodeMetrics nodeMetrics = metricsClient.get(nodeName).throwsApiException().getObject();
        return toJSON(nodeMetrics);
    }

}
