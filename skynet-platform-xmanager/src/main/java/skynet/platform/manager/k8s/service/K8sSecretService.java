package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sSecretService {

    /**
     * 获取 Secret 列表
     */
    List<JSONObject> getSecrets(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Secret 详情
     */
    JSONObject getSecret(String ip, String namespace, String secretName) throws Exception;

    /**
     * 获取 Secret Yaml
     */
    String getSecretYaml(String ip, String namespace, String secretName) throws Exception;

    /**
     * 删除 Secret
     */
    JSONObject deleteSecret(String ip, String namespace, String secretName) throws Exception;
}
