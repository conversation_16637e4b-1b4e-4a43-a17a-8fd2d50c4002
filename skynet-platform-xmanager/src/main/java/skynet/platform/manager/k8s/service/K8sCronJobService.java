package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateCronJobHistoryLimit;

import java.util.List;

public interface K8sCronJobService {

    /**
     * 获取 CronJob 列表
     */
    List<JSONObject> getCronJobs(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 CronJob 详情
     */
    JSONObject getCronJob(String ip, String namespace, String cronJobName) throws Exception;

    /**
     * 获取 CronJob Yaml
     */
    String getCronJobYaml(String ip, String namespace, String cronJobName) throws Exception;

    /**
     * 删除 CronJob
     */
    JSONObject deleteCronJob(String ip, String namespace, String cronJobName) throws Exception;

    /**
     * 暂停运行
     */
    JSONObject stopCronJob(String ip, String namespace, String cronJobName) throws Exception;

    /**
     * 恢复运行
     */
    JSONObject resumeCronJob(String ip, String namespace, String cronJobName) throws Exception;

    /**
     * 手工触发
     */
    JSONObject manualCronJob(String ip, String namespace, String cronJobName) throws Exception;

    /**
     * 更新最大历史 Job 数
     */
    JSONObject updateCronJobHistoryLimit(String ip, String namespace, String cronJobName, K8sUpdateCronJobHistoryLimit k8sUpdateCronJobHistoryLimit) throws Exception;
}
