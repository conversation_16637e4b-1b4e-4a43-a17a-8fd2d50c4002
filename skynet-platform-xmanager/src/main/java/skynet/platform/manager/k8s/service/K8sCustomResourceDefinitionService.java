package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sCustomResourceDefinitionService {

    /**
     * 获取 CustomResourceDefinition 列表
     */
    List<JSONObject> getCustomResourceDefinitions(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 CustomResourceDefinition 详情
     */
    JSONObject getCustomResourceDefinition(String ip, String crdName) throws Exception;

    /**
     * 获取 CustomResourceDefinition Yaml
     */
    String getCustomResourceDefinitionYaml(String ip, String crdName) throws Exception;

    /**
     * 删除 CustomResourceDefinition
     */
    JSONObject deleteCustomResourceDefinition(String ip, String crdName) throws Exception;
}
