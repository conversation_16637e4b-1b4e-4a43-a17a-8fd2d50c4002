package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.AutoscalingV2Api;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.openapi.models.V2HorizontalPodAutoscaler;
import io.kubernetes.client.openapi.models.V2HorizontalPodAutoscalerList;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sHorizontalPodAutoscalerService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sHorizontalPodAutoscalerServiceImpl extends K8sBaseService implements K8sHorizontalPodAutoscalerService {

    public K8sHorizontalPodAutoscalerServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 HorizontalPodAutoscaler 列表
     */
    @Override
    public List<JSONObject> getHorizontalPodAutoscalers(String ip, K8sQuery k8sQuery) throws Exception {

        AutoscalingV2Api api = new AutoscalingV2Api(initApiClient(ip));

        List<JSONObject> hpaDtoList = new ArrayList<>();
        V2HorizontalPodAutoscalerList hpaList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            hpaList = api.listNamespacedHorizontalPodAutoscaler(k8sQuery.getNamespace()).execute();
        } else {
            hpaList = api.listHorizontalPodAutoscalerForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V2HorizontalPodAutoscaler> filteredHPAs = applyK8sQueryFilter(hpaList.getItems(), k8sQuery);

        for (V2HorizontalPodAutoscaler hpa : filteredHPAs) {
            hpaDtoList.add(toJSON(hpa));
        }
        return hpaDtoList;
    }

    /**
     * 获取 HorizontalPodAutoscaler 详情
     */
    @Override
    public JSONObject getHorizontalPodAutoscaler(String ip, String namespace, String hpaName) throws Exception {

        AutoscalingV2Api api = new AutoscalingV2Api(initApiClient(ip));

        V2HorizontalPodAutoscaler hpa = api.readNamespacedHorizontalPodAutoscaler(hpaName, namespace).execute();
        return toJSON(hpa);
    }

    /**
     * 获取 HorizontalPodAutoscaler Yaml
     */
    @Override
    public String getHorizontalPodAutoscalerYaml(String ip, String namespace, String hpaName) throws Exception {

        AutoscalingV2Api api = new AutoscalingV2Api(initApiClient(ip));

        V2HorizontalPodAutoscaler hpa = api.readNamespacedHorizontalPodAutoscaler(hpaName, namespace).execute();
        return Yaml.dump(hpa);
    }

    /**
     * 删除 HorizontalPodAutoscaler
     */
    @Override
    public JSONObject deleteHorizontalPodAutoscaler(String ip, String namespace, String hpaName) throws Exception {

        AutoscalingV2Api api = new AutoscalingV2Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedHorizontalPodAutoscaler(hpaName, namespace).execute();
        return toJSON(status);
    }

}
