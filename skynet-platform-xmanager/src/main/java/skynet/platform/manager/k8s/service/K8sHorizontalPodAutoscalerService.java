package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sHorizontalPodAutoscalerService {

    /**
     * 获取 HorizontalPodAutoscaler 列表
     */
    List<JSONObject> getHorizontalPodAutoscalers(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 HorizontalPodAutoscaler 详情
     */
    JSONObject getHorizontalPodAutoscaler(String ip, String namespace, String hpaName) throws Exception;

    /**
     * 获取 HorizontalPodAutoscaler Yaml
     */
    String getHorizontalPodAutoscalerYaml(String ip, String namespace, String hpaName) throws Exception;

    /**
     * 删除 HorizontalPodAutoscaler
     */
    JSONObject deleteHorizontalPodAutoscaler(String ip, String namespace, String hpaName) throws Exception;
}
