package skynet.platform.manager.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Skynet Manager Configuration Properties
 *
 * <p>This configuration class contains all manager-specific settings that control
 * the behavior of the Skynet manager service. The manager serves as the central
 * control plane for the entire Skynet cluster, providing web UI, API endpoints,
 * and cluster coordination.</p>
 *
 * <p>All properties support Spring Cloud Config refresh scope, allowing for
 * dynamic configuration updates without service restart.</p>
 *
 * <p>Configuration areas covered:</p>
 * <ul>
 *   <li>Server communication timeouts</li>
 *   <li>Repository and file management</li>
 *   <li>Kubernetes integration settings</li>
 *   <li>Docker registry configuration</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 3.4.15
 * @see org.springframework.cloud.context.config.annotation.RefreshScope
 */
@Getter
@Setter
@Component
@RefreshScope
public class ManagerProperties {

    /**
     * Timeout in seconds for fetching server status from agents.
     *
     * <p>This timeout applies to HTTP requests made by the manager to agent
     * services to retrieve status information, health checks, and metrics.
     * Lower values provide faster failure detection but may cause timeouts
     * on slow networks or overloaded agents.</p>
     *
     * <p>Default: 5 seconds</p>
     * <p>Range: 1-30 seconds (recommended)</p>
     */
    @Value("${skynet.fetch.server.status.timeout.seconds:5}")
    private int timeout = 5;

    /**
     * SSH connection timeout in seconds for remote operations.
     *
     * <p>Used when the manager needs to perform SSH operations on remote
     * servers for deployment, configuration, or maintenance tasks. This
     * timeout applies to both connection establishment and command execution.</p>
     *
     * <p>Default: 8 seconds</p>
     * <p>Range: 5-60 seconds (recommended)</p>
     */
    @Value("${skynet.xmanager.ssh.timeout.seconds:8}")
    private int sshTimeout = 8;

    /**
     * Local repository path for storing service artifacts and configurations.
     *
     * <p>This directory is used to store:</p>
     * <ul>
     *   <li>Service JAR files and dependencies</li>
     *   <li>Configuration templates</li>
     *   <li>Deployment scripts</li>
     *   <li>Backup files</li>
     * </ul>
     *
     * <p>The path should be accessible by the manager service and have
     * sufficient disk space for all managed artifacts.</p>
     *
     * <p>Default: empty (must be configured)</p>
     * <p>Example: /opt/skynet/repo</p>
     */
    @Value("${skynet.xmanager.repo.path:}")
    private String repoPath;

    /**
     * Default port number for agent services in standard deployments.
     *
     * <p>This port is used when connecting to agent services that are
     * deployed directly on host machines (non-Kubernetes deployments).</p>
     *
     * <p>Default: 6230</p>
     * <p>Range: 1024-65535</p>
     */
    @Value("${skynet.xagent.server.port:6230}")
    private int agentServerPort;

    /**
     * Port number for agent services in Kubernetes deployments.
     *
     * <p>This port is used when connecting to agent services that are
     * deployed as Kubernetes services, typically exposed via NodePort
     * or LoadBalancer services.</p>
     *
     * <p>Default: 32630</p>
     * <p>Range: 30000-32767 (NodePort range)</p>
     */
    @Value("${skynet.xagent.k8s.port:32630}")
    private int agentK8sPort;

    /**
     * Kubernetes namespace for Skynet platform services.
     *
     * <p>All Skynet platform components (agents, managers, operators) will
     * be deployed in this namespace. This allows for proper isolation and
     * resource management within the Kubernetes cluster.</p>
     *
     * <p>Default: default</p>
     * <p>Recommended: skynet-system or skynet</p>
     */
    @Value("${skynet.k8s.namespace:default}")
    private String k8sNamespace;

    /**
     * Context path for the Docker registry service.
     *
     * <p>This path is appended to the registry URL when accessing registry
     * APIs for image management operations. It should match the registry
     * service configuration.</p>
     *
     * <p>Default: skynet</p>
     * <p>Example: If registry is at http://registry.example.com, the full
     * path becomes http://registry.example.com/skynet</p>
     */
    @Value("${skynet.k8s.registry.context-path:skynet}")
    private String registryContextPath;

    /**
     * Controls the method used for Docker manifest pushing operations.
     *
     * <p>When {@code true}, uses command-line Docker tools for pushing
     * multi-architecture manifests. When {@code false}, uses Docker Java
     * API for manifest operations.</p>
     *
     * <p>Command-line method is more reliable for complex manifest operations
     * but requires Docker CLI to be available. API method is faster but may
     * have limitations with certain manifest formats.</p>
     *
     * <p>Default: false (use Docker Java API)</p>
     */
    @Value("${skynet.manifest.push.use-cmd:false}")
    private boolean manifestPushStyleWithCmd;

    @Value("${skynet.grafana.actionPoint:grafana-server-v6@ant-mon}")
    private String grafanaActionPoint;

    @Value("${skynet.prometheus.nodeExporter.actionPoint:node-exporter@ant-mon}")
    private String nodeExporterActionPoint;

    @Value("${skynet.xmanager.repo.download.tar.format:true}")
    private boolean tarFormat = true;

    @Value("${skynet.xmanager.repo.download.with.zkconfig:true}")
    private boolean withZkConfig = true;
}
