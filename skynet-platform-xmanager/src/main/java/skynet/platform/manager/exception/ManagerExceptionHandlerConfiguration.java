package skynet.platform.manager.exception;

import io.kubernetes.client.openapi.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.cli.CommandLineException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.exception.handler.ExceptionDescriptor;
import skynet.boot.exception.handler.SkynetExceptionHandler;
import skynet.boot.exception.message.ExceptionMessageFormatter;

@Slf4j
@Configuration
public class ManagerExceptionHandlerConfiguration {

    @Bean
    public SkynetExceptionHandler<ApiException> KubernetesApiExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-1024, "Kubernetes 接口异常")) {
            protected void showLog(String url, ExceptionDescriptor.ExceptionInfo info, ApiException e) {
                log.error("{}: code={}; url={}; message={}; responseBody={}", info.getName(), info.getCode(), url, e.getMessage(), e.getResponseBody());
            }
        };
    }

    @Bean
    public SkynetExceptionHandler<CommandLineException> CommandLineExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-1023, "命令行执行异常")) {
        };
    }
}
