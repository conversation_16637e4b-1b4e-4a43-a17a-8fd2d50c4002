package skynet.platform.manager.backup.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.SkynetProperties;
import skynet.platform.common.condition.ConditionalOnManager;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.manager.admin.core.PluginZkTextService;
import skynet.platform.manager.admin.service.V3ClusterService;
import skynet.platform.manager.backup.service.BackupService;
import skynet.platform.manager.backup.service.impl.BackupServiceImpl;

@Slf4j
@ConditionalOnManager
@Configuration(proxyBeanMethods = false)
public class BackupConfiguration {


    @Bean
    @ConfigurationProperties("skynet.backup")
    public BackupProperties backupProperties() {
        return new BackupProperties();
    }

    @Bean
    public BackupService backupService(BackupProperties backupProperties, IAntConfigService antConfigService,
                                       PluginZkTextService pluginZkTextService, V3ClusterService v3ClusterService,
                                       SkynetProperties skynetProperties) {
        return new BackupServiceImpl(backupProperties, antConfigService, pluginZkTextService, v3ClusterService, skynetProperties);
    }
}
