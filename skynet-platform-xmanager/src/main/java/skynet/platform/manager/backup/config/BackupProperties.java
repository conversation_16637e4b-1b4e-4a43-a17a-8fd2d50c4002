package skynet.platform.manager.backup.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetProperties;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
public class BackupProperties extends Jsonable {

    /**
     * 是否开启备份
     */
    private boolean enabled = true;

    /**
     * 备份目录，默认：{skynet_home}/backup
     */
    private String backupDir;

    /**
     * 自动备份时间间隔，默认 30 分钟
     */
    private Integer interval = 30;





}
