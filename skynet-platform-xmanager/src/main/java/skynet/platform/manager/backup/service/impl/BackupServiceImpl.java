package skynet.platform.manager.backup.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import skynet.boot.SkynetProperties;
import skynet.boot.exception.SkynetException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.model.BackupDto;
import skynet.platform.feign.model.BackupRestoreDto;
import skynet.platform.manager.admin.core.PluginZkTextService;
import skynet.platform.manager.admin.service.V3ClusterService;
import skynet.platform.manager.backup.config.BackupProperties;
import skynet.platform.manager.backup.service.BackupService;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class BackupServiceImpl implements BackupService {

    private final BackupProperties backupProperties;
    private final IAntConfigService antConfigService;
    private final PluginZkTextService pluginZkTextService;
    private final V3ClusterService v3ClusterService;
    private final File backupDir;

    public BackupServiceImpl(BackupProperties backupProperties, IAntConfigService antConfigService,
                             PluginZkTextService pluginZkTextService, V3ClusterService v3ClusterService,
                             SkynetProperties skynetProperties) {
        this.backupProperties = backupProperties;
        this.antConfigService = antConfigService;
        this.pluginZkTextService = pluginZkTextService;
        this.v3ClusterService = v3ClusterService;
        this.backupDir = new File(StringUtils.isBlank(backupProperties.getBackupDir()) ? String.format("%s/backup", skynetProperties.getHome()) : backupProperties.getBackupDir());

        if (this.backupProperties.isEnabled()) {
            log.info("Create Backup Dir = {}", backupDir);
            try {
                FileUtils.forceMkdir(backupDir);
            } catch (Exception e) {
                log.error("create backup directory failed：{}", e.getMessage());
            }
            this.startBackupThread();
        }
    }

    /**
     * 创建一个新备份
     */
    @Override
    public BackupDto createBackup() throws Exception {
        return createBackup(false);
    }

    /**
     * 创建一个新备份
     * 如果 auto 为 true，说明是自动创建的，则判断当前备份和最近一次备份是否一样，如果一样的话则跳过备份
     * 如果 auto 为 false，说明是手工创建的，则强制创建一个备份
     */
    private BackupDto createBackup(boolean auto) throws Exception {
        BackupDto backupDto = getCurrentBackup();
        if (auto) {
            BackupDto latestBackupDto = getLatestBackup();
            if (latestBackupDto != null) {
                log.info("obtain the latest backup：{}", latestBackupDto.getName());
                if (isBackupSame(backupDto, latestBackupDto)) {
                    log.info("the current backup is the same as the latest backup，skip backup");
                    return latestBackupDto;
                }
            }
            backupDto.setName("backup-" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + "-auto");
        } else {
            backupDto.setName("backup-" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + "-manual");
        }
        File backupFile = new File(this.backupDir, backupDto.getName());
        FileUtils.writeStringToFile(backupFile, JSON.toJSONString(backupDto), StandardCharsets.UTF_8);
        log.info("write to file：{}", backupFile.getAbsolutePath());
        return backupDto;
    }

    /**
     * 获取备份列表
     */
    @Override
    public List<BackupDto> getBackups() {

        List<BackupDto> backupDtoList = new ArrayList<>();
        File[] files = this.backupDir.listFiles();
        if (files == null) return backupDtoList;
        for (File file : files) {
            BackupDto backupDto = new BackupDto();
            backupDto.setName(file.getName());
            backupDtoList.add(backupDto);
        }
        return backupDtoList.stream().sorted((x, y) -> y.getName().compareTo(x.getName())).toList();
    }

    /**
     * 获取某个备份详情
     */
    @Override
    public BackupDto getBackup(String backupName) throws Exception {
        if ("current".equals(backupName)) {
            return getCurrentBackup();
        }
        File backupFile = new File(this.backupDir, backupName);
        if (!backupFile.exists()) {
            throw new SkynetException(-1, "backup does not exist");
        }
        String content = FileUtils.readFileToString(backupFile, StandardCharsets.UTF_8);
        return JSON.parseObject(content, BackupDto.class);
    }

    /**
     * 恢复备份
     */
    @Override
    public BackupDto restoreBackup(BackupRestoreDto backupRestoreDto) throws Exception {

        // 还原整个备份
        if (StringUtils.isNotBlank(backupRestoreDto.getName())) {
            return restoreBackup(backupRestoreDto.getName());
        }

        // 还原特定的插件备份
        return restoreBackup(backupRestoreDto.getCode(), backupRestoreDto.getContent());
    }

    /**
     * 还原整个备份
     */
    private BackupDto restoreBackup(String backupName) throws Exception {
        BackupDto backupDto = getBackup(backupName);
        // 恢复所有插件配置
        for (BackupDto.BackupPluginVo plugin : backupDto.getPlugins()) {
            pluginZkTextService.importZkConfig(plugin.getContent());
        }
        // 恢复集群配置
        v3ClusterService.updateLoggingLevels(backupDto.getCluster().getLoggingLevels());
        v3ClusterService.updateProperties(backupDto.getCluster().getProperties());
        return backupDto;
    }

    /**
     * 还原特定的插件备份
     */
    private BackupDto restoreBackup(String pluginCode, String content) throws Exception {
        if ("cluster.properties".equals(pluginCode)) {
            v3ClusterService.updateProperties(content);
        } else if ("cluster.loggingLevels".equals(pluginCode)) {
            v3ClusterService.updateLoggingLevels(content);
        } else {
            pluginZkTextService.importZkConfig(content);
        }
        return null;
    }

    /**
     * 删除某个备份
     */
    @Override
    public BackupDto deleteBackup(String backupName) throws Exception {
        File backupFile = new File(this.backupDir, backupName);
        if (!backupFile.exists()) {
            throw new SkynetException(-1, "backup does not exist");
        }
        FileUtils.deleteQuietly(backupFile);
        BackupDto backupDto = new BackupDto();
        backupDto.setName(backupName);
        return backupDto;
    }

    /**
     * 获取当前备份，使用 current 作为备份名
     */
    private BackupDto getCurrentBackup() throws Exception {
        BackupDto backupDto = new BackupDto();
        backupDto.setName("current");
        backupDto.setPlugins(new ArrayList<>());
        // 获取所有插件配置
        List<NodeDescription> plugins = antConfigService.getPlugins();
        for (NodeDescription plugin : plugins) {
            String config = pluginZkTextService.exportZkConfig(plugin.getCode());
            BackupDto.BackupPluginVo backupPluginVo = new BackupDto.BackupPluginVo();
            backupPluginVo.setCode(plugin.getCode());
            backupPluginVo.setName(plugin.getName());
            backupPluginVo.setContent(config);
            backupDto.getPlugins().add(backupPluginVo);
        }
        // 获取集群配置
        BackupDto.BackupClusterVo backupClusterVo = new BackupDto.BackupClusterVo();
        backupClusterVo.setLoggingLevels(v3ClusterService.getLoggingLevels() == null ? "" : v3ClusterService.getLoggingLevels());
        backupClusterVo.setProperties(v3ClusterService.getProperties() == null ? "" : v3ClusterService.getProperties());
        backupDto.setCluster(backupClusterVo);
        return backupDto;
    }

    /**
     * 获取最近一次备份，如果没有备份，返回 null
     */
    private BackupDto getLatestBackup() throws Exception {
        List<String> history = getBackups().stream().map(BackupDto::getName).sorted().toList();
        if (history.isEmpty()) {
            return null;
        }
        String latestName = history.getLast();
        return getBackup(latestName);
    }

    /**
     * 判断两个备份是否相同
     */
    private boolean isBackupSame(BackupDto backup1, BackupDto backup2) {

        BackupDto.BackupClusterVo cluster1 = backup1.getCluster();
        BackupDto.BackupClusterVo cluster2 = backup2.getCluster();
        log.debug("cluster1={}", cluster1);
        log.debug("cluster2={}", cluster2);
        if (!cluster1.getProperties().equals(cluster2.getProperties()) || !cluster1.getLoggingLevels().equals(cluster2.getLoggingLevels())) {
            return false;
        }

        if (backup1.getPlugins().size() != backup2.getPlugins().size()) {
            return false;
        }

        for (int i = 0; i < backup1.getPlugins().size(); i++) {
            BackupDto.BackupPluginVo plugin1 = backup1.getPlugins().get(i);
            BackupDto.BackupPluginVo plugin2 = backup2.getPlugins().get(i);
            if (!plugin1.getCode().equals(plugin2.getCode()) || !plugin1.getName().equals(plugin2.getName())) {
                return false;
            }
            // 导出的服务定义中有一行类似于下面这样的注释，需要忽略
            // # Dumped at 2023-09-07 14:10:45: zookeeper[172.31.164.8:2181]
            String plugin1Content = plugin1.getContent().substring(plugin1.getContent().indexOf('\n') + 1);
            String plugin2Content = plugin2.getContent().substring(plugin2.getContent().indexOf('\n') + 1);
            if (!plugin1Content.equals(plugin2Content)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 开启备份线程
     */
    private void startBackupThread() {
        ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1, runnable -> {
            Thread thread = new Thread(runnable);
            thread.setName("skynet.backup.thread");
            return thread;
        });
        scheduledExecutorService.scheduleWithFixedDelay(() -> {
            try {
                log.info("automatic backup start...");
                BackupDto backupDto = createBackup(true);
                log.info("automatic backup successfully：{}", backupDto.getName());
            } catch (Exception e) {
                log.error("automatic backup exception：{}", e.getMessage(), e);
            }
        }, 1, backupProperties.getInterval(), TimeUnit.MINUTES);
    }
}
