package skynet.platform.manager.security;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * auth相关配置.
 *
 * <AUTHOR> QQ: 408365330
 */
@Getter
@Setter
public class AuthProperties extends Jsonable {

    /**
     * 超时时间秒 默认是10分钟
     */
    private int expiresSecond = 10 * 60;

    /**
     * 密码重复验证错误，锁定周期
     */
    private int failLockDurationSecond = 3 * 60;
    /**
     * 密码验证错误 重试次数
     */
    private int failTriesTimes = 5;

}
