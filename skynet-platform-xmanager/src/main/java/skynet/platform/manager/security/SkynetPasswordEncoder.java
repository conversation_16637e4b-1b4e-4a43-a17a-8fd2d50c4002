package skynet.platform.manager.security;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.crypto.password.Pbkdf2PasswordEncoder;
import skynet.boot.common.utils.MD5Util;

/**
 * 用户名密码 加密算法
 * 为了兼容老版本，对 Pbkdf2PasswordEncoder 重新包装了
 *
 * <AUTHOR>
 */
public class SkynetPasswordEncoder implements PasswordEncoder {

    private final Pbkdf2PasswordEncoder pbkdf2PasswordEncoder;

    public SkynetPasswordEncoder() {
        this.pbkdf2PasswordEncoder = new Pbkdf2PasswordEncoder("skynet", 16, 185000,
                Pbkdf2PasswordEncoder.SecretKeyFactoryAlgorithm.PBKDF2WithHmacSHA256);

    }

    @Override
    public String encode(CharSequence rawPassword) {
        return pbkdf2PasswordEncoder.encode(rawPassword);
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        //兼用老版本密码
        if (encodedPassword.length() <= 32) {
            return MD5Util.getMd5String(rawPassword.toString()).equalsIgnoreCase(encodedPassword);
        } else {
            return pbkdf2PasswordEncoder.matches(rawPassword, encodedPassword);
        }
    }
}
