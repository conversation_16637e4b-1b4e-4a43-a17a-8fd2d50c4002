# -*- coding: utf-8 -*-
import io
import itertools
import json
import mimetypes
import os
import re
import sys
import subprocess
import time
import traceback
import uuid
from copy import deepcopy

if sys.version_info[0] == 2:
    python_ver = 2
    # Python 2
    from urllib import urlencode
    from urllib2 import Request, urlopen, HTTPError
else:
    # Python 3
    python_ver = 3
    from urllib.parse import urlencode
    from urllib.request import Request, urlopen
    from urllib.error import HTTPError

JAVA_CODE = """import jakarta.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class SkynetEncryption {
    private static final String RSA_ALGORITHM = "RSA";
    private static final String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl+vweogshde2kDaugvATnVoh/0SRAtSsHHvvrgot5BJJ0VgjdCetG3l5MoakQfpEIIAA9dySNhl2RVKSVnYFmnYZSIIX1ov1IIFmf6/F3d+WFcab8PvMFdz6PTNh/YgAwYo7SGHDyHtPBlTwT/ymy4dgl1SiiYhpAx0ROxICo9eDzR1/xRZ5u0sAS/XHmfxjYf8HtqDB8ekDuK1MVkCLVgw5Er8fuJb/VeF3UVaugTzQsR/6ZFArsaD/hX22zqikB5mrECbnWZNDJETpBIPKVRF2+Fzli2o6L8WmyFHQEd2pFfPmPJ1/HNMU2dAc1LVt5vqGTs/9xbrPBClDe4z7gQIDAQAB";
    private static  X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));

    public static String encrypt(String text) throws Exception {
        text = text.trim();
        byte[] result = encrypt(text.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(result), StandardCharsets.UTF_8);
    }
    public static byte[] encrypt(byte[] dataByte) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(publicKeySpec);
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(dataByte);
    }

    public static void main(String[] args) {
        try {
            System.out.print(SkynetEncryption.encrypt(args[0]));
        } catch (Exception e) {
            e.printStackTrace();
            System.exit(1);
        }
    }
}
"""

RE_ACTION_FILE = re.compile('^ACTION_FILE_(.+)$')
RE_ACTION = re.compile('^ACTION_(.+)$')
RE_FILE = re.compile('^FILE_(.+)_(\d+)$')
RE_REBOOT = re.compile('^REBOOT_(.+)$')


class Quit(Exception):

    def __init__(self, returncode=1, message=''):
        self.returncode = returncode
        self.message = message


class DeployConfig(object):

    def __init__(self, action, files=[], reboot=True, use_regex=False, clear=True):
        index_of_at = action.find('@')
        if not index_of_at > 0:
            raise Quit(1, 'invalid action point: {}, please check \'PLUGIN_NAME\' variable'.format(action))
        self.action = action
        self.plugin = action[index_of_at + 1:]
        self.files = files
        self.reboot = reboot
        self.use_regex = use_regex
        self.clear = clear

    def add_file(self, file_):
        self.files.append(file_)


class SkynetClient(object):

    def __init__(self, env):
        self.skynet_host = env['SKYNET_HOST']
        self.username = env['SKYNET_USERNAME']
        self.password = env['SKYNET_PWD']
        self.token = ""
        self.token_header = ""

    @staticmethod
    def data_from_resp(resp):
        try:
            body = resp.read()
            if resp.code != 200:
                raise Quit(1, "{}: status[{}] body[{}]".format(resp.url, resp.code, body))
            obj = json.loads(body)
            for check in ['data', 'code']:
                if check not in obj:
                    raise Quit(1, "{} not found in response".format(check))
            if obj['code'] != 0:
                raise Quit(1, "{}: code[{}] message[{}]".format(resp.url, obj['code'], obj['message']))
            return obj['data']
        finally:
            resp.close()

    @staticmethod
    def _compatible_request_data(data):
        if isinstance(data, dict) or isinstance(data, list):
            data = json.dumps(data)

        if python_ver == 2:
            # python2 需传递str类型作为data
            if not isinstance(data, str):
                data = str(data)
        else:
            # python3 需传递bytes类型作为data
            if not isinstance(data, bytes):
                data = data.encode('utf-8')
        return data

    def login(self):
        url = "/skynet/auth/login"
        data = json.dumps({"username": self.username, "password": self.password})
        resp_data = self._do_post(url, data)
        self.token = resp_data['token']
        self.token_header = resp_data["tokenHeader"]
        print('[Info] Login success.')
        return self

    def get_action_definition(self, action_code):
        url = '/skynet/api/v3/actions/definition/{}'.format(action_code)
        return self._do_get(url)

    def update_action_definition(self, action_code, definition):
        url = '/skynet/api/v3/actions/definition/{}'.format(action_code)
        ret = self._do_put(url, json.dumps(definition))
        print('[Info] {} action definition updated'.format(action_code))
        return ret

    def upload_file(self, plugin, filename, local_path):
        if not self.token:
            raise Quit(message='client has not login')
        if not os.path.exists(local_path):
            raise Quit(1, 'file {} does not exist'.format(local_path))
        url = '/skynet/api/v3/repo/files/{}'.format(plugin)
        fobj = open(local_path, 'rb')
        try:
            filesize = os.fstat(fobj.fileno()).st_size / 1048576
            start_time = time.time()
            if python_ver == 2:
                form = MultiPartFormPy2().add_file('files', filename, fobj)
                form_data = str(form)
            else:
                form = MultiPartFormPy3().add_file('files', filename, fobj)
                form_data = bytes(form)
            ret = self._do_post(url, data=form_data, headers={'Content-Type': form.get_content_type()})
            duration = time.time() - start_time
            print('[Info] upload file {} to repo[{}], duration: {} seconds, speed: {} MB/s'
                  .format(filename, plugin, round(duration, 3), round(filesize / duration, 2)))
            return ret
        except HTTPError as err:
            msg = 'POST {} : http_status[{}], body[{}]'.format(err.geturl(), err.getcode(), err.read())
            raise Quit(1, msg)
        finally:
            fobj.close()


    def get_deployment(self):
        return self._do_get('/skynet/api/v3/deployment')


    def get_repo_files(self, plugin):
        return self._do_get('/skynet/api/v3/repo/files/{}'.format(plugin))


    def delete_repo_files(self, plugin, files):
        if not files:
            return None
        ret = self._do_post('/skynet/api/v3/repo/deletion?plugin={}'.format(plugin), data=files)
        print('[Info] Deleted files under repo[{}] : {}'.format(plugin, files))
        return ret

    def reboot(self, action_point, reboot_wait=5):
        deployment = self.get_deployment()
        hosts = []
        for item in deployment:
            ip = item['ip']
            is_k8s = item['agentType'].lower() == 'kubernetes' if 'agentType' in item else False
            for action in item['actions']:
                if action_point == action['actionPoint']:
                    hosts.append({'ip': ip, 'is_k8s': is_k8s})

        if len(hosts) == 0:
            print('[Info] {} is no allocated, skip reboot...'.format(action_point))
            return

        for host in hosts:
            ip = host['ip']
            try:
                if host['is_k8s']:
                    self._do_put('/skynet/api/v3/actions/status', data=[{'ip': ip, 'actionPoint': action_point, 'enabled': False}])
                    print('[Info] {} on {} stopped, wait for {} seconds to start it...'.format(action_point, ip, reboot_wait))
                    time.sleep(reboot_wait)
                    self._do_put('/skynet/api/v3/actions/status', data=[{'ip': ip, 'actionPoint': action_point, 'enabled': True}])
                    print('[Info] {} on {} started.'.format(action_point, ip, reboot_wait))
                else:
                    self._do_post('/skynet/api/v3/actions/status/reboot', data=[{'ip': ip, 'actionPointList': [action_point]}])
                    print('[Info] {} on {} has been reboot.'.format(action_point, ip))
            except Quit as e:
                if e.message:
                    print('[Error] {}'.format(e.message))
                print('[Error] failed to reboot {} on {}'.format(action_point, ip))
            except :
                print(traceback.format_exc())
                print('[Error] failed to reboot {} on {}'.format(action_point, ip))

    def _do_get(self, url, params={}, headers={}):
        if not self.token or not self.token_header:
            raise Quit(message='client has not login')
        url = self._url(url)
        headers.update({"Content-Type": "application/json"})
        headers.update({"Authorization": "{} {}".format(self.token_header, self.token)})
        if params and len(params) > 0:
            query = urlencode(params)
            url = url + "?" + query
        request = Request(url, headers=headers)
        response = urlopen(request)
        return SkynetClient.data_from_resp(response)

    def _do_post(self, url, data, headers={}):
        if url != '/skynet/auth/login' and (not self.token or not self.token_header):
            raise Quit(message='client has not login')
        url = self._url(url)
        if 'Content-Type' not in headers:
            headers['Content-Type'] = 'application/json'
        headers.update({"Authorization": "{} {}".format(self.token_header, self.token)})

        data = SkynetClient._compatible_request_data(data)

        request = Request(url, data=data, headers=headers)
        try:
            response = urlopen(request)
        except HTTPError as err:
            msg = 'POST {} : http_status[{}], body[{}]'.format(err.geturl(), err.getcode(), err.read())
            raise Quit(1, msg)
        return SkynetClient.data_from_resp(response)

    def _do_put(self, url, data, headers={}):
        url = self._url(url)
        if 'Content-Type' not in headers:
            headers['Content-Type'] = 'application/json'
        headers.update({"Authorization": "{} {}".format(self.token_header, self.token)})

        data = SkynetClient._compatible_request_data(data)
        request = Request(url, data=data, headers=headers)
        request.get_method = lambda: 'PUT'
        try:
            response = urlopen(request)
        except HTTPError as err:
            msg = 'PUT {} : http_status[{}], body[{}]'.format(err.geturl(), err.getcode(), err.read())
            raise Quit(1, msg)
        return SkynetClient.data_from_resp(response)



    def _url(self, s):
        if not s.startswith('http'):
            return self.skynet_host + s


class MultiPartFormPy2(object):
    """
    Copied and modified from https://pymotw.com/2/urllib2/
    Accumulate the data to be used when posting a form.
    """

    def __init__(self):
        self.form_fields = []
        self.files = []
        self.boundary = str(uuid.uuid4())
        return

    def get_content_type(self):
        return 'multipart/form-data; boundary=%s' % self.boundary

    def add_field(self, name, value):
        """Add a simple field to the form data."""
        self.form_fields.append((name, value))
        return self

    def add_file(self, fieldname, filename, file_handle, mimetype=None):
        """Add a file to be uploaded."""
        body = file_handle.read()
        if mimetype is None:
            mimetype = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
        self.files.append((fieldname, filename, mimetype, body))
        return self

    def __str__(self):
        """Return a string representing the form data, including attached files."""
        # Build a list of lists, each containing "lines" of the
        # request.  Each part is separated by a boundary string.
        # Once the list is built, return a string where each
        # line is separated by '\r\n'.
        parts = []
        part_boundary = '--' + self.boundary

        # Add the form fields
        parts.extend(
            [part_boundary,
             'Content-Disposition: form-data; name="%s"' % name,
             '',
             value,
             ]
            for name, value in self.form_fields
        )

        # Add the files to upload
        parts.extend(
            [part_boundary,
             'Content-Disposition: form-data; name="%s"; filename="%s"' % \
             (field_name, filename),
             'Content-Type: %s' % content_type,
             '',
             body,
             ]
            for field_name, filename, content_type, body in self.files
        )

        # Flatten the list and add closing boundary marker,
        # then return CR+LF separated data
        flattened = list(itertools.chain(*parts))
        flattened.append('--' + self.boundary + '--')
        flattened.append('')
        return '\r\n'.join(flattened)

class MultiPartFormPy3(object):
    """
    Copied and modified from https://pymotw.com/3/urllib.request/index.html
    Accumulate the data to be used when posting a form.
    """

    def __init__(self):
        self.form_fields = []
        self.files = []
        # Use a large random byte string to separate
        # parts of the MIME data.
        self.boundary = uuid.uuid4().hex.encode('utf-8')
        return

    def get_content_type(self):
        return 'multipart/form-data; boundary={}'.format(
            self.boundary.decode('utf-8'))

    def add_field(self, name, value):
        """Add a simple field to the form data."""
        self.form_fields.append((name, value))

    def add_file(self, fieldname, filename, fileHandle,
                 mimetype=None):
        """Add a file to be uploaded."""
        body = fileHandle.read()
        if mimetype is None:
            mimetype = (
                mimetypes.guess_type(filename)[0] or
                'application/octet-stream'
            )
        self.files.append((fieldname, filename, mimetype, body))
        return self

    @staticmethod
    def _form_data(name):
        return ('Content-Disposition: form-data; '
                'name="{}"\r\n').format(name).encode('utf-8')

    @staticmethod
    def _attached_file(name, filename):
        return ('Content-Disposition: form-data; '
                'name="{}"; filename="{}"\r\n').format(
                    name, filename).encode('utf-8')

    @staticmethod
    def _content_type(ct):
        return 'Content-Type: {}\r\n'.format(ct).encode('utf-8')

    def __bytes__(self):
        """Return a byte-string representing the form data,
        including attached files.
        """
        buffer = io.BytesIO()
        boundary = b'--' + self.boundary + b'\r\n'

        # Add the form fields
        for name, value in self.form_fields:
            buffer.write(boundary)
            buffer.write(self._form_data(name))
            buffer.write(b'\r\n')
            buffer.write(value.encode('utf-8'))
            buffer.write(b'\r\n')

        # Add the files to upload
        for f_name, filename, f_content_type, body in self.files:
            buffer.write(boundary)
            buffer.write(self._attached_file(f_name, filename))
            buffer.write(self._content_type(f_content_type))
            buffer.write(b'\r\n')
            buffer.write(body)
            buffer.write(b'\r\n')

        buffer.write(b'--' + self.boundary + b'--\r\n')
        return buffer.getvalue()

def parse_env():
    env = {}
    prefix_list = ['SKYNET_', 'PLUGIN_NAME', 'ACTION_', 'FILE_', 'REBOOT', 'CLEAR', 'USE_REGEX']
    for k, v in os.environ.items():
        if len(list(filter(lambda prefix: k.startswith(prefix), prefix_list))) > 0:
            env[k] = v
    if 'SKYNET_HOST' not in env:
        env['SKYNET_HOST'] = 'http://localhost:2230'
    else:
        if not env['SKYNET_HOST'].startswith('http'):
            env['SKYNET_HOST'] = 'http://{}'.format(env['SKYNET_HOST'])
    if 'SKYNET_USER' not in env:
        env['SKYNET_USERNAME'] = 'admin'
    if 'SKYNET_PWD' not in env:
        env['SKYNET_PWD'] = 'Skynet@2230'
    for k, v in env.items():
        print('(Environment) {} : {}'.format(k, v))
    return env


def parse_deploy_config(env):
    def to_bool(_bool_key, default_val=True):
        _env_value = env.get(_bool_key)
        return not (_env_value.lower() == 'false') if _env_value else default_val

    def parse_action_name(_s):
        idx_of_at = _s.find('@')
        return _s[:idx_of_at] if idx_of_at > 0 else _s

    plugin_name = env.get('PLUGIN_NAME')
    reboot = to_bool('REBOOT', True)
    use_regex = to_bool('USE_REGEX', False)
    clear = to_bool('CLEAR', True)
    id2code = dict()
    id2files = dict()

    for k, v in env.items():
        m = RE_ACTION_FILE.match(k)
        if m:
            identifier = m.groups()[0]
            code_and_file = v.split(':')
            id2code[identifier] = code_and_file[0].strip()
            if len(code_and_file) < 2:
                raise Quit(message='Value of Config [{}] is not valid'.format(k))
            id2files[identifier] = [code_and_file[1].strip()]
            continue
        m = RE_ACTION.match(k)
        if m:
            identifier = m.groups()[0]
            id2code[identifier] = v.strip()
            continue
        m = RE_FILE.match(k)
        if m:
            identifier = m.groups()[0]
            if identifier not in id2files:
                files = []
                id2files[identifier] = files
            files.append(v.strip())
            continue

    ret = []
    for id_ in id2code:
        action_code = id2code[id_]
        files = id2files.get(id_)
        if not files or len(files) == 0:
            files = [parse_action_name(action_code)]
        if plugin_name and '@' not in action_code:
            action_code = '{}@{}'.format(action_code, plugin_name)
        config = DeployConfig(action_code, files, reboot=reboot, use_regex=use_regex, clear=clear)
        ret.append(config)
    return ret


def runtime_check():
    print('work dir: {}'.format(os.path.abspath(os.getcwd())))
    print('python version: {}'.format('.'.join(map(lambda i: str(i), sys.version_info[0:3]))))
    try:
        proc = subprocess.Popen(['java'], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                shell=False)
        proc.communicate()
        print('java has been installed')
        proc = subprocess.Popen(['javac'], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                shell=False)
        proc.communicate()
        print('javac has been installed')
    except OSError as e:
        raise Quit(message='java or javac is not installed')
    print('runtime_check passed')


def get_out(code):
    print('exit')
    sys.exit(code)


def encrypt_pwd(env):
    encrypted_pwd = encrypt(env['SKYNET_PWD'])
    env['SKYNET_PWD'] = encrypted_pwd
    print('encrypted password : {}'.format(encrypted_pwd))


def encrypt(s):
    with open('./SkynetEncryption.java', 'w+') as fobj:
        fobj.write(JAVA_CODE)
    if not os.path.exists('./SkynetEncryption.class'):
        javac_proc = subprocess.Popen(['javac', 'SkynetEncryption.java'], shell=False)
        out, err = javac_proc.communicate()
        if javac_proc.returncode == 0:
            print('javac compile success')
        else:
            print(out if out else err)
            raise Quit(message='fail to compile java code')
    java_proc = subprocess.Popen(['java', 'SkynetEncryption', s], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 shell=False)
    out, err = java_proc.communicate()
    if java_proc.returncode == 0:
        return out.decode('utf-8') if isinstance(out, bytes) else out
    print(out if out else err)
    raise Quit('''fail to execute 'java SkynetEncryption' ''')


def definition_replace(definition, deploy_config, repo_filenames):
    def _match(_s, _pattern):
        if deploy_config.use_regex:
            return re.match(_pattern, _s) is not None
        return _pattern in _s

    def _parse_ref_filename(_s):
        """
        解析文件关联配置， 返回元组，第一个元素为前缀，第二个元素为文件名
        :param _s:
        :return:
        """
        idx_of_colon = _s.find(':')
        if idx_of_colon < 0:
            return  '', _s
        if idx_of_colon == (len(_s) - 1):
            return _s, ''
        return _s[:idx_of_colon+1], _s[idx_of_colon+1:]

    if 'referencedFiles' not in definition:
        print('''[Warn] No 'referencedFiles' field in action definition, skip {}''', deploy_config.action)
    if 'startupConfig' not in definition:
        print('''[Warn] No 'startupConfig' field in action definition, skip {}''', deploy_config.action)

    new_definition = deepcopy(definition)
    upload_files = []
    local_files = os.listdir('.')
    # print('[Info] local files: {}'.format(local_files))
    for file_pattern in deploy_config.files:
        matched_local_files = list(filter(lambda _lf: _match(_lf, file_pattern), local_files))
        if not len(matched_local_files) == 1:
            print('[Warn] matched local file for action[{}], pattern[{}] : {}'.format(deploy_config.action,
                  file_pattern, matched_local_files))
            print('[Warn] Skip this action...')
            continue
        matched_local_file = matched_local_files[0]
        matched_ref_files = list(
            filter(lambda _rf: _match(_parse_ref_filename(_rf['fileName'])[1], file_pattern), new_definition['referencedFiles']))
        if not len(matched_ref_files) == 1:
            print('[Warn] matched \'referencedFiles\' for action[{}], pattern[{}] : {}'.format(deploy_config.action,
                  file_pattern, matched_ref_files))
            print('[Warn] Skip this action...')
            continue
        upload_files.append(matched_local_file)
        matched_ref_filename = matched_ref_files[0]['fileName']
        matched_ref_files[0]['fileName'] = _parse_ref_filename(matched_ref_filename)[0] + matched_local_file
        print('[Info] Referenced file replacement for {} : {} -> {}'.format(deploy_config.action, matched_ref_filename,
                                                                            matched_ref_files[0]['fileName']))

        if 'runnableJar' in new_definition['startupConfig'] and _match(new_definition['startupConfig']['runnableJar'],
                                                                      file_pattern):
            print('[Info] Runnable jar replacement for {} : {} -> {}'.format(deploy_config.action,
                                                                             new_definition['startupConfig'][
                                                                                 'runnableJar'],
                                                                             matched_local_file))
            new_definition['startupConfig']['runnableJar'] = matched_local_file

    # 模式匹配但是不在关联文件中，判定为过期文件
    remove_files = []
    all_ref_filenames = list(map(lambda _f: _f['fileName'], new_definition['referencedFiles']))
    all_ref_filenames = list(map(lambda _name: _parse_ref_filename(_name)[1], all_ref_filenames))
    for file_pattern in deploy_config.files:
        remove_files.extend(list(filter(lambda _rf: _match(_rf, file_pattern) and _rf not in all_ref_filenames
                                                    and not _rf.endswith('.md5'), repo_filenames)))
    return new_definition, upload_files, remove_files


def main():
    start_time = time.time()
    exit_code = 0
    try:
        runtime_check()
        env = parse_env()
        encrypt_pwd(env)
        configs = parse_deploy_config(env)
        # print(json.dumps(configs, indent=4, default=lambda obj: obj.__dict__))
        client = SkynetClient(env).login()
        for config in configs:
            print('')
            print('------------- process {} -------------'.format(config.action))
            action_def = client.get_action_definition(config.action)
            repo_files = list(map(lambda _f: _f['fileName'], client.get_repo_files(config.plugin)))
            new_action_def, upload_files, remove_files = definition_replace(action_def, config, repo_files)
            for upload_file in upload_files:
                client.upload_file(config.plugin, upload_file, './{}'.format(upload_file))
            if upload_files:
                client.update_action_definition(config.action, new_action_def)

            if config.clear and remove_files:
                client.delete_repo_files(config.plugin, remove_files)
            print('')

        for config in configs:
            if config.reboot:
                client.reboot(config.action)

        print('\nSuccess finish')
    except Quit as e:
        if e.message:
            print('[Error] {}'.format(e.message))
        exit_code = e.returncode
    except :
        print(traceback.format_exc())
        exit_code = 2
    finally:
        print('Cost {} seconds'.format(round(time.time() - start_time, 1)))
        if exit_code !=0:
            get_out(exit_code)



if __name__ == '__main__':
    main()
