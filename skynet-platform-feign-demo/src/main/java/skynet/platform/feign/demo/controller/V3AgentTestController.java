package skynet.platform.feign.demo.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.platform.feign.service.V3Agent;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class V3AgentTestController {

    private final V3Agent v3Agent;

    public V3AgentTestController(V3Agent v3Agent) {
        this.v3Agent = v3Agent;
    }


    @GetMapping("/agent")
    public Object agent() {
        V3Agent.GetAgentsResponse ret = v3Agent.getAgents();
        return ret;
    }
}
