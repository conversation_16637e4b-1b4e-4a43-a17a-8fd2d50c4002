package skynet.platform.agent.server;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.unit.DataSize;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OperatingSystem;
import skynet.boot.AppContext;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.platform.common.AppOfflineChecker;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.domain.AntServerProperty;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.domain.GpuItem;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.observer.ServerParamObserver;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.common.repository.domain.AntNodeUpType;
import skynet.platform.common.xray.Gpu;
import skynet.platform.common.xray.domain.NvidiaGPU;

import java.io.IOException;
import java.util.*;

/**
 * ServerContext
 * <p>
 * 职责：根据 nodeName 侦听Zookeeper ，
 * <p>
 * 负责启动、重启（Worker配置变化时）、停止Worker 向ZooKeeper汇报自己的状态
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public final class AntServerContext implements AutoCloseable {

    private final SkynetProperties skynetProperties;
    private final SkynetZkProperties skynetZkProperties;

    private final AntServer antServer;
    private final AppOfflineChecker appOfflineChecker;
    private final IAntConfigService antConfigService;

    @Getter
    private AntNodeState antNodeState;
    private volatile AntServerParam currentAntServerParam = null;

    public AntServerContext(AntServer antServer, SkynetProperties skynetProperties,
                            SkynetZkProperties skynetZkProperties, AppOfflineChecker appOfflineChecker,
                            IAntConfigService antConfigService) {
        this.antServer = antServer;
        this.skynetProperties = skynetProperties;
        this.skynetZkProperties = skynetZkProperties;
        this.appOfflineChecker = appOfflineChecker;
        this.antConfigService = antConfigService;
    }

    public void start(AppContext appContext) throws Exception {

        this.antNodeState = new AntNodeState(appContext, skynetZkProperties);

        // 启动
        antNodeState.setUp(AntNodeUpType.LOADING);

        this.currentAntServerParam = this.antConfigService.getServerParam(appContext.getIpAddress(),
                new ServerParamObserver() {
                    @Override
                    public void update(AntServerParam antServerParam) {

                        log.info("The ServerParam have been changed...");
                        log.info("The ServerParam is: [{}]", antServerParam);

                        if (antServerParam == null) {
                            exist();
                            return;
                        }

                        log.info("Changed after  actions={}", JSON.toJSONString(antServerParam.getActions()));
                        log.info("Changed before actions={}", JSON.toJSONString(currentAntServerParam.getActions()));

                        // 如果
                        if (JSON.toJSONString(antServerParam.getActions()).equals(JSON.toJSONString(currentAntServerParam.getActions()))) {
                            log.info("The actions haven't changed.");
                            return;
                        }
                        antServerParam.setIp(appContext.getIpAddress());
                        antServer.reload(antServerParam);
                        currentAntServerParam = antServerParam;
                    }
                });

        if (currentAntServerParam == null) {
            if (skynetProperties.isDebugMode()) {
                currentAntServerParam = new AntServerParam();
                currentAntServerParam.setIp(appContext.getIpAddress());
            } else {
                exist();
                return;
            }
        }

        //每次启动都更新
        AntServerProperty antServerProperty = fetchServerProperty(appContext, currentAntServerParam);
        currentAntServerParam.setIp(appContext.getIpAddress());
        currentAntServerParam.setSys(antServerProperty);

        log.debug("AntServerParam= {} ", currentAntServerParam);
        antNodeState.setNodeParam(currentAntServerParam);

        // 保存 服务器硬件配置
        this.antConfigService.setServerParam(currentAntServerParam);
        log.info("The system info= {}", antServerProperty);

        antServer.start(currentAntServerParam, antNodeState);
        appOfflineChecker.check();
    }

    private void exist() {
        log.error("\r\n================\r\nantServerParam is null，this server-node [ip={}] is no registration or deleted.\r\n================", skynetProperties.getIpAddress());
        System.exit(-1);
    }

    private AntServerProperty fetchServerProperty(AppContext appContext, AntServerParam currentAntServerParam) {
        AntServerProperty antServerProperty = currentAntServerParam.getSys();
        if (antServerProperty == null) {
            antServerProperty = new AntServerProperty();
        }
        log.info("Update system info ...");
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hal = systemInfo.getHardware();

        antServerProperty.setHost(OsUtil.getHostName());
        antServerProperty.setIp(appContext.getIpAddress());
        antServerProperty.setMem(DataSize.ofBytes(hal.getMemory().getTotal()).toGigabytes());
        antServerProperty.setCpu(Runtime.getRuntime().availableProcessors());
        antServerProperty.setArch(hal.getProcessor().getProcessorIdentifier().getMicroarchitecture());
        //antServerProperty.setOs(String.format("%s [%s]", OperatingSystem.getInstance().getDescription(), OperatingSystem.getInstance().getVersion()));
        OperatingSystem os = systemInfo.getOperatingSystem();
        antServerProperty.setOs(String.format("%s %s", os.getFamily(), os.getVersionInfo()));

        List<NvidiaGPU> gpus = Gpu.getGraphicsCard();
        if (!gpus.isEmpty()) {
            antServerProperty.setGpu(String.format("%s*%d", gpus.getFirst().getName(), gpus.size()));
            List<GpuItem> gpuList = new ArrayList<>(gpus.size());
            gpus.forEach(x -> gpuList.add(new GpuItem(x.getIndex(), x.getName(), x.getMemoryTotal())));
            antServerProperty.setGpus(gpuList);
        }
        return antServerProperty;
    }

    @Override
    public void close() throws Exception {
        antServer.stop();
    }

    public Map<String, Object> getStateDetail() throws IOException {

        Collection<BootStatus> bootList = antServer.getBootStatusList();
        Map<String, Object> map = new HashMap<>(2);
        map.put("onlineworker_map", bootList);
        map.put("onlineworker_size", bootList.size());
        return map;
    }
}
