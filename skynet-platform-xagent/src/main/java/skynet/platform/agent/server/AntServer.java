package skynet.platform.agent.server;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import skynet.boot.AppContext;
import skynet.platform.agent.core.BootService;
import skynet.platform.agent.core.ScheduledService;
import skynet.platform.common.domain.*;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.common.repository.domain.AntNodeUpType;

import java.io.IOException;
import java.util.Collection;
import java.util.LinkedHashMap;

/**
 * AntServer
 * <p>
 * TODO: 需要重构，通过服务调用的方式停止Worker。
 *
 *
 * <p>
 * jdk 有bug 不能destroy http://bugs.java.com/bugdatabase/view_bug.do?bug_id=4770092
 * </P>
 *
 * <pre>
 * 从ZK中获取当前服务器的Action列表，并逐一启动，同时订阅ZK，观察Action是否 被修改，如果被修改
 *
 * </pre>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public final class AntServer {

    private final AppContext appContext;
    private final ScheduledService scheduledService;
    private final BootService bootService;

    @Getter
    private AntNodeState nodeState;

    public AntServer(AppContext appContext, ScheduledService scheduledService, BootService bootService) {
        this.appContext = appContext;
        this.scheduledService = scheduledService;
        this.bootService = bootService;
    }

    public Collection<BootStatus> getBootStatusList() throws IOException {
        return bootService.getAllBoot().values();
    }

    /**
     * 根据 AntServerParam 启动 AntWorker
     *
     * @param antServerParam
     * @throws Exception
     */
    public void start(AntServerParam antServerParam, AntNodeState nodeState) {
        log.info("Start server [param={}]...", antServerParam);
        this.nodeState = nodeState;
        nodeState.setUp(AntNodeUpType.UP);
        // 异步处理 启动托管的Boot
        new Thread(() -> {
            long cost = System.currentTimeMillis();
            load(antServerParam);
            log.info("start server end.[cost={}]", System.currentTimeMillis() - cost);
        }).start();
    }

    public void reload(AntServerParam antServerParam) {
        long cost = System.currentTimeMillis();
        log.info("Reload server [param={}]...", antServerParam);
        load(antServerParam);
        log.info("Reload server end.[cost={}]", System.currentTimeMillis() - cost);
    }

    private synchronized void load(AntServerParam antServerParam) {

        log.info("Load server  begin [action size={}]...", antServerParam.getActions().size());
        try {
            LinkedHashMap<String, BootProfile> scheduledBootMap = new LinkedHashMap<>();

            int index = 0;
            for (AntActionRegist antActionRegist : antServerParam.getActions()) {
                for (BootAction bootAction : antActionRegist.toBootActionList(appContext.getIpAddress())) {
                    if (bootAction.isEnable()) {
                        BootProfile bootProfile = JSON.parseObject(bootAction.toJson(), BootProfile.class);
                        bootProfile.setIndex(index);
                        scheduledBootMap.put(bootAction.getAid(), bootProfile);
                    }
                    index++;
                }
            }
            scheduledService.start(scheduledBootMap);
        } catch (Exception e) {
            log.error("Load server exception", e);
        }

        log.info("Load server end [action size={}]...", antServerParam.getActions().size());
    }

    public void stop() throws Exception {
        scheduledService.close();
        log.info("stop server end.");
    }
}
