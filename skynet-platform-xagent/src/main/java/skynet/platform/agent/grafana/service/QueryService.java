package skynet.platform.agent.grafana.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import skynet.boot.i18n.MessageUtils;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;
import skynet.platform.agent.grafana.domain.*;
import skynet.platform.agent.grafana.exception.InvalidQueryException;
import skynet.platform.agent.grafana.i18n.MessageEnum;
import skynet.platform.common.xray.domain.*;

import java.util.*;

@Slf4j
@Service
@ConditionOnGrafana
public class QueryService {

    // 集群信息聚合
    private static final String TARGET_CLUSTER = "cluster";

    // 集群信息摘要
    private static final String TARGET_DIGEST = "digest";

    // 服务器磁盘信息
    private static final String TARGET_DISK = "disk";

    // 服务器网卡信息
    private static final String TARGET_NETWORK = "network";

    // 服务器显卡信息
    private static final String TARGET_GPU = "gpu";

    // 服务器连接信息
    private static final String TARGET_CONNECTION = "connection";

    // 服务器CPU信息
    private static final String TARGET_CPU = "cpu";

    // 服务器内存信息
    private static final String TARGET_MEM = "mem";
    private static final Set<String> DEV_NAME_IGNORE = new HashSet<>();

    static {
        DEV_NAME_IGNORE.add("overlay");
        DEV_NAME_IGNORE.add("shm");
        DEV_NAME_IGNORE.add("/dev/shm");
        DEV_NAME_IGNORE.add("tmpfs");
        DEV_NAME_IGNORE.add("rootfs");
    }

    private final QueryCache queryCache;

    public QueryService(QueryCache queryCache) {
        this.queryCache = queryCache;
    }

    public List<Object> query(List<QueryTarget> queryTargets) throws Exception {
        List<Object> ret = new ArrayList<>(1);
        /**
         * 暂时只支持单个target且类型为table
         */
        QueryTarget queryTarget = queryTargets.getFirst();
        if (!QueryTarget.TABLE.equals(queryTarget.getType())) {
            throw new InvalidQueryException("Unsupported type : " + queryTarget.getType());
        }
        ParsedTarget parsedTarget = ParsedTarget.parse(queryTarget.getTarget());
        String target = parsedTarget.getTarget();

        Table table;

        if (TARGET_CLUSTER.equals(target)) {
            table = this.queryClusterSummary();
        } else if (TARGET_DIGEST.equals(target)) {
            table = this.queryDigest();
        } else {
            String ip = parsedTarget.getAttribute("ip");
            if (StringUtils.isBlank(ip)) {
                throw new InvalidQueryException("No ip attribute in query request for " + target);
            }
            if (TARGET_DISK.equals(target)) {
                Integer maxDirNameLen = parsedTarget.getIntAttribute("maxDirNameLen");
                log.debug("maxDirNameLen ={}", maxDirNameLen);
                table = this.queryDisk(ip, (maxDirNameLen == null) ? Integer.MAX_VALUE : maxDirNameLen);
            } else if (TARGET_NETWORK.equals(target)) {
                table = this.queryNetwork(ip);
            } else if (TARGET_GPU.equals(target)) {
                table = this.queryGPU(ip);
            } else if (TARGET_CPU.equals(target)) {
                table = this.queryCPU(ip);
            } else if (TARGET_CONNECTION.equals(target)) {
                table = this.queryConnection(ip);
            } else if (TARGET_MEM.equals(target)) {
                table = this.queryMem(ip);
            } else {
                throw new InvalidQueryException("Unsupported query target : " + target);
            }
        }
        ret.add(table);
        return ret;
    }

    private Table queryClusterSummary() throws Exception {
        Table table = new Table();
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.SERVER_NODE_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CPU_CORE_LOGICAL_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.LOAD_SYSTEM_AVERAGE_PER_CORE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CPU_USAGE_PERCENTAGE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_CAPACITY_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_USAGE_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_USAGE_PERCENTAGE_AVERAGE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_CARD_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_CARD_LOAD_AVERAGE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_MEMORY_CAPACITY_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_MEMORY_USAGE_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_MEMORY_USAGE_PERCENTAGE_AVERAGE)));
        ClusterSummary clusterSummary = this.doQueryClusterSummary();
        table.addRow(Arrays.asList(clusterSummary.getServerNodeNum(), clusterSummary.getCpuCores(), clusterSummary.getAvgLoad15m() / clusterSummary.getCpuCores(), clusterSummary.getCpuUsePerc(),
                clusterSummary.getMemTotal(), clusterSummary.getMemUsed(), clusterSummary.getMemUsePerc(), clusterSummary.getGpuNum(), clusterSummary.getGpuUsePerc(), clusterSummary.getGpuMemTotal(),
                clusterSummary.getGpuMemUsed(), clusterSummary.getGpuMemUsePerc()));
        return table;
    }

    private String getMessage(MessageEnum messageEnum) {
        return MessageUtils.getMessage(messageEnum.getKey());
    }

    private Table queryDigest() throws Exception {
        Table table = new Table();
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.ADDRESS_IP)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CPU_CORE_LOGICAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.LOAD_SYSTEM_AVERAGE_PER_CORE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_CAPACITY)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_USAGE_PERCENTAGE_ACTUAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.DISK_CAPACITY_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.DISK_SPACE_USAGE1)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.DISK_SPACE_USAGE2)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.DISK_SPACE_USAGE3)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_CARD)));
        Map<String, Integer> ipPortMap = this.queryCache.getAllIpPort();
        for (String ip : ipPortMap.keySet()) {
            try {
                CpuStat cpuStat = this.query2server(ip, TARGET_CPU, CpuStat.class);
                MemStat memStat = this.query2server(ip, TARGET_MEM, MemStat.class);
                List<DiskStat> diskStats = this.query2server(ip, TARGET_DISK, new TypeReference<List<DiskStat>>() {
                });
                List<GpuStat> gpuStats = this.query2server(ip, TARGET_GPU, new TypeReference<List<GpuStat>>() {
                });
                double avgLoad15m = (cpuStat.getLoadAvg() != null && cpuStat.getLoadAvg().length > 2) ? cpuStat.getLoadAvg()[2] : -1;
                diskStats = diskStats.stream().filter(e -> e.getDevName() != null && !DEV_NAME_IGNORE.contains(e.getDevName())).toList();
                long diskCapacityTotal = diskStats.stream().mapToLong(DiskStat::getCapacityTotal).sum();
                diskStats.sort((DiskStat d1, DiskStat d2) -> (int) ((d2.getCapacityUsePercent() - d1.getCapacityUsePercent()) * 100));
                int diskStatsSize = diskStats.size();
                double diskUsagePerc1 = (diskStatsSize > 0) ? diskStats.getFirst().getCapacityUsePercent() : -1;
                double diskUsagePerc2 = (diskStatsSize > 1) ? diskStats.get(1).getCapacityUsePercent() : -1;
                double diskUsagePerc3 = (diskStatsSize > 2) ? diskStats.get(2).getCapacityUsePercent() : -1;
                StringBuilder gpuDesc = new StringBuilder();
                Map<String, Integer> gpuModel2Count = new HashMap<>();
                for (GpuStat stat : gpuStats) {
                    if (stat.getStat() == null) {
                        continue;
                    }
                    String type = stat.getStat().get("name").toString();
                    type = (StringUtils.isBlank(type)) ? "unknown" : type;
                    Integer countObj = gpuModel2Count.get(type);
                    int count = (countObj == null) ? 0 : countObj;
                    gpuModel2Count.put(type, ++count);
                }
                gpuModel2Count.forEach((k, v) -> gpuDesc.append(k).append("*").append(v).append(" "));
                table.addRow(Arrays.asList(ip, cpuStat.getCoresTotal(), avgLoad15m / cpuStat.getCoresTotal(), memStat.getTotal(), memStat.getActualUsedPercent(), diskCapacityTotal, diskUsagePerc1,
                        diskUsagePerc2, diskUsagePerc3, gpuDesc.toString()));
            } catch (Exception e) {
                log.error("fail to query digest from {} , cause[{}]", ip, e.getMessage());
                log.debug("", e);
            }

        }

        return table;
    }

    private Table queryDisk(String ip, int maxLen) throws Exception {
        Table table = new Table();
        // table.addColumn(new TableColumn(this.getMessage(MessageEnum.NAME_DEVICE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.POINT_MOUNT)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.TYPE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.SPACE_MAX)));
        // table.addColumn(new TableColumn(this.getMessage(MessageEnum.SPACE_USAGE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.SPACE_USAGE_PERCENTAGE)));
        // table.addColumn(new TableColumn(this.getMessage(MessageEnum.THROUGHPUT_READ)));
        // table.addColumn(new TableColumn(this.getMessage(MessageEnum.THROUGHPUT_WRITE)));
        List<DiskStat> diskStats = this.query2server(ip, TARGET_DISK, new TypeReference<List<DiskStat>>() {
        });
        for (DiskStat diskStat : diskStats) {
            String dirName = diskStat.getDirName();
            if (dirName.length() > maxLen) {
                dirName = dirName.substring(0, maxLen) + "...";
            }
            table.addRow(Arrays.asList(dirName, diskStat.getFsType(), diskStat.getCapacityTotal(), diskStat.getCapacityUsePercent()));
        }
        return table;
    }

    private Table queryNetwork(String ip) throws Exception {
        Table table = new Table();
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.NAME_NETWORK_CARD)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.ADDRESS)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.ADDRESS_MAC)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.RATE_UPLOAD)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.RATE_DOWNLOAD)));
        List<NetworkStat> networkStats = this.query2server(ip, TARGET_NETWORK, new TypeReference<List<NetworkStat>>() {
        });
        for (NetworkStat networkStat : networkStats) {
            table.addRow(Arrays.asList(networkStat.getDevName(), networkStat.getAddress(), networkStat.getHwAddress(), networkStat.getRxSpeed(), networkStat.getTxSpeed()));
        }
        return table;
    }

    private Table queryGPU(String ip) throws Exception {
        Table table = new Table();
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.INDEX)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_MEMORY_CAPACITY_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_MEMORY_USAGE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.GRAPHICS_MEMORY_USAGE_PERCENTAGE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.LOAD)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.TYPE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.NUMBER_SERIAL)));
        List<GpuStat> gpuStats = this.query2server(ip, TARGET_GPU, new TypeReference<List<GpuStat>>() {
        });
        for (GpuStat gpuStat : gpuStats) {
            Map<String, Object> statMap = gpuStat.getStat();
            String index = statMap.get("index").toString();
            long memTotal = Long.parseLong("" + statMap.get("memory.total"));
            long memUsed = Long.parseLong("" + statMap.get("memory.used"));
            String type = statMap.get("name").toString();
            String serial = statMap.get("serial").toString();
            table.addRow(Arrays.asList(index, memTotal, memUsed, (double) memUsed / memTotal, Double.parseDouble("" + statMap.get("utilization.gpu")), type, serial));
        }
        return table;
    }

    private Table queryCPU(String ip) throws Exception {
        Table table = new Table();
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.NUMBER_MODEL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.QUANTITY)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.FREQUENCY)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CPU_CORE_LOGICAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.LOAD_SYSTEM_AVERAGE_PER_CORE)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CPU_USAGE_PERCENTAGE)));
        CpuStat cpuStat = this.query2server(ip, TARGET_CPU, CpuStat.class);
        double avgLoad15m = (cpuStat.getLoadAvg() != null && cpuStat.getLoadAvg().length > 2) ? cpuStat.getLoadAvg()[2] : -1;
        table.addRow(Arrays.asList(cpuStat.getModel(), cpuStat.getCpuNum(), cpuStat.getMhz() + "Mhz", cpuStat.getCoresTotal(), avgLoad15m / cpuStat.getCoresTotal(), cpuStat.getCombined()));
        return table;
    }

    private Table queryConnection(String ip) throws Exception {
        Table table = new Table();
        ConnectionStat connectionStat = this.query2server(ip, TARGET_CONNECTION, ConnectionStat.class);
//        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_TOTAL_INCOMING)));
//        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_TOTAL_OUTGOING)));
//        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_TOTAL_TCP_INCOMING)));
//        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_TOTAL_TCP_OUTGOING)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_TOTAL_TCP)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_COUNT_LISTEN)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_COUNT_ESTABLISHED)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_COUNT_TIME_WAIT)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_COUNT_CLOSE_WAIT)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.CONNECTIONS_COUNT_SYN_SENT)));

        table.addRow(Arrays.asList(
//                connectionStat.getAllInboundTotal(),
//                connectionStat.getAllOutboundTotal(),
//                connectionStat.getTcpInboundTotal(),
//                connectionStat.getTcpOutboundTotal(),
                        connectionStat.getTcpTotal(),
                        connectionStat.getTcpListen(),
                        connectionStat.getTcpEstablised(),
                        connectionStat.getTcpTimeWait(),
                        connectionStat.getTcpCloseWait(),
                        connectionStat.getTcpSynSent())
        );
        return table;
    }

    private Table queryMem(String ip) throws Exception {
        Table table = new Table();
        MemStat memStat = this.query2server(ip, TARGET_MEM, MemStat.class);
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_CAPACITY)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_USAGE_TOTAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_USAGE_ACTUAL)));
        table.addColumn(new TableColumn(this.getMessage(MessageEnum.MEMORY_USAGE_PERCENTAGE_ACTUAL)));
        table.addRow(Arrays.asList(memStat.getTotal(), memStat.getUsed(), memStat.getActualUsed(), memStat.getActualUsedPercent()));
        return table;
    }

    private ClusterSummary doQueryClusterSummary() throws Exception {
        ClusterSummary clusterSummary = new ClusterSummary();
        int cpuCores = 0;
        long memTotal = 0L;
        long memUsed = 0L;
        double cpuUsePerc = 0.0;
        double avgLoad15m = 0.0;
        int gpuNum = 0;
        long gpuMemTotal = 0L;
        long gpuMemUsed = 0L;
        double gpuUsePerc = 0.0;
        Map<String, Integer> ipPortMap = this.queryCache.getAllIpPort();
        int nodeNum = ipPortMap.size();
        for (Map.Entry<String, Integer> entry : ipPortMap.entrySet()) {
            String ip = entry.getKey();
            try {
                CpuStat cpuStat = this.query2server(ip, TARGET_CPU, CpuStat.class);
                MemStat memStat = this.query2server(ip, TARGET_MEM, MemStat.class);
                List<GpuStat> gpuStatList = this.query2server(ip, TARGET_GPU, new TypeReference<List<GpuStat>>() {
                });
                cpuCores += cpuStat.getCoresTotal();
                cpuUsePerc += cpuStat.getCombined();
                avgLoad15m += ((cpuStat.getLoadAvg() != null && cpuStat.getLoadAvg().length > 2) ? cpuStat.getLoadAvg()[2] : 0);
                memTotal += memStat.getTotal();
                memUsed += memStat.getActualUsed();
                gpuNum += gpuStatList.size();
                for (GpuStat gpuStat : gpuStatList) {
                    Map<String, Object> statMap = gpuStat.getStat();
                    gpuMemTotal += Long.parseLong("" + statMap.get("memory.total"));
                    gpuMemUsed += Long.parseLong("" + statMap.get("memory.used"));
                    gpuUsePerc += Double.parseDouble("" + statMap.get("utilization.gpu"));
                }
            } catch (Exception e) {
                log.error("fail to query summary from {} , cause[{}]", ip, e.getMessage());
                log.debug("", e);
            }
        }
        clusterSummary.setServerNodeNum(nodeNum);
        clusterSummary.setCpuCores(cpuCores);
        clusterSummary.setMemTotal(memTotal);
        clusterSummary.setMemUsed(memUsed);
        clusterSummary.setCpuUsePerc((nodeNum == 0) ? 0.0 : (cpuUsePerc / nodeNum));
        clusterSummary.setAvgLoad15m((nodeNum == 0) ? 0.0 : (avgLoad15m / nodeNum));
        clusterSummary.setMemUsePerc((memTotal == 0) ? 0.0 : ((double) memUsed / memTotal));
        clusterSummary.setGpuNum(gpuNum);
        clusterSummary.setGpuMemTotal(gpuMemTotal);
        clusterSummary.setGpuMemUsed(gpuMemUsed);
        clusterSummary.setGpuMemUsePerc((gpuMemTotal == 0) ? 0.0 : ((double) gpuMemUsed / gpuMemTotal));
        clusterSummary.setGpuUsePerc((nodeNum == 0) ? 0.0 : (gpuUsePerc / nodeNum));
        return clusterSummary;
    }

    private <T> T query2server(String ip, String target, TypeReference<T> typeReference) throws Exception {

        String body = this.doQuery2Server(ip, target);
        return JSON.parseObject(body, typeReference);
    }

    private <T> T query2server(String ip, String target, Class<T> clazz) throws Exception {
        String body = this.doQuery2Server(ip, target);
        return JSON.parseObject(body, clazz);
    }

    private String doQuery2Server(String ip, String target) throws Exception {
        Map<String, Integer> ipPortMap = this.queryCache.getAllIpPort();
        Integer port = ipPortMap.get(ip);
        if (port == null) {
            throw new InvalidQueryException(ip + " is not found in online server list");
        }
        String uri = String.format("http://%s:%d/skynet/agent/sysinfo/%s", ip, port, target);
        return this.queryCache.query(uri);
    }

    // public static void main(String[] args) throws InvalidQueryException,
    // TraceableRestClientException {
    // QueryService queryService = new QueryService();
    // queryService.ipPortCache.put("**************", 6230);
    // queryService.restTemplate = new RestTemplate();
    // String body = queryService.doQuery2Server("**************", TARGET_DISK);
    // System.out.println(body);
    // List<DiskStat> stats = JSON.parseObject(body, new
    // TypeReference<List<DiskStat>>(){});
    // System.out.println(JSON.toJSONString(stats, true));
    // }
}
