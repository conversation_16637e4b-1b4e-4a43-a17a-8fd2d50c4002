package skynet.platform.agent.grafana.loader;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.security.config.SkynetBaseAuthProperties;
import skynet.platform.agent.grafana.GrafanaProperties;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;
import skynet.platform.agent.grafana.client.GrafanaClient;
import skynet.platform.agent.grafana.domain.RepoFileDo;
import skynet.platform.common.repository.OnlineActionManager;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * grafana资源加载器
 *
 * <AUTHOR>
 * @since 2018年12月11日 上午10:17:12
 */
@Slf4j
@Component
@ConditionOnGrafana
public class GrafanaLoader implements ApplicationListener<ContextRefreshedEvent> {

    private final Map<String, Dashboard> dashboardCache = new HashMap<>();

    @Autowired
    private SkynetProperties skynetProperties;
    @Autowired
    private GrafanaProperties grafanaProps;
    @Autowired
    private DashboardRepository dashboardRepo;
    @Autowired
    private GrafanaClient grafanaClient;
    @Autowired
    private DashboardUploader dashboardUploader;
    @Autowired
    private SkynetBaseAuthProperties securityProperties;
    @Autowired
    private OnlineActionManager onlineActionManager;

    /**
     * Endpoint 所需数据
     */
    @Getter
    private List<RepoFileDo> dashboardFiles;
    @Getter
    private List<AntActionStatus> onlineGrafanaList;
    @Getter
    private AntActionStatus onlineGrafanaOnLocal;
    @Getter
    private List<String> dashboardUidInCache;
    @Getter
    private volatile boolean running = true;
    private boolean initFlag = false;

    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {
        if (!initFlag) {
            log.debug("GrafanaLoader init...");
            try {
                ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1, r -> new Thread(r, "GrafanaLoader-Thread"));
                executorService.scheduleAtFixedRate(new LoadTask(), 30, this.grafanaProps.getLoadInterval(), TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("", e);
            }
            initFlag = true;
        }
    }

    private AntActionStatus grafanaOnLocalServer() {
        this.onlineGrafanaList = onlineActionManager.getAllNodes(grafanaProps.getGrafanaActionPoint());
        for (AntActionStatus status : this.onlineGrafanaList) {
            if (OsUtil.isLocalIP(status.getIp())) {
                return status;
            }
        }
        return null;
    }

    boolean isFirstPrometheus = true;

    private void registerPrometheusDatasource(AntActionStatus grafana) {
        log.debug("RegisterPrometheusDatasource ...");
        String prometheusDatasourceUrl = prometheusDatasourceUrl();
        if (prometheusDatasourceUrl != null) {
            Map<String, String> replacements = new TreeMap<>();
            replacements.put(Constants.TEMPLATE_SYMBOL_URL, prometheusDatasourceUrl);
            //设置 Prometheus 数据源 的baseAuth 用户名和密码
            replacements.put(Constants.TEMPLATE_SYMBOL_USER, grafanaProps.getPrometheusUser());
            replacements.put(Constants.TEMPLATE_SYMBOL_PW, grafanaProps.getPrometheusPwd());
            boolean basicAuth = StringUtils.isNoneBlank(grafanaProps.getPrometheusPwd());

            log.debug("SET PROMETHEUS DATASOURCE BASIC_AUTH={}", basicAuth);
            replacements.put(Constants.TEMPLATE_SYMBOL_BASIC_AUTH, String.valueOf(basicAuth).toLowerCase());
            try {
                DatasourceLoader dsLoader = new DatasourceLoader(Constants.TEMPLATE_PATH_DATASOURCE_PROMETHEUS, replacements);
                Datasource ds = dsLoader.load();
                upsertDatasource(ds.getName(), ds.getJson());
                log.debug("RegisterPrometheusDatasource ok");
            } catch (Exception e) {
                log.error("RegisterPrometheusDatasource error.", e);
                if (isFirstPrometheus) {
                    log.error("RegisterPrometheusDatasource error.", e);
                    isFirstPrometheus = false;
                } else {
                    log.error("RegisterPrometheusDatasource error.");
                }
            }
        }
    }

    boolean isFirstLoki = true;

    private void registerLokiDatasource(AntActionStatus grafana) {
        log.debug("RegisterLokiDatasource ...");
        String lokiDatasourceUrl = lokiDatasourceUrl();
        if (lokiDatasourceUrl != null) {
            Map<String, String> replacements = new TreeMap<>();
            replacements.put(Constants.TEMPLATE_SYMBOL_URL, lokiDatasourceUrl);
            //设置 loki数据源 的baseAuth 用户名和密码
            replacements.put(Constants.TEMPLATE_SYMBOL_USER, grafanaProps.getLokiUser());
            replacements.put(Constants.TEMPLATE_SYMBOL_PW, grafanaProps.getLokiPwd());
            boolean basicAuth = StringUtils.isNoneBlank(grafanaProps.getPrometheusPwd());

            log.debug("SET LOKI DATASOURCE BASIC_AUTH={}", basicAuth);
            replacements.put(Constants.TEMPLATE_SYMBOL_BASIC_AUTH, String.valueOf(basicAuth).toLowerCase());
            try {
                DatasourceLoader dsLoader = new DatasourceLoader(Constants.TEMPLATE_PATH_DATASOURCE_LOKI, replacements);
                Datasource ds = dsLoader.load();
                upsertDatasource(ds.getName(), ds.getJson());
                log.debug("RegisterLokiDatasource OK");
            } catch (Exception e) {
                if (isFirstLoki) {
                    log.error("RegisterLokiDatasource error.", e);
                    isFirstLoki = false;
                } else {
                    log.error("RegisterLokiDatasource error.");
                }
            }
        }
    }

    boolean isFirstRest = true;

    private void registerRestDatasource(AntActionStatus grafana) {
        log.debug("RegisterRestDatasource ...");
        String restDatasourceUrl = restDatasourceUrl();
        Map<String, String> replacements = new TreeMap<>();
        replacements.put(Constants.TEMPLATE_SYMBOL_URL, restDatasourceUrl);
        //设置 rest数据源 的baseAuth 用户名和密码
        boolean basicAuth = false;
        if (this.securityProperties.getUser() != null) {
            replacements.put(Constants.TEMPLATE_SYMBOL_USER, securityProperties.getUser().getName());
            replacements.put(Constants.TEMPLATE_SYMBOL_PW, securityProperties.getUser().getPassword());
            basicAuth = StringUtils.isNoneBlank(securityProperties.getUser().getPassword());
        }
        log.debug("SET REST DATASOURCE BASIC_AUTH={}", basicAuth);
        replacements.put(Constants.TEMPLATE_SYMBOL_BASIC_AUTH, String.valueOf(basicAuth).toLowerCase());
        try {
            DatasourceLoader dsLoader = new DatasourceLoader(Constants.TEMPLATE_PATH_DATASOURCE_REST, replacements);
            Datasource ds = dsLoader.load();
            upsertDatasource(ds.getName(), ds.getJson());
            log.debug("RegisterRestDatasource ok");
        } catch (Exception e) {
            if (isFirstRest) {
                log.error("RegisterRestDatasource Error", e);
                isFirstRest = false;
            } else {
                log.error("RegisterRestDatasource Error");
            }
        }
    }

    boolean isFirstEs = true;

    private void registerEsDatasource(AntActionStatus grafana) {
        log.debug("RegisterEsDatasource ...");

        String esDatasourceUrl = esDatasourceUrl();
        Map<String, String> replacements = new TreeMap<>();
        replacements.put(Constants.TEMPLATE_SYMBOL_URL, esDatasourceUrl);
        replacements.put(Constants.TEMPLATE_SYMBOL_USER, this.grafanaProps.getEsUser());
        replacements.put(Constants.TEMPLATE_SYMBOL_PW, this.grafanaProps.getEsPwd());
        replacements.put(Constants.TEMPLATE_SYMBOL_METRIC_TYPE, "proc");
        boolean basicAuth = StringUtils.isBlank(this.grafanaProps.getEsPwd());
        log.debug("Set ES DATASOURCE BASIC_AUTH={}", basicAuth);
        replacements.put(Constants.TEMPLATE_SYMBOL_BASIC_AUTH, String.valueOf(basicAuth).toLowerCase());
        try {
            DatasourceLoader dsLoader = new DatasourceLoader(Constants.TEMPLATE_PATH_DATASOURCE_ES, replacements);
            Datasource ds = dsLoader.load();
            upsertDatasource(ds.getName(), ds.getJson());
            log.debug("RegisterEsDatasource ok");
        } catch (Exception e) {
            if (isFirstEs) {
                log.error("RegisterEsDatasource Error", e);
                isFirstEs = false;
            } else {
                log.error("RegisterEsDatasource Error");
            }
        }
    }

    boolean isFirstDs = true;

    private void upsertDatasource(String datasourceName, JSONObject datasource) {
        log.debug("UpsertDatasource datasourceName={}", datasourceName);

        try {
            JSONObject datasourceInGrafana = this.grafanaClient.getDataSource(datasourceName);
            if (datasourceInGrafana == null) {
                this.grafanaClient.createDataSource(datasourceName, datasource.toJSONString());
            } else {
                String urlInGrafana = datasourceInGrafana.getString("url");
                String urlInNewDatasource = datasource.getString("url");
                if (urlInGrafana == null || !StringUtils.equals(urlInGrafana, urlInNewDatasource)) {
                    datasource.put("id", datasourceInGrafana.getIntValue("id"));
                    this.grafanaClient.updateDataSource(datasourceName, datasource);
                }
            }
        } catch (Exception e) {
            if (isFirstDs) {
                log.error("Fail to upsert datasource[{}]", datasourceName, e);
                isFirstDs = false;
            } else {
                log.error("Fail to upsert datasource[{}]", datasourceName);
            }
        }
    }

    private String prometheusDatasourceUrl() {
        List<AntActionStatus> grafanaList = this.onlineActionManager.getAllNodes(this.grafanaProps.getPrometheusActionPoint());
        if (grafanaList == null || grafanaList.isEmpty()) {
            return null;
        }
        return grafanaList.getFirst().getRestWebUri();
    }

    private String restDatasourceUrl() {
        String localIp = skynetProperties.getIpAddress();
        int port = this.skynetProperties.getPort();
        port = (port == 0) ? Integer.parseInt(System.getProperty("server.port")) : port;
        return String.format("http://%s:%d/grafana", localIp, port);
    }

    private String esDatasourceUrl() {
        return String.format("http://%s:%d", this.grafanaProps.getEsHost(), this.grafanaProps.getEsPortHttp());
    }

    private String lokiDatasourceUrl() {
        return (grafanaProps.isLokiEnabled() && StringUtils.isNoneBlank(this.grafanaProps.getLokiHost())) ? String.format("http://%s", this.grafanaProps.getLokiHost()) : null;
    }

    public void stop() {
        this.running = false;
    }

    public void resume() {
        this.running = true;
    }

    private class LoadTask implements Runnable {

        @Override
        public void run() {
            if (!running) {
                return;
            }
            log.debug("The grafana loader begin ..");
            onlineGrafanaOnLocal = grafanaOnLocalServer();
            if (onlineGrafanaOnLocal == null) {
                log.debug("The grafana={} is not on local host.", grafanaProps.getGrafanaActionPoint());
                return;
            }

            log.debug("The grafana is on local host.{}", grafanaProps.getGrafanaActionPoint());
            long start = System.currentTimeMillis();
            try {
                /*
                 * 加载datasource
                 */
                registerEsDatasource(onlineGrafanaOnLocal);
                registerPrometheusDatasource(onlineGrafanaOnLocal);
                registerLokiDatasource(onlineGrafanaOnLocal);
                registerRestDatasource(onlineGrafanaOnLocal);

                /*
                 * 下载缓存缺失的dashboard
                 */
                dashboardFiles = dashboardRepo.list();
                for (RepoFileDo file : dashboardFiles) {
                    try {
                        String md5 = file.getMd5sum();
                        // handle new file
                        if (md5 == null || !dashboardCache.containsKey(md5)) {
                            Dashboard d = dashboardRepo.load(file);
                            dashboardCache.put(d.getMd5(), d);
                            file.setMd5sum(d.getMd5());
                            log.debug("find new dashboard : plugin[{}] path[{}] uid[{}] md5sum[{}]", file.getPlugin(), file.getFilePath(), d.getUid(), d.getMd5());
                        }
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }
                /*
                 * 移除缓存中无效的dashboard
                 */
                Set<String> checksumOfFilesInRepo = dashboardFiles.stream().map(e -> (StringUtils.isBlank(e.getMd5sum()) ? "" : e.getMd5sum())).collect(Collectors.toSet());
                Iterator<Entry<String, Dashboard>> it = dashboardCache.entrySet().iterator();
                while (it.hasNext()) {
                    Entry<String, Dashboard> entry = it.next();
                    if (!checksumOfFilesInRepo.contains(entry.getKey())) {
                        it.remove();
                        log.debug("remove dashboard in cache: uid[{}] md5sum[{}]", entry.getValue().getUid(), entry.getValue().getMd5());
                    }
                }

                /*
                 * 创建grafana缺失的dashboard
                 */
                for (Dashboard dashboard : dashboardCache.values()) {
                    dashboardUploader.create(onlineGrafanaOnLocal, dashboard);
                }
            } catch (Exception e) {
                log.error("Load Grafana resource error.", e);
            } finally {
                dashboardUidInCache = dashboardCache.values().stream().map(Dashboard::getUid).toList();
            }
            log.debug("Grafana load task take {} ms", (System.currentTimeMillis() - start));
        }
    }

}

class Constants {

    /**
     * 模板变量
     */
    public static final String TEMPLATE_SYMBOL_URL = "${url}";
    public static final String TEMPLATE_SYMBOL_USER = "${user}";
    public static final String TEMPLATE_SYMBOL_PW = "${pwd}";
    public static final String TEMPLATE_SYMBOL_BASIC_AUTH = "${basicAuth}";
    public static final String TEMPLATE_SYMBOL_METRIC_TYPE = "${metricType}";

    public static final String TEMPLATE_PATH_DATASOURCE_ES = "grafana/prometheus/datasource-es.json";
    public static final String TEMPLATE_PATH_DATASOURCE_REST = "grafana/prometheus/datasource-rest.json";
    public static final String TEMPLATE_PATH_DATASOURCE_PROMETHEUS = "grafana/prometheus/datasource-prometheus.json";

    public static final String TEMPLATE_PATH_DATASOURCE_LOKI = "grafana/prometheus/datasource-loki.json";

}
