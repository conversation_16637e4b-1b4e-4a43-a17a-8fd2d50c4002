package skynet.platform.agent.grafana.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;
import skynet.platform.agent.grafana.loader.GrafanaLoader;

/**
 * <AUTHOR>
 */
@ConditionOnGrafana
@RestController
@ExposeSwagger2
@RequestMapping(value = "/grafana/loader")
public class GrafanaLoaderController {

    private final GrafanaLoader grafanaLoader;

    public GrafanaLoaderController(GrafanaLoader grafanaLoader) {
        this.grafanaLoader = grafanaLoader;
    }

    @GetMapping("/stop")
    public String stop() {
        this.grafanaLoader.stop();
        return "stopped!";
    }

    @GetMapping("/resume")
    public String resume() {
        this.grafanaLoader.resume();
        return "resume!";
    }

    @GetMapping
    public String status() {
        return (this.grafanaLoader.isRunning()) ? "running" : "stopped";
    }

}
