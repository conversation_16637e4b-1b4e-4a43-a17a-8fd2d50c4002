package skynet.platform.agent.grafana.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.agent.grafana.GrafanaProperties;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;
import skynet.platform.agent.grafana.exception.TraceableRestClientException;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.repository.domain.ActionNameContract;

import java.io.IOException;
import java.net.URI;
import java.util.Base64;
import java.util.List;

@Slf4j
@ConditionOnGrafana
@Component
public class GrafanaClient {

    private final DiscoveryClient discoveryClient;
    private final GrafanaProperties grafanaProps;
    private final String managerServiceId;
    private final String grafanaServiceId;
    private final RestTemplate authRestTemplate;
    private final RestTemplate restTemplate;


    public GrafanaClient(GrafanaProperties grafanaProps, DiscoveryClient discoveryClient,
                         @Qualifier("authRestTemplate") RestTemplate authRestTemplate) {
        this.discoveryClient = discoveryClient;
        this.authRestTemplate = authRestTemplate;
        this.restTemplate = new RestTemplate();
        this.restTemplate.setErrorHandler(new MyErrorHandler());
        this.grafanaProps = grafanaProps;
        this.managerServiceId = AppBootEnvironment.MANAGER_SERVICE_ID;
        this.grafanaServiceId = new ActionNameContract(grafanaProps.getGrafanaActionPoint()).getActionCode();
    }

    public JSONObject getDataSource(String name) throws TraceableRestClientException {
        String uriString = String.format("/api/datasources/name/%s", name);
        return doGet(uriString);
    }

    public void createDataSource(String name, String json) throws TraceableRestClientException {
        this.doPost("/api/datasources", json);
        log.debug("Create datasource [{}] success.", name);
    }

    public void updateDataSource(String name, JSONObject datasource) throws TraceableRestClientException {
        int id = datasource.getIntValue("id");
        String uriString = String.format("/api/datasources/%d", id);
        this.doPut(uriString, datasource.toJSONString());
        log.debug("Update datasource [{}] success.", name);
    }

    public JSONObject getDashboard(String uid) throws TraceableRestClientException {
        String uriString = String.format("/api/dashboards/uid/%s", uid);
        return doGet(uriString);
    }

    public void createOrUpdateDashboard(String uid, String json) throws TraceableRestClientException {
        JSONObject ret = doPost("/api/dashboards/db", json);
        log.debug("Create or update dashboard [{}] success.", uid);
    }

    public JSONObject getUserPrefs() throws TraceableRestClientException {
        return doGet("/api/user/preferences");
    }

    public JSONObject updateUserPrefs(String json) throws TraceableRestClientException {
        return doPut("/api/user/preferences", json);
    }

    private JSONObject doGet(String uriString) throws TraceableRestClientException {
        return doExchange(HttpMethod.GET, uriString, null);
    }

    private JSONObject doPost(String uriString, String body) throws TraceableRestClientException {
        return doExchange(HttpMethod.POST, uriString, body);
    }

    private JSONObject doPut(String uriString, String body) throws TraceableRestClientException {
        return doExchange(HttpMethod.PUT, uriString, body);
    }

    private JSONObject doExchange(HttpMethod method, String uriString, String reqBody) throws TraceableRestClientException {
        JSONObject ret = null;
        log.debug("Api uriString={}", uriString);

        List<ServiceInstance> objList = discoveryClient.getInstances(managerServiceId);
        if (objList.isEmpty()) {
            throw new RuntimeException(String.format("The serviceId=%s not online.", managerServiceId));
        }
        ServiceInstance manager = objList.getFirst();
        String managerUri = String.format("%s%s", manager.getUri(), manager.getMetadata().getOrDefault("server.servlet.context-path", ""));

        log.debug("Fetch token by managerUri={}", managerUri);
        //通过签名获取 获取令牌
        String tokenJson = authRestTemplate.getForObject(String.format("%s/skynet/auth/token", managerUri), String.class);
        log.debug("token json={}", tokenJson);
        SkynetApiResponse<AccessToken> accessToken = JSON.parseObject(tokenJson, new TypeReference<>() {
        });
        assert accessToken != null;
        if (accessToken.code != 0) {
            throw new RuntimeException(String.format("Fetch accessToken Error=%s", accessToken.getMessage()));
        }
        List<ServiceInstance> grafanaServiceList = discoveryClient.getInstances(this.grafanaServiceId);

        for (ServiceInstance serviceInstance : grafanaServiceList) {
            try {

                log.debug("GrafanaServer uriString={}", uriString);
                uriString = String.format("%s%s", serviceInstance.getUri(), uriString);
                log.debug("Full uriString={}", uriString);

                URI uri = new URI(uriString);
                HttpHeaders headers = new HttpHeaders();
                String auth = genAuth(grafanaProps.getGrafanaUser(), grafanaProps.getGrafanaPassword());
                headers.add(HttpHeaders.AUTHORIZATION, auth);
                headers.add("skynet_token", accessToken.getData().getToken());
                if (StringUtils.isNoneBlank(reqBody)) {
                    headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                }
                HttpEntity<String> reqEntity = new HttpEntity<>(reqBody, headers);
                ResponseEntity<String> entity = restTemplate.exchange(uri, method, reqEntity, String.class);
                String respBody = entity.getBody();
                log.trace("{} {}\n  response code: {}\n  response body:{}", method, uriString, entity.getStatusCode(), respBody);
                if (entity.getStatusCode().is2xxSuccessful()) {
                    ret = JSON.parseObject(respBody);
                }
            } catch (RestClientException e) {
                throw TraceableRestClientException.build(method.toString().toUpperCase(), uriString, e);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return ret;
    }

    /**
     * 生成Basic认证方式的HTTP HEADER
     *
     * @param user
     * @param pwd
     * @return
     */
    private static String genAuth(String user, String pwd) {
        return "Basic " + Base64.getEncoder().encodeToString(String.format("%s:%s", user, pwd).getBytes());
    }

    private static class MyErrorHandler extends DefaultResponseErrorHandler {

        @Override
        public boolean hasError(ClientHttpResponse response) throws IOException {
            return !response.getStatusCode().is2xxSuccessful() && response.getStatusCode().value() != 404;
        }
    }

    @Getter
    @Setter
    static class AccessToken extends Jsonable {
        private String token;

        @Override
        public String toString() {
            return super.toString();
        }
    }


    @Getter
    @Setter
    static class SkynetApiResponse<T> extends Jsonable {
        private int code;
        private String message = "";
        private T data;

        @Override
        public String toString() {
            return super.toString();
        }
    }
}
