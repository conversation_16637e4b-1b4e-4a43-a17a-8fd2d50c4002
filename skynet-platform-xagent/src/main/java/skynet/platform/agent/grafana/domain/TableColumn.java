package skynet.platform.agent.grafana.domain;


import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
public class TableColumn extends Jsonable {
    private String text;
    private String type;
    private Boolean sort;
    private boolean desc;


    public TableColumn(String text, String type, Boolean sort, boolean desc) {
        this.text = text;
        this.type = type;
        this.sort = sort;
        this.desc = desc;
    }

    public TableColumn(String text) {
        this.text = text;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}