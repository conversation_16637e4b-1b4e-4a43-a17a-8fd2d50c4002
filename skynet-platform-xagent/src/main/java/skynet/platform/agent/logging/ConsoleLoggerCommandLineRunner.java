package skynet.platform.agent.logging;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;


/**
 * Test 使用
 *
 * <AUTHOR>
 * @date 2020/5/12 20:31
 */
@Slf4j
public class ConsoleLoggerCommandLineRunner implements CommandLineRunner {

    private final Environment environment;

    public ConsoleLoggerCommandLineRunner(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void run(String... strings) throws Exception {

        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(1500);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
                ConsoleLogger consoleLogger = new ConsoleLogger("action-001", true);
                consoleLogger.info("action-001, log: true");
            }
        }).start();

        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
                ConsoleLogger consoleLogger = new ConsoleLogger("action-002", false);
                consoleLogger.info("action-002, log: false");
            }
        }).start();
    }
}
