package skynet.platform.agent.core.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import skynet.boot.common.utils.MD5Util;
import skynet.boot.security.auth.AuthUtils;
import skynet.platform.common.auth.AuthClientProperties;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RemoteFileUtils {

    private final AuthClientProperties authClientProperties;

    public RemoteFileUtils(AuthClientProperties authClientProperties) {
        this.authClientProperties = authClientProperties;
    }

    /**
     * <pre>
     * 下载失败，将返回null
     * </pre>
     *
     * @param uri
     * @return
     */
    public UpdateFileInfo getRemoteUpdateFileInfo(String uri) {
        long cost = System.currentTimeMillis();
        UpdateFileInfo updateFileInfo = null;

        // 构建请求对象
        RequestConfig config = RequestConfig.custom().setConnectTimeout(10000).build();
        try (CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(config).build()) {
            HttpHead req = new HttpHead(uri);
            this.setAuthHead(req);
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                // 检查返回代码
                int code = resp.getStatusLine().getStatusCode();
                if (code >= 200 && code < 300) {
                    updateFileInfo = getUpdateFileInfo(resp);
                } else {
                    closeHttpRequest(req, resp);
                }
            }
        } catch (Exception e) {
            log.error("Error when send head.[{}]", e.getMessage());
        }

        log.info("Fetch UpdateFileInfo [uri={}], cost={}ms", uri, System.currentTimeMillis() - cost);

        //若uri是个外链，则HEAD请求没有filename和md5的Header
        if (updateFileInfo != null && updateFileInfo.getFileName() == null && updateFileInfo.getMd5() == null) {
            URI _uri = null;
            try {
                _uri = new URI(uri);
                String filename = FilenameUtils.getName(_uri.getPath());
                updateFileInfo.setFileName(filename);
                String md5uri = uri.replace(filename, String.format("%s.md5", filename));
                String md5 = this.getContent(md5uri);
                updateFileInfo.setMd5(md5);
            } catch (URISyntaxException e) {
                log.error("Invalid URI : {}", uri);
            }
        }
        return updateFileInfo;
    }

    private void setAuthHead(HttpRequestBase requestBase) {
        log.debug("Set auth header url={}", requestBase.getURI());
        Map<String, String> headers =
                AuthUtils.assembleAuthorizationHeaders(Objects.requireNonNull(HttpMethod.valueOf(requestBase.getMethod())),
                        requestBase.getURI().toString(), authClientProperties.getApiKey(), authClientProperties.getApiSecret(), null);
        headers.forEach(requestBase::addHeader);
    }

    private void closeHttpRequest(HttpRequestBase httpRequest, CloseableHttpResponse resp) {
        try {
            httpRequest.abort();
        } catch (Exception ignored) {
        }
        String errorMessage = "";
        Header header = resp.getFirstHeader(AuthUtils.SKYNET_AUTH_ERR_MESSAGE);
        if (header != null) {
            errorMessage = header.getValue();
        }
        String msg = String.format("uri=%s;code=%s;err=%s.", httpRequest.getURI(), resp.getStatusLine().getStatusCode(), errorMessage);
        throw new RuntimeException(msg);

    }

    /**
     * 下载文件
     *
     * <pre>
     * 产物：
     * 1、目标文件	abc.zip
     * 3、目标文件的MD5   abc.zip.md5
     * </pre>
     *
     * @param uri
     * @param targetDir
     * @param fileName
     * @return
     * @throws IOException
     */
    public UpdateFile download(String uri, File targetDir, String fileName) throws IOException {
        long cost = System.currentTimeMillis();

        // 目标文件地址
        UpdateFile updateFile = new UpdateFile(targetDir, fileName);

        // 删除当前的文件
        log.debug("Del targetFile={} begin...", updateFile.getTargetFile());
        FileUtils.deleteQuietly(updateFile.getTargetFile());
        log.debug("Del Md5File={} begin...", updateFile.getMd5File());
        FileUtils.deleteQuietly(updateFile.getMd5File());
        log.debug("Del Md5File={} end.", updateFile.getMd5File());

        // 创建目标文件
        FileUtils.forceMkdir(updateFile.getTargetFile().getParentFile());
        updateFile.getTargetFile().createNewFile();
        // 下载文件流，向目标文件写入
        String fileMd5 = null;
        // 创建文件写入流
        try (OutputStream out = Files.newOutputStream(updateFile.getTargetFile().toPath())) {
            // 构建请求对象
            RequestConfig config = RequestConfig.custom().setConnectTimeout(10000).build();
            // 执行请求
            try (CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(config).build()) {
                HttpGet req = new HttpGet(uri);
                this.setAuthHead(req);
                try (CloseableHttpResponse resp = httpClient.execute(req)) {
                    // 检查返回代码
                    int code = resp.getStatusLine().getStatusCode();
                    if (code >= 200 && code < 300) {
                        try (InputStream in = resp.getEntity().getContent()) {
                            IOUtils.copyLarge(in, out);
                            fileMd5 = MD5Util.getFileMd5String(updateFile.getTargetFile());
                            updateFile.setLocalUpdateFileInfo(new UpdateFileInfo(fileName, fileMd5));
                            updateFile.setRemoteUpdateFileInfo(getUpdateFileInfo(resp));
                            updateFile.getRemoteUpdateFileInfo().setFileName(fileName);
                        }
                    } else {
                        closeHttpRequest(req, resp);
                    }
                }
            } catch (Exception e) {
                log.error("Download file={} Error={}", uri, e.getMessage());
            }
        }

        log.info("Fetch UpdateFileInfo [{}], cost={}ms", uri, System.currentTimeMillis() - cost);

        // 写入md5
        if (fileMd5 != null && StringUtils.isNoneBlank(fileMd5)) {
            FileUtils.writeStringToFile(updateFile.getMd5File(), fileMd5, StandardCharsets.UTF_8);
        }
        return updateFile;
    }

    private UpdateFileInfo getUpdateFileInfo(CloseableHttpResponse resp) {
        UpdateFileInfo remoteFile = new UpdateFileInfo();

        // 检查返回代码
        int code = resp.getStatusLine().getStatusCode();
        if (code >= 200 && code < 300) {
            String value = getHeaderValue(resp, new String[]{"Last-Modified", "ETag"});
            if (StringUtils.isNotBlank(value)) {
                remoteFile.setLastModified(value);
            }

            value = getHeaderValue(resp, new String[]{"FileName"});
            if (StringUtils.isNotBlank(value)) {
                remoteFile.setFileName(value);
            }
            value = getHeaderValue(resp, new String[]{"FileSize"});
            if (StringUtils.isNotBlank(value)) {
                remoteFile.setFileSize(Long.parseLong(value));
            }

            value = getHeaderValue(resp, new String[]{"MD5"});
            if (StringUtils.isNotBlank(value)) {
                remoteFile.setMd5(value);
            }
        }
        return remoteFile;
    }

    /**
     * 获取URI的http body
     *
     * @param uri
     * @return
     */
    private String getContent(String uri) {
        String ret = null;
        // 构建请求对象
        RequestConfig config = RequestConfig.custom().setConnectTimeout(10000).build();
        // 执行请求
        try (CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(config).build()) {
            HttpGet req = new HttpGet(uri);
            try (CloseableHttpResponse resp = httpClient.execute(req)) {
                // 检查返回代码
                int code = resp.getStatusLine().getStatusCode();
                if (code >= 200 && code < 300) {
                    try (InputStream in = resp.getEntity().getContent()) {
                        ret = IOUtils.toString(in, StandardCharsets.UTF_8);
                    }
                } else {
                    try {
                        req.abort();
                    } catch (Exception e) {
                    }
                }
            }
        } catch (Exception e) {
            log.error("Download {} Error={}", uri, e.getMessage());
        }
        return ret;
    }

    private static String getHeaderValue(CloseableHttpResponse resp, String[] headerNames) {
        for (String name : headerNames) {
            Header[] headers = resp.getHeaders(name);
            if (headers != null) {
                for (Header header : headers) {
                    if (StringUtils.isNotEmpty(header.getValue())) {
                        return header.getValue();
                    }
                }
            }
        }
        return null;
    }
}
