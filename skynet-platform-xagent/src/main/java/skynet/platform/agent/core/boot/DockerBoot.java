package skynet.platform.agent.core.boot;

import com.alibaba.fastjson2.JSON;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.InspectContainerResponse;
import com.github.dockerjava.api.command.InspectImageResponse;
import com.github.dockerjava.api.exception.NotFoundException;
import com.github.dockerjava.api.model.ExposedPort;
import com.github.dockerjava.api.model.Info;
import com.github.dockerjava.api.model.Ports;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import skynet.platform.agent.core.exception.BootException;
import skynet.platform.agent.config.DockerConfig;
import skynet.platform.common.domain.UpdateParam;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.repository.config.IAntConfigService;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * docker程序启动
 *
 * <AUTHOR> QQ：*********
 * @date $date$
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(DockerBoot.BEAN_NAME)
@AutoConfigureAfter(DockerConfig.class)
public class DockerBoot extends BaseBoot {

    public static final String BEAN_NAME = "boot.dockerboot";

    @Autowired(required = false)
    private DockerClient dockerClient;
    private final IAntConfigService antConfigService;

    public DockerBoot(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    @Override
    protected String getToProcess(BootEnvironment bootEnvironment) {
        log.debug("===============Assembling the docker command===========");
        if (dockerClient == null) {
            log.error("--docker client error ------------------------");
            log.error("The docker runtime is not install");
            log.error("Or Please check /usr/lib/systemd/system/docker.service，");
            log.error("Whether the [unix:///var/run/docker.sock] parameter is available for ExecStart");
            log.error("eg：ExecStart=/usr/bin/dockerd -H unix:///var/run/docker.sock");
            log.error("--docker client error ------------------------");
            throw new BootException("Docker runtime is not install");
        }
        // 检测 docker镜像是否存在
        try {
            Info info = dockerClient.infoCmd().exec();
            log.trace("Docker info={}", info);
            if (info != null) {
                log.info("Docker root dir={};version={}", info.getDockerRootDir(), info.getServerVersion());
            }
        } catch (Exception ex) {
            log.error("********************************Please note！ docker error！********************************");
            log.error("Is the docker installed or started?");
            log.error("error msg={}", ex.getMessage());
            log.error("********************************************************************************************");
            throw new BootException("docker is not installed or not running");
        }
        //替换占位符
        String imagesName = bootEnvironment.replacePlaceholder(this.getAntActionParam().getBootParam().getMainJar());
        //skynet.registry.url

        //改用 AID，不然同一个docker不能启动多个实例
        String containerName = "skynet-" + this.getBootParam().getAid().replace("@", "-");
        try {
            InspectContainerResponse containerResponse = dockerClient.inspectContainerCmd(containerName).exec();
            log.info("Container is {}", containerResponse);
            // 端口没做映射
            if (isMod(containerResponse)) {
                log.info("The config is change recreate container");
                dockerClient.removeContainerCmd(containerName).exec();
                return createCmd(containerName, imagesName);
            }
        } catch (NotFoundException ex) {
            log.info("The container [{}] is not exist,create container by image {}", containerName, imagesName);
            return createCmd(containerName, imagesName);
        } catch (Exception ex) {
            log.error("container exception:", ex);
        }
        String cmd = String.format("docker start %s -i", containerName);
        log.info("Exec cmd:\t[{}]", cmd);
        log.info("===============End assembling the docker command===========");
        return cmd;
    }

    private String getUrl(String uriString) {
        if (StringUtils.isBlank(uriString) || StringUtils.isBlank(uriString.replace("skynet:", "")) || StringUtils.isBlank(uriString.replace("skynet:null", ""))) {
            log.warn("The uriString invalidity。{}", uriString);
            return null;
        }
        URI uri = null;
        try {
            uri = new URI(uriString);
        } catch (URISyntaxException e) {
            log.warn("Skynet url invalidity。{}", uriString);
        }
        assert uri != null;
        String fileRelativePath = uri.getSchemeSpecificPart();
        log.info("new url={}", fileRelativePath);
        return fileRelativePath;
    }

    /**
     * 创建run 命令
     *
     * @param containerName
     * @param imagesName
     * @return
     */
    private String createCmd(String containerName, String imagesName) {
        try {
            InspectImageResponse imageResponse = dockerClient.inspectImageCmd(imagesName).exec();
            log.info("The docker image [{}] is exist.", imagesName);
            log.trace("The docker image={} detail={}", imagesName, imageResponse);
        } catch (NotFoundException ex) {
            log.warn("The docker image [{}] is not exist.", imagesName);
            List<UpdateParam> updateParams = this.getAntActionParam().getBootParam().getUpdateParams();
            if (updateParams != null) {
                for (UpdateParam item : updateParams) {
                    String imageFile = String.format("%s/%s", super.getAppEnvironment().replacePlaceholder(item.getTargetDir()), getUrl(item.getFileUrl()));
                    if (!StringUtils.endsWithIgnoreCase(imageFile, ".tar")) {
                        continue;
                    }

                    log.info("Start load docker image [{}]...", imageFile);
                    String exMessage = null;
                    try (InputStream inputStream = new FileInputStream(imageFile)) {
                        log.info("Load docker image .[{}] ...", imageFile);
                        dockerClient.loadImageCmd(inputStream).exec();
                        log.info("Successfully loading the docker image  [{}].", imageFile);
                    } catch (FileNotFoundException e) {
                        exMessage = String.format("Load docker image [%s] error [%s].", imagesName, e.getMessage());
                        log.error(exMessage);
                    } catch (Exception e) {
                        exMessage = String.format("Load docker image [%s] error [%s].", imageFile, e.getMessage());
                        log.error(exMessage, e);
                    } finally {
                        if (StringUtils.isNoneBlank(exMessage)) {
                            throw new BootException(exMessage);
                        }
                    }
                }
            }
        }

        String env = this.getAntActionParam().getBootParam().getDockerEnv();

        String pbind = "";
        int port = this.getAntActionParam().getPort();
        if (StringUtils.isEmpty(env)) {
            env = "";

            if (port > 0) {
                pbind = "-p " + port + ":" + port;
                log.debug("port={}", pbind);
            }
        } else {
            if (!env.contains("-p")) {
                if (port > 0) {
                    pbind = "-p " + port + ":" + port;
                    log.debug("port={}", pbind);
                }
            }
            log.debug("Old env={}", env);
        }
        env = this.envNormalize(env);
        this.getAntActionParam().getBootParam().setOldDockerEnv(env);
        log.debug("Docker env={}", env);

        String labelName = imagesName;
        if (imagesName.indexOf(":") > 0) {
            log.info("The docker image contain tag={}", imagesName);
            labelName = imagesName.split(":")[0];
            log.info("The docker container label={}", labelName);
        }

        List<String> args = this.getAntActionParam().getBootParam().getWorkArgs();
        if (args == null) {
            args = Collections.emptyList();
        }
        //String cmd = String.format("docker run -l %s %s -v /etc/localtime:/etc/localtime %s --name %s %s %s",

        // 将 container name 移动到镜像名称前面，让skynet默认生成的 name 优先。 by lyhu 2020年12月30日19:57:48
        String cmd = String.format("docker run -l %s %s %s --name %s %s %s", labelName, pbind, env, containerName, imagesName, StringUtils.join(args, " "));
        log.debug("exec cmd={}", cmd);
        String pluginNode = String.format("%s/%s", antConfigService.getSkynetPluginPath(), this.getAntActionParam().getPlugin());
        String actionNodePath = String.format("%s/action/%s", pluginNode, this.getAntActionParam().getCode());
        antConfigService.setData(String.format("%s/_xboot_param", actionNodePath), JSON.toJSONString(this.getAntActionParam().getBootParam()));

        return cmd;
    }

    @Getter
    @Setter
    public static class DockerImage {
        private String registry;
        private String user;
        private String image;
        private String tag;

        @Override
        public String toString() {
            return "DockerImage{" +
                    "registry='" + registry + '\'' +
                    ", user='" + user + '\'' +
                    ", image='" + image + '\'' +
                    ", tag='" + tag + '\'' +
                    '}';
        }
    }

    public static DockerImage parseDockerImage(String imageName) {
        DockerImage dockerImage = new DockerImage();
        dockerImage.setTag("latest"); // 默认标签

        // 分割仓库地址和镜像信息
        String[] parts = imageName.split("/");
        if (parts.length == 3) {
            dockerImage.setRegistry(parts[0]);
            dockerImage.setUser(parts[1]);
            dockerImage.setImage(parts[2]);
        } else if (parts.length == 2) {
            dockerImage.setUser(parts[0]);
            dockerImage.setImage(parts[1]);
        } else {
            dockerImage.setImage(parts[0]);
        }

        // 分割镜像名称和标签
        if (dockerImage.getImage().contains(":")) {
            String[] imageParts = dockerImage.getImage().split(":");
            dockerImage.setImage(imageParts[0]);
            dockerImage.setTag(imageParts[1]);
        }

        return dockerImage;
    }

    /**
     * 如果是Dockerboot，将workArgs 置空，自己按照用户原样拼接
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getWorkArgs() throws Exception {
        return Collections.emptyList();
    }

    /**
     * 判断容器属性是否有修改
     *
     * @param containerResponse 容器信息
     * @return 修改true, 未修改false
     */
    private boolean isMod(InspectContainerResponse containerResponse) {
        // 端口修改判断
        boolean isPortChange = isPortChange(containerResponse);
        if (isPortChange) {
            log.debug("The port has changed");
            return true;
        }

        // 参数变化
        String dockerEnv = this.envNormalize(this.getAntActionParam().getBootParam().getDockerEnv());
        log.debug("current env={}", dockerEnv);
        String oldDockerEnv = this.getAntActionParam().getBootParam().getOldDockerEnv();
        log.debug("old env={}", oldDockerEnv);
        if (dockerEnv != null && !dockerEnv.equals(oldDockerEnv)) {
            log.info("docker env changed={}", oldDockerEnv);
            return true;
        }
        return false;
    }

    /**
     * 端口是否有变化
     *
     * @param containerResponse
     * @return
     */
    private boolean isPortChange(InspectContainerResponse containerResponse) {
        int port = this.getAntActionParam().getPort();
        Ports ports = containerResponse.getHostConfig().getPortBindings();
        if (ports != null) {
            Map<ExposedPort, Ports.Binding[]> bs = ports.getBindings();
            log.debug("port binding information={}", bs);
            if (bs != null) {
                for (ExposedPort p : bs.keySet()) {
                    Ports.Binding[] bindings = bs.get(p);
                    if (bindings != null) {
                        for (Ports.Binding b : bindings) {
                            if (b.getHostPortSpec().endsWith("" + port)) {
                                log.debug("port unchanged====port The port has been mapped:{},{}", ports, port);
                                return false;
                            }
                        }
                    }
                }
            }
        }
        log.debug("port has changed");
        return true;
    }

    private String envNormalize(String env) {
        if (StringUtils.isEmpty(env)) {
            return "";
        }
        String ret = super.getAppEnvironment().replacePlaceholder(env);
        ret = ret.replaceAll("\\s+", " ");
        return ret;
    }
}
