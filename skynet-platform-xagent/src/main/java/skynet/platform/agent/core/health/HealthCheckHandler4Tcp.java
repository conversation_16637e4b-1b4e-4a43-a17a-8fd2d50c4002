package skynet.platform.agent.core.health;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import skynet.platform.agent.core.core.EventService;
import skynet.platform.agent.core.domain.HealthResult;
import skynet.platform.common.domain.HealthParam;

import java.util.Date;
import java.util.concurrent.ThreadFactory;

/**
 * 检测 TCP 端口是否存在 判断服务是否健康
 *
 * <pre>
 * 应用场景：在TCP场景下
 * </pre>
 *
 * <AUTHOR> [2018年11月15日 下午4:47:04]
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class HealthCheckHandler4Tcp extends HealthCheckHandler {

    private Bootstrap bootstrap;
    private NioEventLoopGroup eventLoop;

    private ChannelFuture channelFuture;
    private int consecutiveFailures = 0;
    private ChannelFutureListener channelFutureListener;

    private long responseTime = 0;
    private static final ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("health-checker-tcp-%d").build();

    @Override
    protected void onStart(HealthParam healthParam, HealthListener myHealthListener, EventService eventService) throws Exception {

        this.channelFutureListener = new ChannelFutureListener() {

            @Override
            public void operationComplete(ChannelFuture future) throws Exception {

                HealthResult ret = new HealthResult();
                ret.setAid(bootStatus.getAid());
                ret.setPid(bootStatus.getState().getPid());
                ret.setTime(new Date());
                ret.setOk(future.isSuccess());
                ret.setFrom(this.getClass().getSimpleName());
                ret.setResponseTime(System.currentTimeMillis() - responseTime);
                results.put(ret);
                // 报告事件
                eventService.putHealthCheckEvent(ret);

                if (future.isSuccess()) {
                    consecutiveFailures = 0;
                    log.debug("[Port={}] connect successfully", bootProfile.getPort());
                    // 连接成功
                    myHealthListener.onConnected(bootStatus);
                } else {
                    consecutiveFailures++;
                    log.info("[Port={}] connect failed [index={}].", bootProfile.getPort(), consecutiveFailures);

                    // 超过失败次数，杀进程
                    if (consecutiveFailures > healthParam.getRetryTimes()) {
                        log.info("EXCEED MAX CONSECUTIVE FAILURES, PID={}, APP={}", bootStatus.getState().getPid(), bootStatus.getAid());
                        try {
                            myHealthListener.onClosed(bootStatus);
                        } catch (Exception e) {
                            log.error("close proc pid {} error={}", bootStatus.getState().getPid(), e.getMessage());
                        }
                    }
                }
                future.channel().close();
            }
        };

        eventLoop = new NioEventLoopGroup(1, threadFactory);
        bootstrap = new Bootstrap();
        bootstrap.group(eventLoop).channel(NioSocketChannel.class);
        bootstrap.option(ChannelOption.AUTO_CLOSE, true);
        bootstrap.handler(new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel socketChannel) throws Exception {
            }
        });
        bootstrap.remoteAddress(bootProfile.getIp(), bootProfile.getPort());
    }

    @Override
    protected synchronized void check() throws Exception {
        if (bootstrap == null) {
            log.warn("the bootstrap is null");
            return;
        }
        responseTime = System.currentTimeMillis();
        channelFuture = bootstrap.connect().addListener(channelFutureListener);
    }

    @SuppressWarnings("deprecation")
    @Override
    protected void onClose() throws Exception {
        log.debug("HealthCheckHandler close begin...[aid={};port={}]", getAid(), bootProfile.getPort());

        try {
            if (eventLoop != null) {
                eventLoop.shutdownNow();
                eventLoop.shutdownGracefully();
            }
        } catch (Exception ex) {
            log.error("HealthCheckHandler close eventLoop error={}", ex.getMessage());
        } finally {
            eventLoop = null;
        }
        try {
            if (channelFuture != null) {
                channelFuture.channel().close();
            }
        } catch (Exception ex) {
            log.error("HealthCheckHandler close channelFuture error={}", ex.getMessage());
        } finally {
            channelFuture = null;
            bootstrap = null;
        }
    }
}