package skynet.platform.agent.core.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import skynet.boot.common.domain.Jsonable;

/**
 * agent 特有属性
 *
 * <pre>
 * skynet.agent.out.cache.line.count=500
 * skynet.agent.refresh.delay.seconds=5
 * skynet.agent.refresh.initialDelay.seconds=5
 * skynet.agent.try.boot.size=3
 * </pre>
 *
 * <AUTHOR> [Oct 10, 2017 7:18:10 PM]
 */

/**
 * Skynet Agent Configuration Properties
 *
 * <p>This configuration class contains all agent-specific settings that control
 * the behavior of the Skynet agent service. The agent is responsible for managing
 * service instances on individual nodes in the cluster.</p>
 *
 * <p>All properties support Spring Cloud Config refresh scope, allowing for
 * dynamic configuration updates without service restart.</p>
 *
 * <p>Configuration prefix: {@code skynet.agent}</p>
 *
 * <AUTHOR>
 * @since 3.4.15
 * @see org.springframework.cloud.context.config.annotation.RefreshScope
 */
@Getter
@Setter
@RefreshScope
@Component
public class AgentProperties extends Jsonable {

    /**
     * Maximum number of output cache lines to retain in memory.
     *
     * <p>This setting controls how many lines of service output (stdout/stderr)
     * are cached in memory for quick retrieval. Higher values provide more
     * historical output but consume more memory.</p>
     *
     * <p>Default: 500 lines</p>
     * <p>Range: 100-10000 (recommended)</p>
     */
    @Value("${skynet.agent.out.cache.line.count:500}")
    private int outCacheLineCount;

    /**
     * Delay in seconds between agent refresh cycles.
     *
     * <p>This controls how frequently the agent checks for configuration changes,
     * service status updates, and performs maintenance tasks. Lower values provide
     * faster response to changes but increase system load.</p>
     *
     * <p>Default: 8 seconds</p>
     * <p>Range: 1-60 seconds (recommended)</p>
     */
    @Value("${skynet.agent.refresh.delay.seconds:8}")
    private int delay;

    /**
     * Initial delay in seconds before starting the first refresh cycle.
     *
     * <p>This provides a grace period during agent startup to allow all
     * components to initialize properly before beginning regular operations.</p>
     *
     * <p>Default: 8 seconds</p>
     * <p>Range: 1-30 seconds (recommended)</p>
     */
    @Value("${skynet.agent.refresh.initialDelay.seconds:8}")
    private int initialDelay;

    /**
     * Maximum number of boot attempts for failed services.
     *
     * <p>When a service fails to start, the agent will retry up to this many
     * times before marking the service as permanently failed. This prevents
     * infinite retry loops while allowing for transient failures.</p>
     *
     * <p>Default: 3 attempts</p>
     * <p>Range: 1-10 attempts (recommended)</p>
     */
    @Value("${skynet.agent.try.boot.size:3}")
    private int trySize;

    /**
     * Enable NUMA (Non-Uniform Memory Access) CPU binding for services.
     *
     * <p>When enabled, the agent will attempt to bind services to specific
     * NUMA nodes to optimize memory access patterns and improve performance
     * on multi-socket systems.</p>
     *
     * <p>Note: This feature requires NUMA-aware hardware and appropriate
     * system permissions. It may not be available in containerized environments.</p>
     *
     * <p>Default: false (disabled)</p>
     */
    @Value("${skynet.agent.numa.bind.enabled:false}")
    private boolean numaBindEnabled;

    /**
     * Number of services to include in each delayed loading group.
     *
     * <p>To prevent system overload during startup, services are loaded in
     * groups with delays between groups. This setting controls the size of
     * each group.</p>
     *
     * <p>Default: 4 services per group</p>
     * <p>Range: 1-20 services (recommended)</p>
     */
    @Value("${skynet.agent.load.delay.size:4}")
    private int loadDelaySize;

    /**
     * Delay in seconds between loading different service groups.
     *
     * <p>This delay helps prevent resource contention and allows the system
     * to stabilize between service group startups. Adjust based on system
     * capacity and service startup time requirements.</p>
     *
     * <p>Default: 8 seconds</p>
     * <p>Range: 1-60 seconds (recommended)</p>
     */
    @Value("${skynet.agent.load.delay.seconds:8}")
    private int loadDelaySec;

    /**
     * Returns a JSON string representation of this configuration object.
     *
     * @return JSON representation of all configuration properties
     */
    @Override
    public String toString() {
        return super.toString();
    }
}