package skynet.platform.agent.core;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.exception.DockerException;
import com.github.dockerjava.api.exception.NotModifiedException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.plexus.util.cli.Commandline;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import skynet.boot.AppContext;
import skynet.boot.common.domain.SampleState;
import skynet.boot.exception.ExceptionExt;
import skynet.boot.metrics.SkynetMetricsService;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.platform.agent.core.boot.BaseBoot;
import skynet.platform.agent.core.core.BootRunner;
import skynet.platform.agent.core.core.BootRunnerManager;
import skynet.platform.agent.core.core.EventService;
import skynet.platform.agent.core.core.StatusService;
import skynet.platform.agent.core.domain.ObservableQueue;
import skynet.platform.agent.core.event.ConfigFileEvent;
import skynet.platform.agent.core.event.DashboardEvent;
import skynet.platform.agent.core.event.Status4ZkService;
import skynet.platform.agent.core.exception.BootException;
import skynet.platform.common.config.PlatformProperties;
import skynet.platform.common.domain.*;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionLabel;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.common.repository.domain.AntNodeUpType;
import skynet.platform.common.utils.ProcessUtils;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * Boot Service - Core Service Management Component
 *
 * <p>This service is the central component responsible for managing the lifecycle
 * of all services (boots) running on a Skynet agent node. It provides comprehensive
 * functionality for service startup, shutdown, monitoring, and cleanup operations.</p>
 *
 * <p>Key responsibilities include:</p>
 * <ul>
 *   <li>Service lifecycle management (start, stop, restart)</li>
 *   <li>Service status monitoring and health checking</li>
 *   <li>Configuration management and updates</li>
 *   <li>Resource cleanup for failed or stopped services</li>
 *   <li>Integration with ZooKeeper for cluster coordination</li>
 *   <li>Docker container management for containerized services</li>
 *   <li>Event publishing for service state changes</li>
 * </ul>
 *
 * <p>This class is implemented as a singleton Spring service and maintains
 * the state of all managed services in memory with persistent storage in
 * ZooKeeper for cluster-wide visibility.</p>
 *
 * <p>Thread Safety: This class is thread-safe and can handle concurrent
 * operations on different services. However, operations on the same service
 * are serialized to prevent race conditions.</p>
 *
 * <AUTHOR> [2018年7月6日 下午1:56:32]
 * @since 3.4.15
 * @see BaseBoot
 * @see BootRunner
 * @see StatusService
 */
@Slf4j
@Service
public class BootService {

    /**
     * 本地没有安装docker，dockerClient将为空
     */
    @Resource
    @Autowired(required = false)
    private DockerClient dockerClient;

    @Autowired
    private IAntConfigService antConfigService;
    @Autowired
    private AppContext appContext;
    @Autowired
    private Status4ZkService status4ZkService;
    @Autowired
    private StatusService statusService;
    @Autowired
    private UpdateService updateService;
    @Autowired
    private ExtConfigFileService extConfigFileService;

    @Autowired
    private EventService eventService;
    @Autowired
    private ConfigFileEvent configFileEvent;
    @Autowired
    private DashboardEvent dashboardEvent;

    @Autowired
    private SkynetMetricsService skynetMetricsService;

    @Autowired
    private BootRunnerManager bootRunnerManager;

    @Autowired
    private SkynetZkProperties skynetZkProperties;

    @Autowired
    private PlatformProperties platformProperties;

    /**
     * Start a service instance based on the provided boot profile.
     *
     * <p>This method performs a complete service startup sequence including:</p>
     * <ol>
     *   <li>Retrieving or creating service status tracking</li>
     *   <li>Loading service configuration from ZooKeeper</li>
     *   <li>Downloading required artifacts and dependencies</li>
     *   <li>Creating configuration files and environment setup</li>
     *   <li>Starting the service process using appropriate boot type</li>
     *   <li>Registering service status in ZooKeeper for cluster visibility</li>
     *   <li>Setting up monitoring and health checking</li>
     * </ol>
     *
     * <p>The method supports various boot types including:</p>
     * <ul>
     *   <li>SpringBoot applications</li>
     *   <li>Java applications</li>
     *   <li>Docker containers</li>
     *   <li>Kubernetes deployments</li>
     * </ul>
     *
     * <p>If the service was previously running, this method will inherit
     * the previous startup count and other relevant state information.</p>
     *
     * @param bootProfile the service profile containing startup configuration,
     *                   including service ID, name, plugin, and basic parameters
     * @return BootStatus object representing the current state of the started service,
     *         including process ID, port information, and runtime status
     * @throws Exception if service startup fails due to configuration errors,
     *                  resource unavailability, or process startup failures
     * @throws IllegalArgumentException if bootProfile is null or contains invalid data
     * @see BootProfile
     * @see BootStatus
     * @see BaseBoot
     */
    public BootStatus start(BootProfile bootProfile) throws Exception {

        skynetMetricsService.counterIncrement("agent.boot.start.count");
        // 优先从上次的状态中获取,主要继承上次的启动次数
        BootStatus bootStatus = getBootStatus(bootProfile.getAid());
        if (bootStatus == null) {
            bootStatus = new BootStatus(bootProfile);
        } else {
            status4ZkService.cancelNode(bootStatus);
        }

        // 构建启动中 的状态，相对于 启动中
        AntNodeState antNodeState = buildAntNodeState(bootProfile);
        bootStatus.setState(antNodeState);
        bootStatus.setPid(0);
        bootStatus.setUp(AntNodeUpType.LOADING);

        // 正式启动前，本地持久化,
        statusService.putBoot(bootStatus);

        try {
            // 通过ZK， 获取 AntActionParam
            AntActionParam antActionParam = getAntActionParam(bootStatus.getProfile());

            AntActionLabel debugAntActionLabel = antActionParam.getBootParam().getActionLabelByCode("debug");

            bootProfile.setBootType(antActionParam.getBootType());
            bootStatus.setAction(antActionParam);
            if (debugAntActionLabel != null) {
                bootStatus.setDebug(debugAntActionLabel.getValue());
            }
            // 根据 getBootType 获取 baseBoot
            String beanName = String.format("boot.%s", antActionParam.getBootType()).toLowerCase();
            BaseBoot baseBoot = this.appContext.getSpringContext().getBean(beanName, BaseBoot.class);
            baseBoot.init(bootProfile, bootStatus.getAction());
            bootStatus.setProtocol(antActionParam.getBootParam().getActionProtocol());
            bootStatus.setPort(baseBoot.getAppPort());
            bootStatus.setAppPort(baseBoot.getAppPort());
            bootStatus.setExtPorts(baseBoot.getExtPorts());

            // 下载关联资源文件
            updateService.downloadArtifacts(antActionParam, baseBoot.getAppEnvironment());

            //扩展配置文件
            if (!antActionParam.getBootParam().getExtConfigItems().isEmpty()) {
                log.info("Create Local Ext Config File.");
                extConfigFileService.create(bootProfile.getAid(), antActionParam.getBootParam().getExtConfigItems(), baseBoot.getAppEnvironment());
            }

            // 导入 关联的 dashboard 图表定义
            dashboardEvent.onEvent(baseBoot);

            // 设置本地配置文件
            configFileEvent.onEvent(baseBoot);
            bootStatus.setAppConfigFile(baseBoot.getAppConfigFile().toString());

            String appCmd = composeCommand(baseBoot);
            bootStatus.setCmd(appCmd);

            BootRunner bootRunner = bootRunnerManager.getBootRunner(bootProfile.getAid(), antActionParam.getBootParam().isLogCollection());
            bootRunner.start(appCmd, baseBoot.getAppEnvironment());

            bootStatus.setPid(bootRunner.getPid());
            bootStatus.setExtProps(baseBoot.getExtProps());
            bootStatus.setServerContextPath(baseBoot.getServerContextPath());
            bootProfile.setPath(bootStatus.getServerContextPath());

            //设置  DiscoveryMetadata   add by lyhu 2024年12月02日
            Map<String, Object> vars = platformProperties.getDiscoveryMetadata();
            if (vars != null) {
                vars = JSON.parseObject(JSON.toJSONString(vars), new TypeReference<Map<String, Object>>() {
                });
                Map<String, Object> discoveryMetadata = baseBoot.getAppEnvironment().replacePlaceholder(vars);
                antNodeState.setMetadata(discoveryMetadata);
            }

            // 启动后 本地持久化
            statusService.putBoot(bootStatus);
        } catch (BootException e) {
            log.error("Start action= {} Err= {}", bootProfile.getAid(), e.getMessage());
            SampleState sampleState = new SampleState("startup failed:" + e.getMessage(), null);
            bootStatus.getState().setState(sampleState);
            bootStatus.setStatus(sampleState);
            this.setDown(bootStatus);
        } catch (Exception e) {
            log.error(String.format("Start action=%s Err=%s", bootProfile.getAid(), e.getMessage()), e);
            bootStatus.getState().setState(new SampleState("startup failed，unknown exception:", e));
            bootStatus.setStatus(new SampleState(String.format("startup recognition:%s. StackTrace: %s", e.getMessage(), JSON.toJSONString(e.getStackTrace())), e));
            this.setDown(bootStatus);
        }

        return bootStatus;
    }

    private String composeCommand(BaseBoot baseBoot) throws Exception {

        StringBuilder cmdLines = new StringBuilder();

        for (Map.Entry<String, String> item : baseBoot.getWorkEnvs().entrySet()) {
            cmdLines.append(String.format("export %s=%s%s", item.getKey(), item.getValue(), System.lineSeparator()));
        }

        if (StringUtils.isNoneBlank(baseBoot.getWorkHome())) {
            cmdLines.append(String.format("chmod -R +x %s%s", baseBoot.getWorkHome(), System.lineSeparator()));
        }

        // 构建命令行 // 执行命令行
        Commandline commandline = baseBoot.getCommand();
        cmdLines.append(commandline.toString().replace("/bin/sh -c", ""));
        return cmdLines.toString();
    }


    public void restoreRunner(BootProfile bootProfile) {
        try {
            AntActionParam antActionParam = getAntActionParam(bootProfile);
            bootRunnerManager.getBootRunner(bootProfile.getAid(), antActionParam.getBootParam().isLogCollection());
        } catch (Exception e) {
            log.error("restore [{}] runner err:{}", bootProfile.getAid(), e.getMessage(), e);
        }
    }

    private AntNodeState buildAntNodeState(BootProfile bootProfile) {// 构建输出存储器

        AntNodeState antNodeState = new AntNodeState(appContext, skynetZkProperties);
        antNodeState.setAntId(bootProfile.getAid());
        antNodeState.setName(bootProfile.getFullName());
        antNodeState.setStartTime(new Date());
        antNodeState.getProjectVersion().clear();
        antNodeState.setPlugin(bootProfile.getPlugin());
        //antNodeState.setState(null);
        antNodeState.setActionTitle(bootProfile.getName());
        antNodeState.setServiceId(bootProfile.getCode());

        log.debug("--Action {} NodeState------------------------------", bootProfile.getAid());
        log.debug("{}", antNodeState);
        log.debug("---------------------------------------------------");
        return antNodeState;
    }

    public synchronized void stop(long pid) throws Exception {
        Map<String, BootStatus> runningBoot = this.getAllBoot();
        for (Entry<String, BootStatus> item : runningBoot.entrySet()) {
            if (pid == 0 || item.getValue().getState().getPid() == pid) {
                this.stop(item.getKey());
            }
        }
    }

    public synchronized void setDown(long pid) throws Exception {
        log.debug("set the pid={} down", pid);
        long begin = System.currentTimeMillis();

        // 相关进程不存在，aid关联的文件还存在
        Map<String, BootStatus> runningBoot = this.getAllBoot();
        for (Entry<String, BootStatus> item : runningBoot.entrySet()) {
            if (pid == 0 || item.getValue().getState().getPid() == pid) {
                BootStatus bootStatus = item.getValue();
                this.setDown(bootStatus);
            }
        }
        log.debug("set the pid={} down [cost={}]", pid, System.currentTimeMillis() - begin);
    }

    private void setDown(BootStatus bootStatus) throws Exception {
        try {
            bootStatus.setDown(bootStatus.getBootIndex() + 1);
            // kill 与aid 相关的所有进程
            this.clean(bootStatus);
        } catch (Exception e) {
            log.error("{} setDown Err={}", bootStatus.getAid(), ExceptionExt.mergedMessage(e));
        }
        statusService.putBoot(bootStatus);
    }

    public boolean isRunning(String aid) throws Exception {
        return bootRunnerManager.isRunning(aid);
    }

    public void setUp(String aid) throws Exception {
        log.info("Set the actionId={} UP", aid);

        long begin = System.currentTimeMillis();
        BootStatus bootStatus = statusService.getBoot(aid);
        if (bootStatus == null) {
            log.warn("[SetUp] the aid={} bootStatus is Null", aid);
            return;
        }

        // 启动次数 重置，同时将子进程树，持久化
        bootStatus.setBootIndex(0);
        bootStatus.setUp(AntNodeUpType.UP);
        AntNodeState antNodeState = bootStatus.getState();

        //获取所有的孩子进程
        bootStatus.setPidChildren(ProcessUtils.getChildrenList(antNodeState.getPid()));

        // 汇报ZK（服务注册与发现）
        List<String> zkPaths = status4ZkService.reportZK(bootStatus);
        bootStatus.setZkPaths(zkPaths);

        statusService.putBoot(bootStatus);
        log.info("Set the {} UP [cost={}ms]", aid, System.currentTimeMillis() - begin);
    }

    /**
     * 检测 服务注册中心 上的 状态是否掉线
     *
     * <pre>
     * 如果掉线，自动补偿注册
     * </pre>
     *
     * @param bootStatus
     * @throws IOException
     */
    public void checkZkOffline(BootStatus bootStatus) throws IOException {
        log.debug("checkZkOffline [aid={};up={}]", bootStatus.getAid(), bootStatus.getProfile().getUp());
        if (bootStatus.getProfile().getUp() != AntNodeUpType.UP) {
            this.antConfigService.cancelNode(bootStatus.getZkPaths());
            return;
        }

        List<String> offlineList = status4ZkService.checkZkOffline(bootStatus);
        // 如果有变化
        if (!offlineList.isEmpty()) {
            // 防止多个线程同时在修改这个status，获取最新的状态，重新赋值
            BootStatus lastBootStatus = statusService.getBoot(bootStatus.getAid());
            lastBootStatus.setZkPaths(offlineList);
            statusService.putBoot(lastBootStatus);
        }
    }

    /**
     * 根据进程Id，Kill 所有的子进程，删除ZK路径，删除本地Boot状态
     */
    protected synchronized void stop(String aid) throws Exception {
        Assert.hasText(aid, "The aid is blank.");
        aid = aid.trim();
        long begin = System.currentTimeMillis();
        log.info("Stop [aid={}]", aid);
        BootStatus bootStatus = statusService.getBoot(aid);
        if (bootStatus != null) {
            try {
                this.clean(bootStatus);
            } catch (Exception e) {
                log.error(String.format("clean action [%s] status err: %s", bootStatus.getAid(), e.getMessage()));
            } finally {
                statusService.deleteBoot(aid);
            }
        }
        log.info("Stop [aid={}].[cost={}ms]", aid, System.currentTimeMillis() - begin);
    }

    /**
     * 根据aId，clean 所有的子进程，删除ZK路径
     */
    private synchronized void clean(BootStatus bootStatus) throws Exception {
        long begin = System.currentTimeMillis();
        String aid = bootStatus.getAid();
        log.info("Clean [aid={}] begin..", aid);

        //防止，异常情况下，actionParam 状态没有写入到状态文件中
        if (bootStatus.getAction() == null) {
            bootStatus.setAction(getAntActionParam(bootStatus.getProfile()));
        }
        if (bootStatus.getAction().getBootType() == BootType.DockerBoot) {
            //改用 AID，不然同一个docker不能启动多个实例
            String dockerName = "skynet-" + bootStatus.getAid().replace("@", "-");

            log.debug("docker name={}", dockerName);
            if (dockerClient == null) {
                log.warn("docker is not install");
                throw new BootException("the docker runtime is not install");
            }

            try {
                log.debug("Stop docker container [{}]...", dockerName);
                dockerClient.stopContainerCmd(dockerName).exec();
                log.debug("Stop docker ok.[{}]", dockerName);
            } catch (NotModifiedException ex) {
                log.error("Stop docker error.[{}]", ex.getMessage());
            } catch (DockerException ex) {
                if (StringUtils.isBlank(ex.getMessage())) {
                    log.error("Stop docker error.", ex);
                } else {
                    log.error("Stop docker={} error.[{}]", dockerName, ex.getMessage());
                }
            }

            try {
                log.debug("Remove docker container [{}]...", dockerName);
                dockerClient.removeContainerCmd(dockerName).exec();
                log.debug("Remove docker ok.[{}]", dockerName);
            } catch (DockerException ex) {
                log.error("Remove docker={} error.[{}]", dockerName, ex.getMessage());
                log.debug("the container has been stopped. no need to stop[{}]", dockerName);
            }
        }

        log.info("Stop {} bootRunner.", aid);

        BootParam bootParam = bootStatus.getAction().getBootParam();
        int signal = bootParam == null ? 9 : bootParam.getKillSignal();
        boolean isLogCollection = bootParam != null && bootParam.isLogCollection();
        BootRunner bootRunner = bootRunnerManager.getBootRunner(aid, isLogCollection);

        try {
            bootRunner.stop(signal);
        } catch (Exception ex) {
            log.error("Stop {} bootRunner error.[{}]", aid, ex.getMessage());
        }

        bootRunnerManager.removeBootRunner(aid, bootStatus.isDebug());

        // KILL 所有子进程，防止有野进程
        for (int pid : bootStatus.getPidChildren()) {
            ProcessUtils.kill(pid, 9, 2);
        }

        // 取消所有的在线节点
        if (bootStatus.getZkPaths() != null) {
            status4ZkService.cancelNode(bootStatus);
        }

        //删除配置文件
        configFileEvent.destroy(bootStatus);

        // 报告事件
        eventService.putStopProcessEvent(bootStatus.getAid(), bootStatus.getState().getPid());
        log.info("Clean [aid={}] end.[cost={}ms]", aid, System.currentTimeMillis() - begin);
    }


    private AntActionParam getAntActionParam(BootProfile bootProfile) throws Exception {
        AntActionParam antActionParam = this.antConfigService.getActionParam(bootProfile.getPlugin(), bootProfile.getCode());
        if (antActionParam.getBootParam() == null) {
            antActionParam.setBootParam(new BootParam());
        }
        log.trace("antActionParam={}", antActionParam);
        return antActionParam;
    }

    /**
     * 进程已经停止，状态文件还存在的
     *
     * @throws Exception
     */
    public void cleanBootStatus() throws Exception {
        //
    }

    /**
     * Key: aid
     *
     * @return
     * @throws IOException
     */
    public Map<String, BootStatus> getAllBoot() throws IOException {
        return statusService.getBoots().stream().collect(Collectors.toMap(BootStatus::getAid, bootStatus -> bootStatus));
    }

    public BootStatus getBootStatus(String aid) throws IOException {
        if (StringUtils.isBlank(aid)) {
            throw new IllegalArgumentException("aid is blank");
        }
        return statusService.getBoot(aid);
    }

    public List<String> getStds(String aid, boolean isErr) {
        ObservableQueue<String> queue = bootRunnerManager.getStdQ(aid);
        return queue.getList();
    }

    public ObservableQueue<String> getStdQ(String aid) {
        return bootRunnerManager.getStdQ(aid);
    }
}
