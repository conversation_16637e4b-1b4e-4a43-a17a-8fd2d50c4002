package skynet.platform.agent.core.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.agent.core.BootService;
import skynet.platform.agent.core.HealthService;
import skynet.platform.agent.core.core.EventService;
import skynet.platform.agent.server.AntServerContext;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.repository.domain.AntNodeState;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@ExposeSwagger2
@RestController
@RequestMapping(value = "/skynet/agent", produces = {MediaType.APPLICATION_JSON_VALUE})
public class AgentController {

    private static final Date startTime = new Date();

    private final EventService eventService;
    private final HealthService healthService;
    private final BootService bootService;
    private final AntServerContext antServerContext;

    public AgentController(EventService eventService, HealthService healthService, BootService bootService, AntServerContext antServerContext) {
        this.eventService = eventService;
        this.healthService = healthService;
        this.bootService = bootService;
        this.antServerContext = antServerContext;
    }

    /**
     * 显示主页内容
     *
     * @return 系统信息
     */
    @GetMapping("/_help")
    public Map<String, Object> index() {
        log.debug("_help");
        Map<String, Object> map = new HashMap<>(2);
        map.put("welcome", "hello skynet agent service!");
        map.put("start_time", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        return map;
    }

    @GetMapping("/event")
    public Object event() {
        log.debug("get.agent.event");
        return eventService.getEvents();
    }

    @DeleteMapping("/event/remove")
    public Object event2remove() {
        log.debug("get.event2remove.event");
        return eventService.getEventsAndRemove();
    }

    @GetMapping("/boot-status")
    public Object getBootStatus() throws IOException {
        log.debug("get.agent.nodes");
        return bootService.getAllBoot();
    }

    @GetMapping("/boot-status/{antId}")
    public BootStatus getBootStatus(@PathVariable("antId") String antId) throws IOException {
        log.debug("get.agent.boot.status:{}", antId);
        return bootService.getBootStatus(antId);
    }

    @GetMapping("/node-status")
    public Object getNodeStatus() {
        return antServerContext.getAntNodeState();
    }

    @GetMapping("/node-status/{antId}")
    public AntNodeState getNodeStatus(@PathVariable("antId") String antId) throws IOException {
        log.debug("get.agent.node.status:{}", antId);
        BootStatus bootStatus = bootService.getBootStatus(antId);
        return bootStatus != null ? bootStatus.getState() : null;
    }

    @GetMapping("/stdout/{antId}")
    public Object stdout(@PathVariable("antId") String antId) {
        log.debug("get.agent.stdout." + antId);
        return bootService.getStds(antId, false);
    }

    @GetMapping("/stderr/{antId}")
    public Object stderr(@PathVariable("antId") String antId) {
        log.debug("get.agent.stderr." + antId);
        return bootService.getStds(antId, true);
    }

    @GetMapping("/health/{antId}")
    public Object health(@PathVariable("antId") String antId) {
        log.debug("get.agent.health." + antId);
        return healthService.getResults(antId);
    }
}