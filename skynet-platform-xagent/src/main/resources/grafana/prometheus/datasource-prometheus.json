{
    "id": 10,
    "orgId": 1,
    "name": "prometheus",
    "type": "prometheus",
    "typeLogoUrl": "public/app/plugins/datasource/prometheus/img/prometheus_logo.svg",
    "access": "proxy",
    "url": "${url}",
    "password": "",
    "user": "",
    "database": "",
    "isDefault": false,
    "jsonData": {
        "httpMethod": "GET",
        "keepCookies": [],
        "timeInterval": "10s"
    },
    "basicAuth": ${basicAuth},
    "basicAuthUser": "${user}",
    "basicAuthPassword": "${pwd}",
    "readOnly": false
}