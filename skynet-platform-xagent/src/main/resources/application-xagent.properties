#------------------------------------------------------------------------------------------------------
# 基础设置 Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=6230
server.servlet.session.cookie.name=skynet-agent
skynet.platform.agent.enabled=true
skynet.action-point=ant-xagent@ant
skynet.action-name=${skynet.action-point}
skynet.action-id=${skynet.action-point}
skynet.action-desc=${skynet.action-point}
skynet.action-code=ant-xagent
skynet.zookeeper.enabled=true
#------------------------------------------------------------------------------------------------------
# spring.cloud.zookeeper Settings
#------------------------------------------------------------------------------------------------------
spring.application.name=ant-xagent
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.register=true
spring.cloud.zookeeper.connect-string=${skynet.zookeeper.server_list}
spring.cloud.zookeeper.discovery.root=${skynet.zookeeper.cluster_name:skynet}/discovery
spring.cloud.zookeeper.discovery.instance-host=${skynet.ip-address}
#------------------------------------------------------------------------------------------------------
# 系统参数 System Parameters
#------------------------------------------------------------------------------------------------------
skynet.k8s.namespace=default
#------------------------------------------------------------------------------------------------------
# 认证与安全配置 Authentication & Security Configuration
#------------------------------------------------------------------------------------------------------
skynet.auth.api-key=skynet
skynet.auth.api-secret=SKYNET_API_SECRET_PLACEHOLDER
skynet.security.enabled=true
skynet.security.base-auth.enabled=true
skynet.security.base-auth.path-patterns=/skynet/config/**,/grafana/**,/actuator/prometheus
skynet.security.base-auth.ignore-patterns=
skynet.security.base-auth.actuator-security-enabled=false
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=skynet2230
skynet.security.sign-auth.enabled=true
skynet.security.sign-auth.app.skynet=${skynet.auth.api-secret}
skynet.security.sign-auth.path-patterns=/skynet/agent/**,/actuator/**
skynet.security.sign-auth.ignore-patterns=/actuator/health,/actuator/prometheus
skynet.security.form-auth.enabled=false
#------------------------------------------------------------------------------------------------------
# 日志配置 Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.file.path=../log
logging.file.name=<EMAIL>
logging.config=classpath:logback-xagent.xml
#------------------------------------------------------------------------------------------------------
# 其他配置 Other Settings
#------------------------------------------------------------------------------------------------------
# disabled agent swagger
skynet.api.swagger2.enabled=false
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
management.endpoint.env.show-values=always
i18n.language=zh_CN