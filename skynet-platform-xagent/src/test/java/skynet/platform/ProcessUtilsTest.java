package skynet.platform;

import org.junit.Test;
import skynet.platform.common.utils.ProcessUtils;

import java.util.Arrays;
import java.util.List;

public class ProcessUtilsTest {

    /**
     * 具体的业务测试
     *
     * @throws Exception
     */
    @Test
    public void test() throws Exception {

        List<Integer> pids = Arrays.asList(73566, 73581, 73572, 73556);
        for (Integer pid : pids) {
            long cost = System.currentTimeMillis();
            for (int i = 0; i < 10; i++) {

                boolean exist = ProcessUtils.exist(pid);
                System.out.println(exist);
            }
            System.err.println(System.currentTimeMillis() - cost);
        }

        for (Integer pid : pids) {
            ProcessUtils.kill(pid);

            boolean exist = ProcessUtils.exist(pid);
            System.out.println(exist);
        }
    }
}