package skynet.platform.agent.health;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.agent.core.health.HealthListener;

import java.util.concurrent.TimeUnit;

/**
 * tcp客户端
 *
 * <AUTHOR> qq: 408365330
 */
@Slf4j
public class TcpClient implements AutoCloseable {
    private EventLoopGroup loop = new NioEventLoopGroup();
    private final String mIp;
    private final int mPort;
    private Bootstrap bootstrap;
    private ChannelFuture channelFuture;
    private final HealthListener mListener;
    private int tryCount = 0;

    /**
     * 尝试最大次数
     */
    private final int maxTryCount;
    /**
     * 尝试相隔时间
     */
    private final int intervalSeconds;

    private boolean isFirst = true;

    /**
     * 任务
     */
    public TcpClient(String ip, int port, int intervalSeconds, int retryTimes, HealthListener listener) {
        this.mIp = ip;
        this.mPort = port;
        this.mListener = listener;
        this.maxTryCount = retryTimes;
        this.intervalSeconds = intervalSeconds;
    }

    /**
     * 连接tcp
     */
    public void connect() {
        bootstrap = new Bootstrap();
        createBootstrap(bootstrap, loop);
    }

    @Override
    public void close() throws Exception {
        try {
            if (loop != null) {
                loop.shutdownGracefully();
            }
            if (channelFuture != null) {
                channelFuture.channel().close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            channelFuture = null;
            loop = null;
            bootstrap = null;
        }
    }

    private Bootstrap createBootstrap(Bootstrap bootstrap, EventLoopGroup eventLoop) {
        if (bootstrap != null) {
            final MyInboundHandler handler = new MyInboundHandler(this);
            if (eventLoop != null) {
                bootstrap.group(eventLoop);
            }
            bootstrap.channel(NioSocketChannel.class);
            bootstrap.option(ChannelOption.SO_KEEPALIVE, true);
            bootstrap.handler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel socketChannel) throws Exception {
                    socketChannel.pipeline().addLast(handler);
                }
            });
            bootstrap.remoteAddress(this.mIp, this.mPort);
            channelFuture = bootstrap.connect().addListener(new ConnectionListener(this));
        }
        return bootstrap;
    }

    class MyInboundHandler extends SimpleChannelInboundHandler<String> {
        private final TcpClient tcpClient;

        public MyInboundHandler(TcpClient tcpClient) {
            this.tcpClient = tcpClient;
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            super.channelInactive(ctx);
            log.warn("[Port={}]断开了", mPort);
            final EventLoop loop = channelFuture.channel().eventLoop();
            loop.schedule(() -> {
                tcpClient.createBootstrap(new Bootstrap(), loop);
            }, intervalSeconds, TimeUnit.SECONDS);

        }

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, String msg) throws Exception {
        }
    }

    class ConnectionListener implements ChannelFutureListener {
        private final TcpClient tcpClient;

        public ConnectionListener(TcpClient tcpClient) {
            this.tcpClient = tcpClient;
        }

        @Override
        public void operationComplete(ChannelFuture channelFuture) throws Exception {
            if (!channelFuture.isSuccess()) {
                log.info("[Port={}]重连中……", mPort);
                if (isFirst) {
                    if (tryCount == 30) {// 尝试超过次数
                        cancellConnect();
                    } else {
                        tryConnect();
                    }
                } else {
                    if (tryCount == maxTryCount) {
                        cancellConnect();
                    } else {
                        tryConnect();
                    }
                }
            } else {
                tryCount = 0;
                isFirst = false;
                log.info("[Port={}]连接成功", mPort);
                // 连接成功
                if (mListener != null) {
                    //	mListener.onConnected(mIp, mPort);
                    mListener.onConnected(null);

                }
            }
        }

        /**
         * 尝试连接
         */
        private void tryConnect() {
            log.info("[Port={}]第{}次尝试连接", mPort, tryCount);
            tryCount++;
            final EventLoop loop = channelFuture.channel().eventLoop();
            loop.schedule(() -> {
                tcpClient.createBootstrap(new Bootstrap(), loop);
            }, intervalSeconds, TimeUnit.SECONDS);
        }

        /**
         * 取消尝试连接
         */
        private void cancellConnect() throws Exception {
            if (mListener != null) {
//				mListener.onClosed(mIp, mPort);
                mListener.onClosed(null);
                log.info("[Port={}]取消尝试,触发事件", mPort);
            }
        }
    }

}