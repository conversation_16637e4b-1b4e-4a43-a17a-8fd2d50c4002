<h1>skynet-util使用指南</h1>

<!-- TOC -->

- [1. 获取脚本](#1-获取脚本)
- [2. 功能](#2-功能)
- [3. 传参](#3-传参)
  - [3.1. 通过命令行参数](#31-通过命令行参数)
  - [3.2. 通过环境变量](#32-通过环境变量)
- [4. 参数详解](#4-参数详解)
  - [4.1. xtemplate_paths](#41-xtemplate_paths)
  - [4.2. xtemplate_config_url](#42-xtemplate_config_url)
  - [4.3. xtemplate_log_level](#43-xtemplate_log_level)
  - [4.4. xtemplate_log_path](#44-xtemplate_log_path)
  - [4.5. -e](#45--e)
- [5. 模板文件书写规则](#5-模板文件书写规则)
- [6. 变量替换优先级](#6-变量替换优先级)
- [7. 案例](#7-案例)

<!-- /TOC -->

# 1. 获取脚本

下载地址： http://172.31.164.7/other/skynet-util

# 2. 功能

skynet-util是一个用于从模板文件生成文件的工具，主要用于配置文件生成，我们一般将这个脚本打包进docker镜像，在系统启动时生成配置文件，达到动态修改容器内部配置的目的。

skynet-util采用python编写，所以需要在制作镜像时添加python2环境。

# 3. 传参

## 3.1. 通过命令行参数
执行`'python skynet-util -h'` 可以查看所有命令行参数：

```
bash-4.4# skynet-util -h

Usage: python skynet-util [OPTIONS]

Options:
  --xtemplate_paths    List of path pairs, splitted by ','.Each pair is splitted by ':' : the path before ':' is source, and the path after ':' is desitnation.
  --xtemplate_config_url    Skynet action config url.
  --xtemplate_log_level   Logging level of this script.'info', 'debug','error' is supported. Default value is 'info'.
  --xtemplate_log_path    File path of log.
  -e    Config passed by commandline options.The format of each config is {key}={value}
  -h    Print the help.

Samples:
    python skynet-util --xtemplate_paths /a/b/c:/d,/foo/:/xxx --xtemplate_config_url http://*************:6230/config/demo 
    --xtemplate_log_level debug --xtemplate_log_path /iflytek/log/util.log -e demo_key1=demo_value1 -e demo_key2=demo_value2

```

## 3.2. 通过环境变量

可以在执行脚本时指定以下环境变量 ： `'xtemplate_paths'` /  `'xtemplate_config_url'` / `'xtemplate_log_level'`/ `'xtemplate_log_path'`，达到和命令行参数同样的效果。

# 4. 参数详解

## 4.1. xtemplate_paths

xtemplate_paths 用于描述`'模板文件来自何处'`以及`'文件生成到哪里'`。我们用冒号':'将一个模板源路径和一个文件目标路径隔开，称为一个pair。多个路径pair用逗号','隔开。

一个pair冒号前后既可以写目录路径，也可以写具体文件路径。当源路径目录和目标目录是同一个时，冒号可以省略，pair只写一个路径。

当模板来源路径是目录时，skynet-util会查找该路径下所有以xtemplate为后缀的文件作为模板文件，并依次进行变量替换并生成文件到目标路径下。

举例

|pair写法|含义|
|-|-|
|/abc|从/abc目录下查找所有xtemplate为后缀的文件，进行变量替换，并把后缀去掉作为生成的目标文件名。该写法省略了目标目录，默认把文件生成到原目录。|
|/abc:/d|从/abc目录下查找所有xtemplate为后缀的文件，进行变量替换，并把后缀去掉作为生成的目标文件名, 目标文件被生成到/d目录下|
|/abc/config.json.xtemplate| 变量替换生成/abc/config.json|
|/abc/config.json.xtemplate:/d| 变量替换生成/d/config.json(前提/d目录存在,否则生成/d文件)|
|/abc/config.json.xtemplate:/d/config.txt| 变量替换生成/d/config.txt|

## 4.2. xtemplate_config_url

skynet-xagent提供了一个WEB接口：/skynet/config/${SKYNET_ACTION_ID},该接口返回服务最终的属性配置，该接口返回的HTTP类型是text/plain，它和基于key/value的properties文件格式是一样的。

xtemplate_config_url 通常传入该接口地址，skynet-util会解析接口返回的内容，将其中的键值对作为变量替换的字典。

## 4.3. xtemplate_log_level

该脚本工具的日志级别，默认为info。

## 4.4. xtemplate_log_path

该脚本工具的日志路径，如果不传此参数，默认不生成日志。

## 4.5. -e

通过命令行传递变量替换的字典键值对。该选项可以在命令行中多次出现，比如 `'-e demo_key1=demo_value1 -e demo_key2=demo_value2'`

# 5. 模板文件书写规则

模板文件没有具体格式要求，可以是 普通txt，html，json，xml等任何文本格式。

模板变量使用 `$skynet{var_name:default-value}`标记，var_name是变量名,default-value是变量不存在时的变量默认值。

`':default-value'`可以省略。不提供默认值时，如果找不到指定的变量，脚本会抛异常并退出。

# 6. 变量替换优先级 

变量替换的字典有三个来源：
* 脚本执行时的环境变量
* xtemplate_config_url 指定接口返回的键值对
* -e 参数指定的键值对

三个来源的优先级从高到低分别是：'环境变量' > '-e' > 'xtemplate_config_url'

# 7. 案例

假设有如下文件
```
-/mount
 |-/conf
   |-/main.conf.xtemplate
  -/logging
   |-/logging.xml.xtemplate
-/opt
 |-/demo
   |-/conf/
   |-/logging/
```
main.conf.xtemplate文件内容如下
```
app.name=$skynet{app.name}
app.ip=$skynet{app.ip:127.0.0.1}
app.port=$skynet{app.port:8080}
```

logging.xml.xtemplate文件内容如下
```
<logger name="xxx" level="info">
  <log-file path="$skynet{log.file.path}"/>
</logger>
```

GET http://*************:6230/skynet/config/xxx@yyy 返回的HTTP Body如下：
```
app.name=iflytek
log.file.path=/var/log/demo.log
```

脚本test.sh如下：
```
export xtemplate_paths='/mount/conf:/opt/demo/conf,/mount/logging:/opt/demo/logging'
export xtemplate_log_path='/var/log/skynet-util.log'

skynet-util -e app.port=9000 --xtemplate_config_url http://*************:6230/skynet/config/xxx@yyy 
```

执行./test.sh 将生成如下文件：

[1] /opt/demo/conf/main.conf

内容为
```
app.name=iflytek
app.ip=127.0.0.1
app.port=9000
```

[2] /opt/demo/logging/logging.xml

内容为
```
<logger name="xxx" level="info">
  <log-file path="/var/log/demo.log"/>
</logger>
```