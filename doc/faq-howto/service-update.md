<h1>如何升级被托管服务</h1>

我们推荐将被托管服务的程序打包上传到skynet资源仓库目录中(${SKYNET_HOME}/repo/)，然后在服务定义中用关联文件的方式进行资源关联。这样我们只需要更新仓库中的文件。

以 [SpringBoot Demo服务托管](../quickstart/springboot/springboot-integration.md) 为例，该demo服务关联了cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar这个文件，关联的目标目录是${SKYNET_PLUGIN_HOME} (即 /iflytek/server/skynet/plugin/skynet-doc-demo ) 。 服务定义中的`'工作目录'`设置的是${SKYNET_PLUGIN_HOME}，skynet-xagent执行 java -jar 命令前会先cd到该目录。

skynet-xagent启动该服务前，会检查${SKYNET_PLUGIN_HOME}/cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar是否存在，如果不存在就从skynet-xmanager的相关仓库服务接口下载该文件到${SKYNET_PLUGIN_HOME}目录下。如果文件存在，则比对${SKYNET_PLUGIN_HOME}/cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar.md5 与 仓库目录中的cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar.md5文件值是否相同，如果不同就会更新${SKYNET_PLUGIN_HOME}/cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar。

因此，如果我们要更新cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar，只需要替换skynet-xmanger所在服务器上的`'${SKYNET_HOME}/repo/skynet-doc-demo/cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar'`，并删除 `'${SKYNET_HOME}/repo/skynet-doc-demo/cloud-doc-demo-springboot2-1.0.0-SNAPSHOT.jar.md5'` 即可。新版本的程序会在服务启动时自动分发到服务部署的节点。