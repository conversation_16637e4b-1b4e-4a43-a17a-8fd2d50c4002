<h1>SpringBoot Demo日志采集</h1>

<!-- TOC -->

- [1. 目标](#1-目标)
- [2. 修改服务定义](#2-修改服务定义)
- [3. 部署扩展组件-日志采集](#3-部署扩展组件-日志采集)
- [4. 查看日志](#4-查看日志)

<!-- /TOC -->
# 1. 目标

本章节将利用skynet提供的扩展组件对前面章节开发的`"Skynet文档-springboot demo"`服务进行日志收集。

# 2. 修改服务定义

在`'基本信息'` 中 设置`'启用日志收集'`设为`'是'`

![alt](res/logging-service-def-1.png)

在`'日志配置'`中设置服务级的日志级别 ： 
```
skynet.boot.demo=INFO
```

![alt](res/logging-service-def-2.png)

修改后重启`"Skynet文档-springboot demo"`服务。


# 3. 部署扩展组件-日志采集

参考 [日志采集-系统部署](../../extension/skynet-log/log-deploy.md)

我们至少需要部署以下服务：
* Skynet日志-ES集群-node1
* Skynet日志-Kafka
* Skynet日志-Kibana
* Skynet日志-Logstash

# 4. 查看日志

访问 http://{ip}:8600/api/foo1 和 http://{ip}:8600/api/foo2, 生成INFO日志

访问 http://{ip}:15601 `(Skynet日志-Kibana)`

![alt](res/logging-kibana-1.png)

查询日志

查询框输入： `logger:skynet.boot.demo.*`

![alt](res/logging-kibana-2.png)

