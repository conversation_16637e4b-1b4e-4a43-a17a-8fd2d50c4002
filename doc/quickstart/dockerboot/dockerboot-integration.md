<h1>Doocker Demo服务托管</h1>

<!-- TOC -->

- [1. 上传资源](#1-上传资源)
- [2. 服务定义](#2-服务定义)
  - [2.1. 方式一： 导入](#21-方式一-导入)
  - [2.2. 方式二：填写表单](#22-方式二填写表单)
    - [2.2.1. 基本信息](#221-基本信息)
    - [2.2.2. docker参数](#222-docker参数)
    - [2.2.3. 健康检查](#223-健康检查)
    - [2.2.4. 文件关联](#224-文件关联)
    - [2.2.5. 属性配置](#225-属性配置)
- [3. 启动服务](#3-启动服务)
- [4. 验证效果](#4-验证效果)
  - [4.1. docker ps](#41-docker-ps)
  - [4.2. 验证docker选项参数生效](#42-验证docker选项参数生效)

<!-- /TOC -->


# 1. 上传资源 

先将构建好的镜像文件导出
```
docker save -o /tmp/skynet-doc-demo.tar skynet/doc-demo:2.1.1009
```

然后将该文件上传到skynet-xmanager所在服务器的${SKYNET_HOME}/repo/skynet-doc-demo下。

我们这里提供了构建好的镜像文件 ： http://172.31.164.7/other/skynet-doc-demo/skynet-doc-demo.tar

# 2. 服务定义 

## 2.1. 方式一： 导入

在 [SpringBoot Demo服务托管](quickstart/springboot/springboot-integration.md) 章节其实已经导入过了`'Skynet文档示例'`的服务定义，此步骤可以跳过。

服务定义文件：http://172.31.164.7/other/skynet-doc-demo/skynet-doc-demo.zk.config


## 2.2. 方式二：填写表单

### 2.2.1. 基本信息

![alt](res/service-def-basic.png)

说明：
* 镜像名称就是构建时docker build的 --tag 参数

### 2.2.2. docker参数

![alt](res/service-def-args.png)

此处填的内容是docker start/create 命令的选项参数。

|参数|说明|
|-|-|
|-v ${SKYNET_HOME}/conf/:${SKYNET_HOME}/conf/:ro|将宿主机的skynet配置目录以只读方式映射到容器内部|
|-v ${SKYNET_HOME}/log/:${SKYNET_HOME}/log/|将宿主机的skynet日志目录映射到容器内部|
|-e xtemplate_config_url=${SKYNET_CONFIG_LOCATION_URI}|xtemplate_config_url环境变量传递到skynet-util程序，作为属性获取的来源|
|-e demo_prop_a=${aaa}|用于演示直接使用容器的环境变量|
|-p ${PORT}:8080 |将服务定义的'服务端口'映射到容器内部|


### 2.2.3. 健康检查

![alt](res/service-def-health.png)

### 2.2.4. 文件关联

![alt](res/service-def-file.png)

当服务类型为`'DockerBoot'`时，tar文件会被当做镜像文件导入docker。（所以此场景服务只能关联一个tar文件）

### 2.2.5. 属性配置

![alt](res/service-def-prop.png)

# 3. 启动服务

分配服务 `'skynet文档-dockerboot demo'`

第一次启动服务后，查看 /dev/shm/.skynet/action/demo-dockerboot@skynet-doc-demo/target.sh 脚本，可以看到启动命令被组装成如下的样子
```
'docker' 'run' '--name' 'skynet-demo-dockerboot-skynet-doc-demo' '-l' 'skynet/doc-demo' '-v' '/etc/localtime:/etc/localtime' '-v' '/iflytek/server/skynet/conf/:/iflytek/server/skynet/conf/:ro' '-v' '/iflytek/server/skynet/log/:/iflytek/server/skynet/log/' '-e' 'xtemplate_config_url=http://172.31.97.149:6230/skynet/config/demo-dockerboot@skynet-doc-demo?actionId=demo-dockerboot@skynet-doc-demo' '-e' 'demo_prop_a=ThisIsAAA' '-p' '8601:8080' 'skynet/doc-demo:2.1.1009'
```

第二次启动服务，由于容器创建过一次，启动命令变成

```
'docker' 'start' 'skynet-demo-dockerboot-skynet-doc-demo' '-i'
```

# 4. 验证效果

## 4.1. docker ps

`docker ps |grep 'skynet-demo-dockerboot'` 结果如下

|CONTAINER ID|IMAGE|COMMAND|CREATED|STATUS|PORTS|NAMES|
|-|-|-|-|-|-|-|
|e4ada82595ec|skynet/doc-demo:2.1.1009|"/bin/bash /docker-e…"|About a minute ago|Up About a minute|0.0.0.0:8601->8080/tcp|skynet-demo-dockerboot-skynet-doc-demo

`'skynet-demo-dockerboot-skynet-doc-demo'` 是skynet生成的容器名，因为容器名不能出现@符号，所以该名称实际是将${SKYNET_ACTION_ID}的'@'符号替换成'-'得来的。

如果启动失败，可以通过 `'docker logs skynet-demo-dockerboot-skynet-doc-demo'`命令查看容器日志，或者通过xmanager的标准输出功能查看启动时的输出，如下图。

![alt](res/stdout.png)

## 4.2. 验证docker选项参数生效

进入容器
```
docker exec -it skynet-demo-dockerboot-skynet-doc-demo bash
cd /opt/skynet-doc-demo-docker/
```

ls可以看到生成了config.json文件。config.json.xtemplate中的demo.config.name变量被替换成了`'服务定义->属性配置'`中的值。

![alt](res/config-json-content.png)

从xmanager查看服务标准输出，会看到如下输出

![alt](res/stdout-2.png)


在宿主机${SKYNET_HOME}/log目录下，应该会看到 <EMAIL> 生成，说明数据卷映射生效。

访问http://${IP}:8601/api-v2/hello ，能正常返回结果，说明属性 'demo.api.v2.enable=true'生效。

