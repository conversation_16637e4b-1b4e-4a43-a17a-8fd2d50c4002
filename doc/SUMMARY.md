# Summary

* [说明](README.md)
* [文档版本](__version.md)
* [skynet平台介绍](intro/intro.md)
* [skynet平台部署](deploy/platform-deploy.md)
* 快速开始
    * 构建一个SpringBoot类型Demo   
        * [SpringBoot Demo服务开发](quickstart/springboot/springboot-dev.md)   
        * [SpringBoot Demo服务托管](quickstart/springboot/springboot-integration.md)   
        * [SpringBoot Demo服务监控](quickstart/springboot/springboot-mon.md)   
        * [SpringBoot Demo日志采集](quickstart/springboot/springboot-logging.md)   
    * 构建一个DockerBoot类型demo
        * [Docker Demo镜像构建](quickstart/dockerboot/dockerboot-dev.md)
        * [Docker Demo服务托管](quickstart/dockerboot/dockerboot-integration.md)
    * 构建一个BaseBoot类型demo
        * [BaseBoot Demo服务托管](quickstart/baseboot/baseboot-integration.md)
* 核心功能
    * [xAgent管理](core/server-management/server-management.md)
    * [服务定义](core/service-definition/overview.md)
        * [系统](core/service-definition/sys.md)
        * [资源](core/service-definition/resource.md)
        * [服务](core/service-definition/service.md)
            * [基本信息](core/service-definition/service-def-basic.md)
            * [运行参数](core/service-definition/service-def-args.md)
            * [环境变量](core/service-definition/service-def-env.md)
            * [健康检查](core/service-definition/service-def-health.md)
            * [关联文件](core/service-definition/service-def-file.md)
            * [属性配置](core/service-definition/service-def-prop.md)
            * [日志配置](core/service-definition/service-def-logging.md)
        * [导入导出](core/service-definition/def-import-export.md)
    * [服务管理](core/service-management/service-management.md)
    * [服务守护](core/service-daemon/service-daemon.md)
    * [服务状态查看](core/service-state/service-state.md)
    * [NUMA平台资源自动绑定](core/numa/numa.md)   
* 扩展功能
    * 监控与告警
        * [系统部署](extension/skynet-mon/mon-deploy.md)
        * [使用指南](extension/skynet-mon/mon-guide.md)
        * [FAQ](extension/skynet-mon/mon-faq.md)
    * 日志采集
        * [系统部署](extension/skynet-log/log-deploy.md)
        * [使用指南](extension/skynet-log/log-guide.md)
        * [FAQ](extension/skynet-log/log-faq.md)    
    * [Nginx网关](extension/nginx/nginx.md)
* [第三方集成(CI/CD) API](cicd/cicd-api.md)
* FAQ & HOW-TO
    * [定义BaseBoot类型服务需要注意的地方](faq-howto/baseboot-requirement.md)
    * [如何让某个物理节点同时属于不同skynet集群](faq-howto/member-of-multiple-cluster.md)
    * [如何修改skynet-xagent和xmanager端口](faq-howto/modify-port.md)
    * [如何删除应用系统及其下所有服务定义](faq-howto/delete-plugin.md)
    * [如何让'服务端口'配置生效](faq-howto/service-port-work.md)
    * [如何升级skynet版本](faq-howto/skynet-update.md)
    * [如何升级被托管服务](faq-howto/service-update.md)
    * [服务启动失败如何排查](faq-howto/startup-troubleshooting.md)
    * [如何修改Logback日志配置](faq-howto/logback-config.md)
    * [国产化平台部署注意事项](faq-howto/gch.md)
* [Release Notes](release-notes/release-notes.md)
* [术语表](glossary/glossary.md)
* 内部实现
    * [服务启停](implementation/service-startup/service-startup.md)
* 附录
    * [skynet-util使用指南](appendix/skynet-util/skynet-util-guide.md)
    * [第三方组件信息汇总](appendix/components/components.md)
    * [skynet日志与监控系统与集团平台对比](appendix/compare/compare.md)
