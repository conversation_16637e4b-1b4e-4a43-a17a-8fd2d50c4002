<h1>监控告警</h1>
<!-- TOC -->

- [1. 方案介绍](#1-方案介绍)
- [2. 组件部署](#2-组件部署)
- [3. 使用指南](#3-使用指南)
  - [3.1. 如何查看skynet提供的基础监控](#31-如何查看skynet提供的基础监控)
    - [3.1.1. 监控图表](#311-监控图表)
    - [3.1.2 告警规则](#312-告警规则)
  - [3.2 自己编写的被托管服务如何才能被prometheus采集指标？](#32-自己编写的被托管服务如何才能被prometheus采集指标)
    - [3.2.1 通过服务发现机制](#321-通过服务发现机制)
    - [3.2.2 手动配置](#322-手动配置)
  - [3.3 如何针对自定义指标进行告警](#33-如何针对自定义指标进行告警)
    - [3.3.1 全局配置](#331-全局配置)
    - [3.3.2 通道配置](#332-通道配置)
    - [3.3.3 创建模板](#333-创建模板)
    - [3.3.4 调整模板实例阈值](#334-调整模板实例阈值)
    - [3.3.5 查看告警](#335-查看告警)
- [4. 附录](#4-附录)
  - [4.1 如何使用微信企业号进行通道配置](#41-如何使用微信企业号进行通道配置)
    - [4.1.1 注册、登陆](#411-注册登陆)
    - [4.1.2 创建应用](#412-创建应用)
    - [4.1.3 添加通讯好友](#413-添加通讯好友)
    - [4.1.4 全局配置](#414-全局配置)
    - [4.1.5 通道配置](#415-通道配置)

<!-- /TOC -->

# 1. 方案介绍

skynet通过集成流行的第三方开源组件，对系统资源、服务可用性、以及业务指标进行监控、告警。其中，使用prometheus作为数据采集、存储的组件，使用AlertManager进行告警的处理与推送，使用grafana对采集到的指标数据进行可视化展示。

下图展示监控方案的工作流程。

![alt](res/guide/dataflow.png)

Exporter是prometheus生态的术语，代表指标数据的提供方，它通过提供一个满足prometheus数据规范的http接口向prometheus暴露指标数据，prometheus采用定时拉（pull）的方式采集数据并存储起来。prometheus官方提供了许多exporter用来对硬件、操作系统、开源组件等等进行监控，用户也可以集成promethues的sdk以编写业务相关的exporter。

Skynet会将被托管服务的状态汇报到zookeeper中，这个状态信息包含这样一个配置：它描述服务是否开启了prometheus的集成功能，如果开启，此服务必须提供prometheus规范的接口，以及对采集行为必要的描述。这个配置是在服务定义页面配置的，后续章节会详细介绍。skynet提供的管理中心服务会定时扫描zk中的服务状态，将开启prometheus采集功能的服务信息同步到prometheus的配置文件里，并让promethues重新加载配置文件，这样promehtues就能主动从被托管服务拉取监控数据了。

Skynet定时扫描仓库（repo)中符合命名规则的可视化模板定义文件(dashboard-*.json)，将这些模板文件导入到grafana中。因为grafana、prometheus都处于skynet的托管之下，所以无需配置这些地址，skynet会自动完成整个导入过程。skynet在仓库中预置了一些dashboard定义文件，用来对主机、集群、被托管服务进行监控。

监控管理中心是skynet专门为promtheus、AlertManager编写的配置管理服务，它提供了易用的UI让用户对采集规则、监控告警规则、告警推送规则进行定制，屏蔽了底层复杂的配置步骤。它还提供了告警记录持久化（依赖Elasticsearch)、告警记录查询的功能。

# 2. 组件部署

参考 ['基础平台-监控'系统部署](./mon-deploy.md) 

监控功能至少依赖以下组件：

* Skynet监控-管理中心
* Skynet监控-采集服务
* Skynet监控-仪表盘
* Skynet监控-主机监控

告警功能至少依赖以下组件（如果不需要告警功能可以不部署)：
* Skynet监控-预警服务
* Skynet日志-ES

# 3. 使用指南

## 3.1. 如何查看skynet提供的基础监控

### 3.1.1. 监控图表

日志监控方案部署完毕后，在仪表盘服务(grafana)中会看到skynet自动导入的几个图表，如下图

![alt](res/guide/grafana-1.png)

几点说明：

1. 主机监控用来对主机的系统资源进行监控的图表
2. 集群监控主要是对集群的节点信息进行汇总，提供全局概览的视图
3. 服务监控、服务监控历史是skynet对被托管服务的监控图表，前者只能查看当前在线服务，后者能查看所有服务的监控历史记录。
4. 通用图表直接使用传递的模板变量作为查询展现图表，主要是由管理中心用来对告警记录相关的指标历史进行展示。从grafana直接点进去，该图表无法成功渲染。
5. Skynet流式计算展示了流式计算相关的监控信息

### 3.1.2 告警规则

![alt](res/guide/alert-rules.png)

`"Skynet监控-管理中心"`启动后，会初始化自动导入预置的监控告警模板，这些模板没有使用扩展标签，意味着它对所有的服务节点都使用同样的告警阈值。这些模板也没有关联任何告警通道，意味着由它生成的告警只能在告警记录页查询到，不会推送给任何人。

由这些模板会生成监控实例。关于如何调整这些模板与实例的阈值，请参考后续章节。

## 3.2 自己编写的被托管服务如何才能被prometheus采集指标？

### 3.2.1 通过服务发现机制

![alt](res/guide/endpoint-service-discover-1.png)

如上图，需要在服务定义处开启监控采集功能（选“是”）,并在采集点配置处输入被采集的url路径以及采集周期，用冒号隔开。采集点配置须填写完整，否则无法生效。采集周期的单位支持s/m/h，分别代表秒、分钟、小时，一般推荐15秒作为采集周期。

配置表单提交一段时间后（一般在三十秒以内），即可在`"Skynet监控-管理中心"`的采集配置页看到相应记录，其`“是否自动创建”`列显示为“是”

![alt](res/guide/endpoint-service-discover-2.png)

### 3.2.2 手动配置

在`"Skynet监控-管理中心"`的采集配置页点击`"添加"`按钮，填写必要信息，保存，即可在`"采集任务"`页看到相关记录，其`"是否自动创建"`列显示为"否"。这种配置方式适合监控非skynet托管的服务，以及开发调试阶段的服务。skynet托管服务在生产环境推荐使用服务发现机制进行自动配置。

![alt](res/guide/endpoint-manual.png)

## 3.3 如何针对自定义指标进行告警

完整的告警配置流程包含以下步骤（"Skynet监控-管理中心"）：

### 3.3.1 全局配置

"Skynet监控-管理中心"支持三种告警推送方式：企业微信、邮件、webhook。其中微信与邮件方式都需要在全局配置页配置一些必要信息。此外，全局配置还包括对预警的处理相关配置。以下详细说明：

* 邮箱地址：用来发送告警的邮箱地址，必须满足电子邮件格式要求
* 邮箱密码：用来发送告警的邮箱的密码
* 发件服务器：用来发送邮件的服务器配置，包含服务器的主机和端口号，如* mail.iflytek.com:25
* 微信API 企业ID：企业微信的Corp_ID，参考企业微信注册章节
* 微信API Secret：企业微信用户登录用的secret，参考企业微信注册章节
* 微信API AgentID：
* 告警分组等待时间：目前"Skynet监控-管理中心"按照模板对告警进行分组，当一个告警被触发，* 它会等待一定时间看是否有同分组的告警被触发，如果有这些告警会被合并成一个告警消息。
* 告警分组发送间隔：同一分组有新告警时的发送间隔
* 告警重复发送间隔：重复告警的发送间隔

### 3.3.2 通道配置

新增需要通知的通道，如果不配置任何通道，告警只能在告警记录页查询到，不会推送给任何人。

### 3.3.3 创建模板
监控模板的作用是由其生成监控实例，一个模板可能生成多个实例，这些实例用扩展标签的值做区分，实例被创建时拥有和模板一样的告警阈值，后续用户可以调整实例的阈值。

比如，与磁盘相关的指标会有一个名为device的标签值，如下图（prometheus查询页面）。

![alt](res/guide/prometheus-1.png)

如果在模板配置页面配置扩展标签列表['device','instance']

![alt](res/guide/template-edit.png)

就会从instance、device两个维度生成不同的实例，我们可以为某节点上的某个磁盘调整相关告警阈值。

![alt](res/guide/template-instance-list.png)

`说明：所有的指标被prometheus采集后都默认会增加两个标签：job,instance。 job会被"Skynet监控-管理中心"设置为被托管服务的服务坐标，instance其值为被采集服务的IP:端口。`

### 3.3.4 调整模板实例阈值

skynet预置的模板扩展标签为空，其生成的实例默认对所有instance都生效。如果需要针对某个节点上的阈值做调整，比如调整某个节点的内存占用告警阈值，则需要在相应内存监控模板上增加扩展标签 instance，保存。然后调整相应节点模板实例里的阈值。

如果调整了模板的阈值，希望实例的阈值相应变化，只需要删除实例即可，删除后实例会被重新创建，其阈值被初始化为模板的阈值。

### 3.3.5 查看告警

所有的告警，无论是否配置了告警通道，都能在管理中心的告警记录页面查询到，点击表格行即可查看告警详情。下图红框内的按钮点击后会跳转到仪表盘的通用图表，展现告警指标在告警时间前后的变化趋势。

![alt](res/guide/alerts-list.png)

# 4. 附录

## 4.1 如何使用微信企业号进行通道配置

### 4.1.1 注册、登陆

访问https://work.weixin.qq.com/进入页面，进入微信企业号注册页面，按提示填好信息。登陆该账号。

### 4.1.2 创建应用

登陆后，点击应用与小程序，创建应用。该应用用于推送告警消息。

![alt](res/guide/wechat-create-app.png)

### 4.1.3 添加通讯好友

在通讯录页，添加成员，这些成员的账号信息可以在"Skynet监控-管理中心"的告警通道中使用。也可以通过企业微信手机APP进行好友邀请，避免直接手动输入账号信息。

![alt](res/guide/wechat-add-friends.png)

### 4.1.4 全局配置

在"Skynet监控-管理中心"全局配置页，输入以下信息

![alt](res/guide/wechat-enterprise-info.png)

![alt](res/guide/wechat-app-info.png)

### 4.1.5 通道配置

![alt](res/guide/channel-edit.png)

微信告警通道可以推送给个人，也可以推送给整个部门，可以通过以下方式查看部门和个人的账号ID

![alt](res/guide/wechat-depart.png)

![alt](res/guide/wechat-depart-member.png)