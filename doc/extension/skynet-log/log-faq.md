<h1>'基础平台-日志'系统 FAQ</h1>
<!-- TOC -->

- [1. 如何修改服务端口](#1-如何修改服务端口)
  - [1.1. Elasticsearch 端口修改](#11-elasticsearch-端口修改)
  - [1.2. Kaf<PERSON> 端口修改](#12-kafka-端口修改)
  - [1.3. Kibana 端口修改](#13-kibana-端口修改)
- [2. 如何查看服务的日志和数据](#2-如何查看服务的日志和数据)
- [3. 如何修改JVM堆内存大小](#3-如何修改jvm堆内存大小)
  - [3.1. elasticsearch](#31-elasticsearch)
  - [3.2. logstash](#32-logstash)
  - [3.3. kafka](#33-kafka)
- [4. 如何查看Kafka的状态](#4-如何查看kafka的状态)
- [5. 如何进入容器](#5-如何进入容器)
- [6. DockerBoot服务目录在容器内部的路径](#6-dockerboot服务目录在容器内部的路径)
  - [6.1. <PERSON><PERSON><PERSON>](#61-elk)
  - [6.2. kafka](#62-kafka)
- [7. 可以使用外部的ELK吗](#7-可以使用外部的elk吗)

<!-- /TOC -->


# 1. 如何修改服务端口
为了避免和业务线部署的组件端口产生冲突，以上基于docker部署的服务端口大部分都和官方默认端口不一样

elasticsearch ：  
* http端口：19200  
* tcp端口：19300  
* elasticsearch_head http端口：19100  

kibana端口 ：15601

kafka :  
* 服务端口：19092  
* kafka-manager 端口：19000  

修改端口时，如果该容器存在多个端口，不仅要修改“服务端口”，也要修改docker参数里的端口映射配置。

## 1.1. Elasticsearch 端口修改
Elasticsearch 容器包含了elasticsearch服务和elasticsearch_head服务 （elasticsearch_head默认连接本节点的elasticsearch）。

Elasticsearch 节点的http端口配置在服务定义的服务端口里

![alt](res/modify-port-es-1.png)

其他端口统一配置在`'基础平台-日志'`的系统级属性定义里，此处配置对所有es节点的容器都生效

![alt](res/modify-port-es-2.png)

说明: ${PORT}代表引用服务定义中的"服务端口" ,服务端口设为0代表随机端口


如果要针对某个节点修改相关端口，可在相关节点的服务定义( 如Ant监控-ES集群-node1 ）中增加服务级别的属性定义，覆盖系统系统级属性。

修改完服务级别和系统级别的端口配置后，还需要修改集群级配置

![alt](res/modify-port-es-3.png)

## 1.2. Kafka 端口修改

kafka服务端口：  
![alt](res/modify-port-kafka-1.png)

kafka容器内置的kafka_manager端口(http）  
![alt](res/modify-port-kafka-2.png)

修改完kafka服务端口后记得修改集群配置  
![alt](res/modify-port-kafka-3.png)

## 1.3. Kibana 端口修改

仅需修改服务定义的“服务端口”即可。

# 2. 如何查看服务的日志和数据

skynet通过docker数据卷映射的方式将所有DockerBoot类型服务的日志和数据目录映射到宿主机的 /iflytek/volume下：

* elasticsearch  
日志目录：/iflytek/volume/es/node-1/log  
数据目录：/iflytek/volume/es/node-1/data

* kibana  
日志目录：/iflytek/volume/kibana/log

* logstash  
日志目录：/iflytek/volume/logstash/log  
数据目录：/iflytek/volume/logstash/data

* kafka  
日志目录： /iflytek/volume/kakfa/log  
数据目录： /iflytek/volume/kakfa/data  

可以修改系统级别属性 skynet.volume，改变数据卷的目录。

```
skynet.volume=/iflytek/volume
```

# 3. 如何修改JVM堆内存大小

## 3.1. elasticsearch
```
#默认最小堆内存
skynet.logging.es.jvm_xms=4g
#默认最大堆内存
skynet.logging.es.jvm_xmx=4g
```
![alt](res/modify-heapsize-es.png)

`可单独针对某个节点配置服务级属性覆盖系统级属性配置`

## 3.2. logstash

![alt](res/modify-heapsize-logstash.png)

## 3.3. kafka

![alt](res/modify-heapsize-kafka.png)



# 4. 如何查看Kafka的状态
 
"Skynet日志-Kafka"服务的容器内部集成了kafka-manager作为kafka的管理客户端。

通过宿主机的19000端口访问 （http://{IP}:19000）。skynet提供了快捷访问kafka-manager的方式，如下图
![alt](res/access-kafka-manager.png)

第一次使用kafka-manger需要初始化配置。 选择 `"Cluster → Add Cluster"`，`"Cluster Name"`部分可以任意填写，`"Kafka Version"`选择********，`"Cluster Zookeeper Hosts"`必须填 "localhost:2181" 。保存。
![alt](res/kafka-manager-add-cluster.png)

kafka依赖zookeeper，为了和skynet的zookeeper隔离开，”Skynet日志-kafka"的容器内部封装了一个zookeeper及zkui，它们的端口没有暴露出来，如果某些情况下需要访问kafka依赖的zookeeper，可以在docker参数里加上 `"-p 2189:2181 -p 9097:9090"`进行端口映射 , 其中2189和9097是宿主机端口（为了避免端口冲突采用和默认端口不同的值）。

# 5. 如何进入容器

方法一：
```
docker exec -it skynet-${SKYNET_ACTION_CODE}-${SKYNET_ACTION_PLUGIN} bash
```
将${SKYNET_ACTION_CODE}和${SKYNET_ACTION_PLUGIN}替换成相关的变量。

举例：
```
docker exec -it skynet-log-es-node1-ant-mon bash
docker exec -it skynet-log-kafka-ant-mon bash
docker exec -it skynet-log-logstash-ant-mon bash
docker exec -it skynet-log-kibana-ant-mon bash
```

方法二：

以kafka服务为例

{% raw %}  
执行 `docker inspect -f '{{.State.Pid}}'  skynet-log-kafka-ant-mon` 得到进程pid (比如10090）  
{% endraw %}

执行 `nsenter --target 10090 --mount --uts --ipc --net --pid`

# 6. DockerBoot服务目录在容器内部的路径

## 6.1. ELK

* /opt/es
* /opt/kibana
* /opt/head
* /opt/logstash

![alt](res/container-path-elk.png)

## 6.2. kafka

* /opt/kafka
* /opt/kafka-manager-********
* /opt/zkui
* /opt/zookeeper-3.4.11

![alt](res/container-path-kafka.png)


# 7. 可以使用外部的ELK吗

可以，只需要修改集群级配置，不进行服务分配即可。配置参考'章节3.2'。