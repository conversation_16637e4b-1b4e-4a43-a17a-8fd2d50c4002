<h1>skynet平台部署</h1>

<!-- TOC -->

- [1. 部署包准备](#1-部署包准备)
- [2. 环境检查](#2-环境检查)
- [3. 基础软件（ZooKeeper，ZKUI）安装](#3-基础软件zookeeperzku<PERSON>安装)
  - [3.1. Java 环境安装配置](#31-java-环境安装配置)
  - [3.2. Zookeeper安装](#32-zookeeper安装)
  - [3.3. <PERSON><PERSON><PERSON>安装](#33-z<PERSON><PERSON>安装)
- [4. Skynet包部署](#4-skynet包部署)
- [5. xManager启动](#5-xmanager启动)
- [6. 注意事项](#6-注意事项)

<!-- /TOC -->

# 1. 部署包准备

依赖：

* http://************/component/jdk1.8.0_261.tar.gz
* http://************/component/zookeeper-3.5.5-bin.tar.gz
* http://************/component/zkui.zip

skynet开发版本获取地址：http://************/skynet

目前最新RELEASE版本为 3.0.0-SNAPSHOT，请到图聆产品货架申请，联系人为【葛志星】(<EMAIL>)。申请时请注意填写平台架构: x86_64 / ARM64v8(aarch64)。

请申请使用最新的skynet版本。[Release Notes](../../release-notes/release-notes.md)

# 2. 环境检查

`注意：此步骤是针对将来在skynet集群中的所有服务器节点`

[1]hosts 配置  

部署环境的/etc/hosts需要添加ip与主机名的设置，如下面所示  
```
127.0.0.1 localhost  
************** skynet203
```
[2]关闭防火墙  

Skynet环境需要关闭防火墙，不同系统，防火墙关闭与查看方式统统，下面列出几种主要系统的关闭防火墙命令

**centos6.x**  
```
#查看防火墙的状态：
service iptables status

#关闭防火墙：
service iptables stop
```
**centOS 7.x**  
```
#查看防火墙的状态:
systemctl status firewalld
#关闭防火墙：
service firewalld stop
```
**Ubuntu**
```
#查看防火墙的状态：
ufw status
#关闭防火墙：
ufw disable
```

[3]系统参数调整

解决linux端口TIME_WAIT以及close_wait过多和禁用 IPv6:

```
vim /etc/sysctl.conf
```

配置内容  
```
#linux端口TIME_WAIT以及close_wait过多
#如果存在，直接修改，不存在 在后面添加
net.ipv4.tcp_timestamps = 0
net.ipv4.tcp_tw_recycle = 1
net.ipv4.tcp_tw_reuse = 1
net.core.netdev_max_backlog = 262144
net.ipv4.tcp_max_syn_backlog = 262144
net.ipv4.tcp_max_tw_buckets = 3000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 60
 
#禁用IPv6
net.ipv6.conf.all.disable_ipv6 =1
net.ipv6.conf.default.disable_ipv6 =1
 
#如果你想要为特定的网卡禁止IPv6，比如，对于enp0s3
#net.ipv6.conf.enp0s3.disable_ipv6 =1
```

让配置生效 ```sysctl -p```

如果IPv6未禁用，需要重启系统
```
reboot
netstat -lnpt
```

# 3. 基础软件（ZooKeeper，ZKUI）安装

skynet部署前需要安装的软件有：zookeeper，zkui。zookeeper和zkui依赖jdk，所以需要在部署zookeeper的节点先安装jdk。

`说明：从2.1.0.1009版本开始，skynet安装包内置了jdk1.8，无需在skynet的服务托管节点单独安装jdk，本章节所述的jdk安装仅用于zookeeper(集群)节点。`

## 3.1. Java 环境安装配置  

Skynet运行需要jdk环境，jdk版本要求为1.8.0_212。jdk由于安装较为简单，请自行安装。安装完成后，请查看java是否正常。

centos默认自带openjdk，安装前请先执行rpm -qa |grep openjdk 查询已安装的jdk rpm，用 rpm -e --nodeps 包名来卸载openjdk。  
`这里一定要注意，需要安装的是完整的jdk环境，而不仅仅是jre环境，下面是java环境查看命令`
```
#查看jre版本
java -version
#查看jdk版本
javac -version
```

如果不是jdk1.8，重新下载安装jdk1.8：  
x86_64平台：http://************/component/jdk1.8.0_261.tar.gz  
ARM64平台：http://************/component/jdk-8u212-linux-arm64-vfp-hflt.tar.gz

```
root@u167:/usr# mkdir /usr/java
root@u167:/usr# cp /iflytek/software/jdk1.8.0_261.tag.gz /usr/java
root@u167:/usr# cd /usr/java
root@u167:/usr/java# tar -xzvf jdk1.8.0_261.tag.gz
```

配置JAVA_HOME环境变量 `vim /etc/profile`,在尾部添加  
```
export JAVA_HOME=/usr/java/jdk1.8.0_261
export JRE_HOME=$JAVA_HOME/jre
export CLASSPATH=.:$JAVA_HOME/lib:$JRE_HOME/lib:$CLASSPATH
export PATH=$JAVA_HOME/bin:$JRE_HOME/bin:$JAVA_HOME:$PATH
```
使修改生效
```
root@u167:~# source /etc/profile
```
再检查java_version
```
root@u167:~# java -version
java version "1.8.0_212"
Java(TM) SE Runtime Environment (build 1.8.0_212-b15)
Java HotSpot(TM) 64-Bit Server VM (build 25.71-b15, mixed mode)
```

## 3.2. Zookeeper安装
1. 修改zookeeper配置文件   

解压zookeeper.tar.gz，将zookeeper-3.4.5/conf目录下面的 zoo_sample.cfg修改为zoo.cfg。

修改 dataDir=/home/<USER>/tmp下的目录。

在zoo.cfg最后一行添加server

```
server.1=engtest01:2888:3888
```
`注意：这里的engtest01是主机名，也可以直接使用主机ip`

2. 在${dataDir}目录下新建文件myid（里面的数字与server.x 中的数字相同，例如1）

```
echo "1" > /home/<USER>/myid
```

3. 启动zk服务

```
bin/zkServer.sh start
```

启动完成之后可以通过`zkServer.sh status`可以查看状态。至此`Standalone`模式服务端安装完成。在skynet集群比较大的情况下（服务器节点大于10台），建议部署Zookeeper集群（3个节点）具体的部署步骤参见依赖组件-ZooKeeper 集群模式部署节。

## 3.3. Zkui安装
zkui安装只需要解压zkui2.0.tar.gz，然后进入其目录，启动即可

```
chmod -R 775 zkui2.0
./start.sh
```

启动后进入页面查看，界面地址为  http://{ip}:9090/home，ip为zkui部署地址
![alt zkui界面](res/zkui.png)

# 4. Skynet包部署

将skynet.zip拷贝到/iflytek/server/下，对其进行解压(根路径为/iflytek)
```
cd /iflytek/server
#zip包 或 tar包
unzip skynet.zip
#重命名文件夹
mv skynet-boot-build-2.1.10XX-SNAPSHOT skynet
```

解压后文件目录为：
```
[root@skynetant skynet]# ll
total 32
drwxr-xr-x  2 <USER> <GROUP>  4096 1月  17 21:05 bin
drwxr-xr-x  2 <USER> <GROUP>  4096 1月  11 13:47 conf
drwxr-xr-x  2 <USER> <GROUP>  4096 12月 29 12:02 doc
drwxr-xr-x  2 <USER> <GROUP>  4096 12月 29 12:02 docker
drwxr-xr-x  4 <USER> <GROUP> 12288 1月  17 21:02 lib
drwxr-xr-x 54 <USER> <GROUP>  4096 1月  18 09:57 plugin
drwxr-xr-x 18 <USER> <GROUP>  4096 1月  17 10:57 repo
```

指定Skynet集群的zookeeper地址，修改文件是skynet/conf/skynet.properties，修改zookeeper配置，修改skynet.zookeeper.server_list为外部IP地址，不要填127.0.0.1（cluster_name一般都是skynet，除非特殊情景，否则不用更改）

zk是集群时，用英文下的逗号隔开，如 skynet.zookeeper.server_list=**************:2181,**************:2181,**************:2181

```
skynet.zookeeper.cluster_name=skynet
skynet.zookeeper.server_list=**************:2181
skynet.zookeeper.session_timeout=20000
skynet.zookeeper.connection_timeout=30000
```

为可执行脚本赋予权限

```
cd skynet/bin;
chmod +x *.sh
```

# 5. xManager启动
进入到解压后的skynet目录，运行脚本，启动xmanager

```
[root@skynetant bin]# ./ant-xmanager.sh
--------------------------------------------------
Usage: ant-xmanager.sh  help
       ant-xmanager.sh (start|daemon|stop) [port]
       ant-xmanager.sh  help
 
help       - this screen
start      - Start the program in the foreground
daemon     - Start the program in the background
stop       - stop  the service

[root@skynetant bin]# ./ant-xmanager.sh daemon
Restarting……；Waiting for a while
Starting program……
=========================================================
arg: daemon
SKYNET_HOME:/iflytek/server/skynet
JAVA_HOME:/usr/java/jdk1.8.0_261
-Dskynet.zookeeper.cluster_name=skynet -Dskynet.zookeeper.server_list=**************:2181 -Dskynet.zookeeper.session_timeout=20000 -Dskynet.zookeeper.connection_timeout=30000
=========================================================
try starting skynet-platform-xmanager
----------------
skynet-platform-xmanager started.
```

稍等片刻，进入到xmanager管理界面，界面地址为{ip}:2230。下面是登陆界面显示，出现此界面，说明xmanager启动成功。如果无法出现预期画面，请用./ant-xmanager.sh start 前台启动，查看报错信息。

默认用户名和密码为admin/admin.
![alt zkui界面](res/xmanager-login.png)

进入管理界面后，界面如下图所示
![alt zkui界面](res/xmanager.png)

> `xmanager启动时会检查系统初始化数据是否导入（判断依据：Zookeeper上对应的/${skynet_cluster_name}节点是否存在），如果缺失会自动导入初始化数据（包括基础服务定义及其他配置）`

# 6. 注意事项

[国产化平台部署注意事项](../../faq-howto/gch.md)