import sys
import os
import traceback
import re


major_v = None

# find the version string like '2.7.5' in sys.version
idx = sys.version.find('(')
if idx > 0:
	ver = sys.version[:idx].strip()
	print('python version is ' + ver)
	major_v = ver[:ver.find('.')]

if not major_v:
    sys.stderr.write('fail to parse python version : ' + sys.version)
    sys.exit(1)

print('python major version is ' + major_v)

if major_v == '2':
    reload(sys)
    sys.setdefaultencoding('utf8')

if __name__ == '__main__':
    fd = os.open('./fifo', os.O_WRONLY)
    if fd < 1:
        sys.stderr.write('fail to open fifo')
        sys.exit(2)
    while True:
        try:
            line = sys.stdin.readline()
            if line == '':
                break
            os.write(fd, bytearray(line,'utf-8'))
        except Exception:
            #sys.stderr.write(traceback.format_exc())
            pass
