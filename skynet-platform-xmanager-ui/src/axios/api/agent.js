import locale from '@/i18n/index.js'
// 操作相关接口
import axios from '../request'
import { setIfAbsentOrNull } from '@/common/util'
const emptyVersionDto = {
  buildBranch: '',
  buildJdkSpec: '',
  buildNumber: '',
  buildTime: '',
  createdBy: '',
  implementationTitle: '',
  implementationVendor: '',
  implementationVersion: '',
  mainClass: '',
  manifestVersion: '',
  projectName: '',
  projectVersion: '',
  skynetBootVersion: ''
}
const getAgents = data => {
  return new Promise((resolv, reject) => {
    axios
      .get('skynet/api/v3/agents', {
        __view_pop_error: locale.t('0')
      })
      .then(data => {
        resolv(__agentsDto2Vo(data))
      })
      .catch(err => {
        reject(err)
      })
  })
}
const getAgentByIp = ip => {
  return new Promise((resolv, reject) => {
    axios
      .get(`skynet/api/v3/agents/${ip}`)
      .then(data => {
        resolv(__agentDto2Vo(data))
      })
      .catch(err => {
        reject(err)
      })
  })
}
const startAgent = ip => {
  return axios.put(`skynet/api/v3/agents/status/${ip}?operation=start`)
}
const stopAgent = (ip, stopActions) => {
  stopActions = !!stopActions
  return axios.put(`skynet/api/v3/agents/status/${ip}?operation=stop&&stopActions=${stopActions}`)
}
const deleteAgent = ip => {
  return axios.delete(`skynet/api/v3/agents/${ip}`)
}
const testConnect = data => {
  return axios.post(`skynet/api/v3/agents/connection-test`, data, {
    __view_pop_success: data.agentType === 'server' ? `${data.sshUser}@${data.ip}${locale.t('1')}` : locale.t('2'),
    __view_pop_error: data.agentType === 'server' ? `${data.sshUser}@${data.ip}${locale.t('3')}` : locale.t('4')
  })
}
function createAgent(agentCreateParam) {
  return axios.post('skynet/api/v3/agents', agentCreateParam, {
    __view_pop_success: locale.t('5'),
    __view_pop_error: locale.t('6')
  })
}
function updateAgent(agentParam) {
  return axios.put(`skynet/api/v3/agents/${agentParam.ip}`, agentParam, {
    __view_pop_success: locale.t('7'),
    __view_pop_error: locale.t('8')
  })
}
function install(ip, isForce, isInstallDocker) {
  return axios.post(`skynet/api/v3/agents/installation/${ip}?isForce=${isForce}&dockerEnabled=${isInstallDocker}`)
}
function __agentsDto2Vo(data) {
  let ret = []
  if (data) {
    for (let item of data) {
      item = __agentDto2Vo(item)
      ret.push(item)
    }
  }
  return ret
}
function __agentDto2Vo(item) {
  const statusChsMap = {
    ONLINE: locale.t('9'),
    OFFLINE: locale.t('10'),
    BOOTING: locale.t('11')
  }
  if (!item) {
    return null
  }
  let serverInfo = item['serverInfo']
  let cpuCores = 'cpu' in serverInfo ? serverInfo['cpu'] : 0
  let memGB = 'mem' in serverInfo ? serverInfo['mem'] : 0
  let gpu = 'gpu' in serverInfo && serverInfo['gpu'] ? serverInfo['gpu'] : ''
  let nodes = 'nodes' in serverInfo ? serverInfo['nodes'] : 0
  let hardware = `${cpuCores}${locale.t('12')}${memGB}${locale.t('13')}`
  if (gpu) {
    hardware = `${hardware} | ${gpu}`
  }
  if (nodes) {
    hardware = `${hardware} | ${nodes}${locale.t('14')}`
  }
  let hostname = 'host' in serverInfo ? serverInfo['host'] : 'UNKNOWN'
  let os = 'os' in serverInfo ? serverInfo['os'] : 'UNKNOWN'
  item['__view_os'] = os
  item['__view_hardware'] = hardware
  item['__view_hostname'] = hostname
  item['__view_status_chs'] = item.status && item.status in statusChsMap ? statusChsMap[item.status] : locale.t('15')
  if (!item.version) {
    item.version = emptyVersionDto
  }
  let versionInfo = item.version
  Object.keys(emptyVersionDto).forEach(k => setIfAbsentOrNull(versionInfo, k, '')) // 避免null或者undefined产生的错误
  let projectVersion = versionInfo['projectVersion'] || locale.t('15')
  let buildSid = versionInfo['buildSid']
  item['__view_version'] = buildSid ? `${projectVersion}_Build${buildSid}` : `${projectVersion}`
  return item
}
function getGrafanaAddress() {
  return axios.get('/grafana/address')
}
const updateAgentConfigBlockCodes = data => {
  return axios.put(`skynet/api/v3/agents/configBlocks/${data.ip}`, data, {
    __view_pop_success: locale.t('7'),
    __view_pop_error: locale.t('8')
  })
}
export default {
  getAgentByIp,
  getAgents,
  startAgent,
  stopAgent,
  testConnect,
  deleteAgent,
  createAgent,
  updateAgent,
  getGrafanaAddress,
  install,
  updateAgentConfigBlockCodes
}
