import locale from '@/i18n/index.js'
import request from '../request'
import store from '@/store'
function getPlugins() {
  return new Promise((resolv, reject) => {
    request
      .get('skynet/api/v3/plugins')
      .then(d => {
        store.dispatch('skynet/changePlugins', d)
        resolv(d)
      })
      .catch(err => {
        reject(err)
      })
  })
}
function getPlugin(pluginCode) {
  return request.get(`skynet/api/v3/plugins/${pluginCode}`)
}
function createPlugin(vo) {
  let dto = {
    ...vo,
    ...{
      loggingLevels: '',
      properties: ''
    }
  }
  let promise = request.post('skynet/api/v3/plugins', dto, {
    __view_pop_success: locale.t('37'),
    __view_pop_error: locale.t('38')
  })
  return __new_promise_clear_store(promise)
}
function updatePlugin(dto) {
  let promise = request.put(`skynet/api/v3/plugins/${dto.code}`, dto, {
    __view_pop_success: locale.t('39'),
    __view_pop_error: locale.t('40')
  })
  return __new_promise_clear_store(promise)
}
function getPluginProps(code) {
  return request.get(`skynet/api/v3/plugins/${code}/properties`)
}
function updatePluginProps(code, data) {
  return request.put(`skynet/api/v3/plugins/${code}/properties`, data, {
    __view_pop_success: locale.t('41'),
    __view_pop_error: locale.t('42')
  })
}
function getPluginLogging(code) {
  return request.get(`skynet/api/v3/plugins/${code}/logging`)
}
function updatePluginLogging(code, data) {
  return request.put(`skynet/api/v3/plugins/${code}/logging`, data, {
    __view_pop_success: locale.t('43'),
    __view_pop_error: locale.t('44')
  })
}
function deletePlugin(code) {
  let promise = request.delete(`skynet/api/v3/plugins/${code}`)
  return __new_promise_clear_store(promise)
}
function __new_promise_clear_store(promise) {
  return new Promise((resolv, reject) => {
    promise
      .then(d => {
        store.dispatch('skynet/clearActionDefs')
        store.dispatch('skynet/clearPlugins')
        resolv(d)
      })
      .catch(err => {
        reject(err)
      })
  })
}
export default {
  getPlugins,
  getPlugin,
  createPlugin,
  updatePlugin,
  deletePlugin,
  getPluginProps,
  updatePluginProps,
  getPluginLogging,
  updatePluginLogging
}
