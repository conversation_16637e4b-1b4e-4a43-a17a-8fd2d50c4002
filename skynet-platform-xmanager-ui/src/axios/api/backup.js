import locale from '@/i18n/index.js'
import request from '../request'
import store from '@/store'
function getBackups() {
  return request.get('skynet/api/v3/backups')
}
function getBackup(backupName) {
  return request.get(`skynet/api/v3/backups/${backupName}`)
}
function createBackup() {
  return request.post(`skynet/api/v3/backups`)
}
function deleteBackup(backupName) {
  return request.delete(`skynet/api/v3/backups/${backupName}`)
}
function restoreBackup(data) {
  return request.post(`skynet/api/v3/backups/restore`, data)
}
export default {
  getBackups,
  getBackup,
  createBackup,
  deleteBackup,
  restoreBackup
}
