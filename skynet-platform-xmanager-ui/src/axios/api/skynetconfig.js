import locale from '@/i18n/index.js'
import request from '../request'
let encrypt = function(text) {
  return new Promise((resolv, reject) => {
    request
      .post(`skynet/security/encrypt`, text, {
        headers: {
          'Content-Type': 'text/plain'
        },
        __use_raw_response_data: true
      })
      .then(resp => {
        if (!resp.startsWith('{cipher}')) {
          resolv(`{cipher}${resp}`)
        } else {
          resolv(resp)
        }
      })
      .catch(err => {
        reject(err)
      })
  })
}
let decrypt = function(text) {
  text = text.replace('{cipher}', '')
  return request.post(`skynet/security/decrypt`, text, {
    headers: {
      'Content-Type': 'text/plain'
    },
    __use_raw_response_data: true
  })
}
export default {
  encrypt,
  decrypt
}
