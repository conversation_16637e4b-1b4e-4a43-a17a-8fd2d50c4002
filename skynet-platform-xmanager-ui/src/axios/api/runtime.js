import locale from '@/i18n/index.js'
import request from '../request'
function getNodeStatus(ip, port, actionID) {
  return request.get(`skynet/api/v3/actions/runtime/_nodestatus?ip=${ip}&port=${port}&aid=${actionID}`)
}
function getBootStatus(ip, port, actionID) {
  return request.get(getBootStatusUrl(ip, port, actionID))
}
function getBootStatusUrl(ip, port, actionID) {
  return `skynet/api/v3/actions/runtime/_bootstatus?ip=${ip}&port=${port}&aid=${actionID}`
}
function getEndpoints(ip, port, actionPoint) {
  return request.get(`skynet/api/v3/actions/runtime/springboot/actuator?ip=${ip}&port=${port}&actionPoint=${actionPoint}`)
}
export default {
  getBootStatusUrl,
  getBootStatus,
  getNodeStatus,
  getEndpoints
}
