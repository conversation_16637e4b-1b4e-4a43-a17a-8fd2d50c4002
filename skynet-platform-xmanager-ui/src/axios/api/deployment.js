import locale from '@/i18n/index.js'
import request from '../request'
import { setIfAbsentOrNull } from '@/common/util'
function getClusterDeployment() {
  return new Promise((resolv, reject) => {
    request.get('skynet/api/v3/deployment').then(data => {
      resolv(__dto2vo(data))
    })
  })
}
function updateDeploymentByServer(ip, data) {
  return request.put(`skynet/api/v3/deployment/${ip}`, data)
}
function getDeploymentByServer(ip) {
  return new Promise((resolv, reject) => {
    request.get(`skynet/api/v3/deployment/${ip}`).then(data => {
      if (data) {
        let _data = __dto2vo([data])
        resolv(_data[0])
      } else {
        reject()
      }
    })
  })
}
function __dto2vo(data) {
  /**
   * TODO: 开发初期前端定义的状态字符串和后端传的值不一致，暂时进行如下转换，后续需要统一
   */
  const statusMapping = {
    UP: 'running',
    DOWN: 'failed',
    OFFLINE: 'disabled',
    LOADING: 'starting'
  }
  if (data && data instanceof Array && data.length > 0) {
    data.map(v => {
      setIfAbsentOrNull(v, 'actions', [])
      setIfAbsentOrNull(v, 'serverTags', [])
      setIfAbsentOrNull(v, 'serverInfo', {})
      setIfAbsentOrNull(v.serverInfo, 'cpu', '0')
      setIfAbsentOrNull(v.serverInfo, 'mem', '0')
      setIfAbsentOrNull(v.serverInfo, 'gpu', '0')
      if ('actions' in v) {
        for (let action of v.actions) {
          // TODO : 后端数据错误，这里前端加判断绕过
          let __view_status
          if (action.status) {
            __view_status = statusMapping[action.status]
          } else {
            __view_status = 'disabled'
          }
          action['__view_status'] = __view_status
        }
      }
    })
    return data
  }
  return []
}
export default {
  getClusterDeployment,
  updateDeploymentByServer,
  getDeploymentByServer
}
