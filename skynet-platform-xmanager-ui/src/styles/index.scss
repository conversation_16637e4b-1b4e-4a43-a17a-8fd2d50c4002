@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  // height: calc(100vh - 50px);
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: sans-serif;
}

label {
  font-weight: bold;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: rgb(59, 116, 240);
  cursor: pointer;

  &:hover {
    color: rgba(59, 116, 240, 0.7);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.pagination {
  overflow: hidden;

  .left {
    float: left;
    line-height: 32px;
    color: #8b8f94;

    i {
      color: #293342;
    }
  }

  .el-pagination.is-background {
    padding-right: 0;
    float: right;

    .el-pager {
      .number {
        background: none;
        border: 1px solid #e0e4eb;
        border-radius: 3px;
      }

      li:not(.disabled).active {
        border-color: #367AE0;
        background: none;
        color: #367AE0;
      }
    }

    .btn-next,
    .btn-prev {
      background: none;
      border: 1px solid #e0e4eb;
      border-radius: 3px;
    }

    .el-pagination__jump {
      margin-left: 0;
    }

    .el-pagination__sizes {
      .el-input {}
    }
  }
}

.el-message {
  min-width: 360px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 0;
  border-left: 4px solid #fff;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -ms-border-radius: 8px;
  -o-border-radius: 8px;
  padding: 0;
  -webkit-box-align: unset;
  -ms-flex-align: unset;
  align-items: unset;

  i {
    position: absolute;
    top: 20px;

    &.el-message__icon {
      left: 20px;

      &::after {
        content: '提示';
        font-weight: bold;
        color: rgba(36, 47, 62, 1);
        font-size: 16px;
        margin-left: 10px;
      }
    }

    &.el-icon-close {
      right: 30px;
      transform: translateY(0);
      -webkit-transform: translateY(0);
      -moz-transform: translateY(0);
      -ms-transform: translateY(0);
      -o-transform: translateY(0);
    }
  }

  p.el-message__content {
    color: #888C91;
    line-height: 22px;
    padding: 50px 0 10px 50px;
    padding-right: 50px !important;
  }

  &.el-message--error {
    border-color: #F63A44;

    .el-icon-error {

      // box-shadow:0px 2px 8px 0px rgba(246,58,68,0.5);
      &:before {
        content: "\e78d";
        font-size: 20px;
      }

      &::after {
        content: '失败'
      }
    }
  }

  &.el-message--success {
    border-color: #56CD75;

    .el-icon-success {

      // box-shadow:0px 2px 8px 0px rgba(246,58,68,0.5);
      &:before {
        content: "\e720";
        font-size: 20px;
      }

      &::after {
        content: '成功'
      }
    }

  }
}

.el-message-box {
  position: relative;
  padding-bottom: 30px;

  .el-message-box__header {
    position: unset;
  }

  .el-message-box__headerbtn {
    right: 30px;
  }

  .el-message-box__title {
    top: 32px;
    left: 62px;
    position: absolute;
    font-size: 16px;
    font-weight: bold;
    color: rgba(36, 47, 62, 1);
  }

  .el-message-box__container {
    position: unset;
  }

  .el-message-box__status {
    top: 32px;
    left: 30px;
    font-size: 20px !important;
    transform: translateY(0);
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    line-height: 15px;

    &::before {
      content: "\e6c9";
    }
  }

  .el-message-box__message {
    font-size: 14px;
    font-weight: bold;
    color: rgba(136, 140, 145, 1);
    line-height: 22px;
    padding-top: 32px;
    padding-left: 48px;

  }

  .el-message-box__btns {
    padding: 25px 30px 0;
  }

}

.el-dialog {

  // border-radius: 2px;
  // -webkit-border-radius: 8px;
  // -moz-border-radius: 8px;
  // -ms-border-radius: 8px;
  // -o-border-radius: 8px;
  .el-dialog__header {
    border-bottom: 1px solid #E5E9F2;
    padding: 0 20px;

    .el-dialog__title {
      font-size: 14px;
      color: #242F3E;
      line-height: 50px;
    }
  }

  .el-dialog__body {
    padding: 18px 20px;
    border-bottom: 1px solid #E5E9F2;
    // margin-bottom: 5px;
  }
}

.el-table {
  .headbg {
    th {
      background: #e5e9f2;
      color: #3c4654;

      >div {
        font-weight: bold;
      }
    }
  }

  .cell {
    padding: 7.5px 0
  }



  &.el-table--mini th,
  &.el-table--mini td {
    //  padding: 0px 0 !important;
    font-size: 14px;
  }
}

.el-tooltip__popper.is-light {
  box-shadow: 0px 4px 30px 0px rgba(174, 174, 194, 0.6);
  border-color: #fff;
  padding: 5px 18px;
  min-height: 15px;

  .popper__arrow {
    border-color: #fff transparent transparent transparent !important;
    border: 20px;

    &::after {}
  }
}

.el-radio-group {
  >label.el-radio-button {
    span {
      border-color: #DFE1E6 !important;
      background-color: #EBECF0 !important;
      color: #42526E !important
    }

    &.is-active {
      span {
        background-color: #fff !important;
        color: #367ae0 !important;
        box-shadow: none !important
      }
    }
  }
}

::-webkit-scrollbar {
  height: 4px;
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: #ababab;
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 8px;
}

::-webkit-scrollbar-track-piece {
  background-color: #e6ebf5;
  border: 0;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(59, 116, 240);
}

.el-form {
  &>.el-form-item:last-child {
    margin-bottom: 0;
  }
}

.el-tag {
  font-weight: bold;
  line-height: 20px;
  /*color:rgba(62,201,235,1);*/
  color: #8b8b8b;
  /*font-size: 14px;*/
  /*background:rgba(235,249,252,0.7);*/
  background: #ebf2fc;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.el-table__empty-text {
  background: url('../assets/img/nodata.png') no-repeat center;
  height: 200px;
  color: #FFF;
}



.status-point {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;

  &::before {
    content: "";
    margin: 3px;
    display: table;
    width: 6px;
    height: 6px;
    background: #2090ff;
    border-radius: 50%;
  }

  &[status="enable"]::before {
    background: #13ce66;
  }

  &[status="disable"]::before {
    background: #c0c4cc;
  }

  &[status="failed"]::before {
    background: #ff4949;
  }
}

.el-button--mini {
  padding: 7px 10px;
}

.el-cascader-menu__wrap {
  height: 350px; // 影响级联菜单的高度
}

.el-dropdown-menu--mini .el-dropdown-menu__item {
  padding: 5px 15px; //影响DropDown菜单项的大小
}

.el-button--mini.el-dropdown__caret-button {
  height: 28px !important; //影响DropDown按钮的高度，解决1px对齐误差
}

/*
 * 表格项拖拽样式，勿删
 * See common/util.js
 */
.sortable-ghost {
  opacity: 0.4;
  background-color: #F4E2C9;
}

// .prism-editor-wrapper .prism-editor__editor, .prism-editor-wrapper .prism-editor__textarea{
//   line-height: 20px!important;
// }
.el-textarea__inner {
  font-size: 14px;
}

.my-prism-editor {
  /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
  /* you must provide font-family font-size line-height. Example: */
  font-size: 14px;
  line-height: 1.6;
  padding: 5px;
  .token.keyword, .token.property, .token.selector, .token.constant, .token.symbol, .token.builtin {
    color: hsl(53deg 25.84% 18.26%);
  }
    background: #1e1e1e;
    color: #ce9178;
    &.edit{
      background: #1e1e1e;
    }
    pre[class*="language-"],
    code[class*="language-"] {
      color: #d4d4d4;
      font-size: 13px;
      text-shadow: none;
      font-family: Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace;
      direction: ltr;
      text-align: left;
      white-space: pre;
      word-spacing: normal;
      word-break: normal;
      line-height: 1.5;
      -moz-tab-size: 4;
      -o-tab-size: 4;
      tab-size: 4;
      -webkit-hyphens: none;
      -moz-hyphens: none;
      -ms-hyphens: none;
      hyphens: none;
    }
  
    pre[class*="language-"]::selection,
    code[class*="language-"]::selection,
    pre[class*="language-"] *::selection,
    code[class*="language-"] *::selection {
      text-shadow: none;
      background: #264F78;
    }
  
    @media print {
  
      pre[class*="language-"],
      code[class*="language-"] {
        text-shadow: none;
      }
    }
  
    pre[class*="language-"] {
      padding: 1em;
      margin: .5em 0;
      overflow: auto;
      background: #1e1e1e;
    }
  
    :not(pre)>code[class*="language-"] {
      padding: .1em .3em;
      border-radius: .3em;
      color: #db4c69;
      background: #1e1e1e;
    }
  
    /*********************************************************
  * Tokens
  */
    .namespace {
      opacity: .7;
    }
  
    .token.doctype .token.doctype-tag {
      color: #569CD6;
    }
  
    .token.doctype .token.name {
      color: #9cdcfe;
    }
  
    .token.comment,
    .token.prolog {
      color: #6a9955;
    }
  
    .token.punctuation,
    .language-html .language-css .token.punctuation,
    .language-html .language-javascript .token.punctuation {
      color: #d4d4d4;
    }
  
    .token.property,
    .token.tag,
    .token.boolean,
    .token.number,
    .token.constant,
    .token.symbol,
    .token.inserted,
    .token.unit {
      color: #b5cea8;
    }
  
    .token.selector,
    .token.attr-name,
    .token.string,
    .token.char,
    .token.builtin,
    .token.deleted {
      color: #ce9178;
    }
  
    .language-css .token.string.url {
      text-decoration: underline;
    }
  
    .token.operator,
    .token.entity {
      color: #d4d4d4;
      background: inherit;
    }
  
    .token.operator.arrow {
      color: #569CD6;
    }
  
    .token.atrule {
      color: #ce9178;
    }
  
    .token.atrule .token.rule {
      color: #c586c0;
    }
  
    .token.atrule .token.url {
      color: #9cdcfe;
    }
  
    .token.atrule .token.url .token.function {
      color: #dcdcaa;
    }
  
    .token.atrule .token.url .token.punctuation {
      color: #d4d4d4;
    }
  
    .token.keyword {
      color: #569CD6;
    }
  
    .token.keyword.module,
    .token.keyword.control-flow {
      color: #c586c0;
    }
  
    .token.function,
    .token.function .token.maybe-class-name {
      color: #9cdcfe;
    }
  
    .token.regex {
      color: #d16969;
    }
  
    .token.important {
      color: #569cd6;
    }
  
    .token.italic {
      font-style: italic;
    }
  
    .token.constant {
      color: #9cdcfe;
    }
  
    .token.class-name,
    .token.maybe-class-name {
      color: #4ec9b0;
    }
  
    .token.console {
      color: #9cdcfe;
    }
  
    .token.parameter {
      color: #9cdcfe;
    }
  
    .token.interpolation {
      color: #9cdcfe;
    }
  
    .token.punctuation.interpolation-punctuation {
      color: #569cd6;
    }
  
    .token.boolean {
      color: #569cd6;
    }
  
    .token.property,
    .token.variable,
    .token.imports .token.maybe-class-name,
    .token.exports .token.maybe-class-name {
      color: #9cdcfe;
    }
  
    .token.selector {
      color: #d7ba7d;
    }
  
    .token.escape {
      color: #d7ba7d;
    }
  
    .token.tag {
      color: #569cd6;
    }
  
    .token.tag .token.punctuation {
      color: #808080;
    }
  
    .token.cdata {
      color: #808080;
    }
  
    .token.attr-name {
      color: #3dc9b0;
    }
  
    .token.attr-value,
    .token.attr-value .token.punctuation {
      color: #ce9178;
    }
  
    .token.attr-value .token.punctuation.attr-equals {
      color: #d4d4d4;
    }
  
    .token.entity {
      color: #569cd6;
    }
  
    .token.namespace {
      color: #4ec9b0;
    }
  
    /*********************************************************
  * Language Specific
  */
  
    pre[class*="language-javascript"],
    code[class*="language-javascript"],
    pre[class*="language-jsx"],
    code[class*="language-jsx"],
    pre[class*="language-typescript"],
    code[class*="language-typescript"],
    pre[class*="language-tsx"],
    code[class*="language-tsx"] {
      color: #9cdcfe;
    }
  
    pre[class*="language-css"],
    code[class*="language-css"] {
      color: #ce9178;
    }
  
    pre[class*="language-html"],
    code[class*="language-html"] {
      color: #d4d4d4;
    }
  
    .language-regex .token.anchor {
      color: #dcdcaa;
    }
  
    .language-html .token.punctuation {
      color: #808080;
    }
  
    /*********************************************************
  * Line highlighting
  */
    pre[class*="language-"]>code[class*="language-"] {
      position: relative;
      z-index: 1;
    }
  
    .line-highlight.line-highlight {
      background: #f7ebc6;
      box-shadow: inset 5px 0 0 #f7d87c;
      z-index: 0;
    }
}


.prism-editor__textarea:focus {
  outline: none;
}

/*
 * 日志组件，日志级别颜色定义
 */
debug {
  background-color: #0070bb;
  color: #e7e7e7;
}

info {
  background-color: #256d16;
  color: #e7e7e7;
}

warn {
  background-color: #bbbb23;
  color: #e7e7e7;
}

error {
  background-color: #940205;
  color: #e7e7e7;
}

// 去除表格多余线条
.el-table__fixed::before,
.el-table__fixed-right::before,
.el-table--group::after,
.el-table--border::after,
.el-table::before {
  background-color: transparent;
}

// sky列表页面样式（toolbar+body）
.sky-table-contanier {
  .toolbar {
    height: 28px;
    line-height: 28px;
  }

  .body {
    padding-top: 8px;
  }
}

// sky详细页面样式（toolbar+body）
.sky-detail-contanier {
  background: #fff;
  padding: 0;
  margin: 0;
  height: 100%;
  box-shadow: 0 1px 4px 0 rgba(96, 94, 94, 0.4);

  .toolbar {
    margin: 0;
    padding: 5px 15px;
    border-bottom: 1px #ccc dashed;

    .left,
    .right {
      height: 40px;
      line-height: 40px;
      display: flex;
      align-items: center;
    }

    .left {
      float: left;

      >span {
        margin-right: 8px;
      }
    }
  }

  .body {
    margin: 0;
    padding: 0px 15px;
  }
}

//对象详细  region 样式
.region {
  .title {
    font-weight: bold;
    font-size: 16px;
    height: 18px;
    line-height: 18px;
    border-left: 3px solid #367ae0;
    margin: 10px 0;
    padding-left: 10px;
  }

  .content {
    font-size: 14px;

    .detail-col {
      display: flex;
      line-height: 36px;

      .name {
        display: inline-block;
        width: 105px;
        text-align: right;
        margin-right: 8px;
        font-weight: bold;
        flex-shrink: 0;
      }

      .value {
        display: inline-block;
      }
    }
  }
}


.right {
  float: right;
}

.icon-success-fill {
  color: #367ae0;
}

.icon-reeor-fill {
  color: #d81e06;
}

.pod-terminated {
  color: #CCC;
}

.pod-running {
  color: #367ae0;
}

.pod-waiting {
  color: #ffba00;
}

.icon-server {
  color: #367AE0
}

.online {
  color: #367ae0;

}

.offline {
  color: gray;
}

.icon16 {
  width: 16px;
  height: 16px;
  vertical-align: -10px;
  fill: currentColor;
  overflow: hidden;
}

.icon24 {
  width: 24px;
  height: 24px;
  vertical-align: -10px;
  fill: currentColor;
  overflow: hidden;
}

// 文本超长省略显示
.overflow-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
}

.meta-label {
  float: left;
  padding: 0 5px;

  .key,
  .val {
    padding: 2px 5px;
    border: 1px solid #ccc;
  }

  .key {
    background-color: #e5e9f2;
  }

  .val {
    background-color: #f9f9f9;
    border-left: 0px;
  }
}

.red {
  color: red;
}

.yellow {
  color: #fdbd00;
}

.deletion-row {
  text-decoration: line-through;
  text-decoration-style: solid;
  color: #ccc;
}