import locale from '@/i18n/index.js'
import { formatByte } from './index'

/**
 * CPU 计量单位，以 10 为底数。
 * 比如 1234m 表示 1234 * 10^-3 = 1.234 核，也就是 CPU 占用率 123.4%
 */
const cpuUnitMap = {
  n: -9,
  u: -6,
  m: -3,
  '': 0,
  k: 3,
  M: 6,
  G: 9,
  T: 12,
  P: 15,
  E: 18
}

/**
 * 内存计量单位，以 2 为底数。
 * 比如 4Ki 表示 4 * 2^10 = 4096 字节，也写作 4KB
 */
const memoryUnitMap = {
  // 2-based
  '': 0,
  Ki: 10,
  Mi: 20,
  Gi: 30,
  Ti: 40,
  Pi: 50,
  Ei: 60
}

/**
 * 转换为 CPU 核数
 */
export function convertCpuCores(cpu) {
  if (!cpu) return 0
  // cpu = "91066667n"
  let parts = (cpu + '').split(/(\d+)/)
  let value = parseFloat(parts[1])
  let unit = parts[2]
  let exponentiation = cpuUnitMap[unit] || 0
  return value * Math.pow(10, exponentiation)
}

/**
 * 转换为人类可读的形式，如：23.4%
 */
export function convertCpuHumanReadable(cpu) {
  let cores = convertCpuCores(cpu)
  return (cores * 100).toFixed(2) + '%'
}

/**
 * 转换为内存字节数
 */
export function convertMemoryBytes(memory) {
  if (!memory) return 0
  // memory = "3068580Ki"
  let parts = (memory + '').split(/(\d+)/)
  let value = parseFloat(parts[1])
  let unit = parts[2]
  let exponentiation = memoryUnitMap[unit] || 0
  return value * Math.pow(2, exponentiation)
}

/**
 * 转换为人类可读的形式，如：23.4 KB
 */
export function convertMemoryHumanReadable(memory) {
  let bytes = convertMemoryBytes(memory)
  return formatByte(bytes)
}
