<template>
  <div>
    <el-pagination
      @size-change="paginationHandler('size_change', $event)"
      @current-change="paginationHandler('current_change', $event)"
      :current-page.sync="paginationParam.currentPage"
      :page-size="paginationParam.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paginationParam.totalSum"
      :background="true"
    >
      <!-- :page-count="Number(paginationParam.totalSum /10)" -->
    </el-pagination>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  props: {
    paginationParam: {
      type: Object,
      default() {}
    }
  },
  data() {
    return {}
  },
  methods: {
    paginationHandler(key, data) {
      this.$emit('paginationHandler', key, data)
      //console.log(data)
    }
  }
}
</script>
