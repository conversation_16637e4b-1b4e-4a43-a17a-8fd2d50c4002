<template>
  <el-dropdown :trigger="trigger" class="custom-tree-menu" size="small">
    <i class="el-icon-more rotate " />
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(item, index) in events" :key="index" :divided="index > 0" @click.native="clickMenu(item)">
        {{ item.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  props: {
    events: {
      type: Array,
      default: function() {
        return [
          {
            label: locale.t('63'),
            funcName: 'addNode'
          },
          {
            label: locale.t('64'),
            funcName: 'addNode'
          },
          {
            label: locale.t('65'),
            funcName: 'addNode'
          }
        ]
      }
    },
    // 注入数据
    data: {
      type: Object
    },
    trigger: {
      type: String,
      default: 'click'
    }
  },
  methods: {
    clickMenu(item) {
      this.$emit(item.funcName, this.data)
    }
  }
}
</script>
<style lang="scss">
.custom-tree-menu {
  .el-icon-more:before {
    content: '\E794';
    color: #c0c4cc;
    font-size: 20px;
  }
  .rotate {
    cursor: pointer;
    margin-left: 5px;
    /* transform: rotate(90deg); */
  }
  .rotate:focus {
    width: 20px;
    height: 20px;
    border-radius: 4em;
    background-color: rgba(130, 132, 138, 0.2);
  }
}
</style>
