<template>
  <div class="plugin-props">
    <prompt class="prompt-wrap">{{ $t('254') }}</prompt>
    <plugin-props-config :pluginCode="pluginCode"></plugin-props-config>
    <cluster-props-config :initialCollapsed="false"></cluster-props-config>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ClusterPropConfig from './components/ClusterPropConfig'
import PluginPropConfig from './components/PluginPropConfig'
import Prompt from '@/components/common/Prompt'
export default {
  name: 'pluginProps',
  components: {
    'cluster-props-config': ClusterPropConfig,
    'plugin-props-config': PluginPropConfig,
    prompt: Prompt
  },
  props: {
    pluginCode: {
      type: String
    }
  }
}
</script>
<style scoped lang="scss">
.plugin-props {
  // height: 50%;
  // min-height: 400px;
  padding: 20px 0;
  width: calc(100% - 20px);
  .prompt-wrap {
    margin-bottom: 20px;
  }
}
</style>
