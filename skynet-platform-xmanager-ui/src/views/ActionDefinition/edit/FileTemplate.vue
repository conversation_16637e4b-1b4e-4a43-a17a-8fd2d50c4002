<template>
  <div class="tmpl-files">
    <div v-if="edit" class="buttons">
      <el-button size="mini" type="primary" plain icon="el-icon-plus" @click="onCreate">{{ $t('112') }}</el-button>
    </div>
    <div v-else>
      <prompt class="tmpl-propmt">{{ $t('355') }}</prompt>
    </div>
    <div v-if="form.files && form.files.length > 0">
      <el-form :model="form" label-width="100px" size="mini" ref="fileTemplateForm" :hide-required-asterisk="!edit">
        <div class="file-item" v-for="(item, index) in form.files" :key="index">
          <div class="index">{{ index + 1 }}.</div>
          <div class="info">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('356')" :prop="'files.' + index + '.targetFile'" :rules="rules.targetFile">
                  <el-input v-if="edit" size="mini" v-model="item.targetFile"></el-input>
                  <div v-else class="not-edit">{{ item.targetFile }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('357')" :prop="'files.' + index + '.encoding'">
                  <el-input v-if="edit" size="mini" v-model="item.encoding" :placeholder="$t('358')"></el-input>
                  <div v-else class="not-edit">{{ item.encoding }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('359')" :prop="'files.' + index + '.mode'">
                  <el-input v-if="edit" size="mini" v-model="item.mode" :placeholder="$t('360')"></el-input>
                  <div v-else class="not-edit">{{ item.mode }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('361')" :prop="'files.' + index + '.owner'">
                  <el-input v-if="edit" size="mini" v-model="item.owner" :placeholder="$t('362')"></el-input>
                  <div v-else class="not-edit">{{ item.owner }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="info-item content">
              <div class="label">{{ $t('363') }}</div>
              <div class="input">
                <div class="not-edit">
                  <div v-if="item.__collapsed" class="collapse" @click="collapseClick(index)">
                    <span class="">{{ $t('364') }}</span
                    ><i class="el-icon-bottom"></i>
                  </div>
                  <div v-else class="collapse" @click="collapseClick(index)">
                    <span class="">{{ $t('365') }}</span
                    ><i class="el-icon-top"></i>
                  </div>
                </div>
                <div class="clear" />
              </div>
            </div>
            <div class="clear" />
            <prism-editor
              :readonly="!edit"
              v-if="!item.__collapsed"
              :class="{ 'my-prism-editor': true, text: true, edit }"
              v-model="item.text"
              :highlight="highlighters[index]"
              line-numbers
            ></prism-editor>
          </div>
          <div class="delete">
            <i v-if="edit" class="el-icon-delete" @click="onDelete(index)" :title="$t('366')" />
          </div>
          <div class="clear"></div>
        </div>
      </el-form>
    </div>
    <div v-else-if="!edit">
      <div v-if="!langSelect" class="no-data"></div>
      <div v-if="langSelect" class="no-data-en"></div>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import Prompt from '@/components/common/Prompt'
import ext2lang from './extension2lang'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-properties'
// import 'prismjs/themes/prism-dark.css'
export default {
  name: 'FileTemplate',
  components: {
    Prompt
  },
  props: {
    data: {
      type: Array,
      default: null
    },
    edit: {
      type: Boolean,
      default: true
    },
    langSelect: {
      type: Boolean,
      default: localStorage.getItem('locale-lang') === 'en'
    }
  },
  created() {
    if (this.data) {
      this.initData(this.data)
    }
  },
  watch: {
    data(val) {
      if (val) {
        this.initData(val)
      }
    }
  },
  data() {
    return {
      originalFiles: null,
      form: {
        files: []
      },
      rules: {
        targetFile: [
          {
            required: true,
            message: locale.t('367'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    language(idx) {
      const reg = /\.(.*)$/
      let text = this.form.files[idx].text
      reg.test(text)
      return RegExp.$1
    },
    highlighters() {
      const reg = /\.(.*)$/
      let ret = []
      if (this.form.files && this.form.files.length > 0) {
        for (let file of this.form.files) {
          reg.test(file.targetFile)
          let extension = RegExp.$1
          if (!extension) {
            extension = 'html'
          }
          let langName = extension in ext2lang ? ext2lang[extension] : extension
          let lang = Prism.languages[langName]
          if (!lang) {
            lang = Prism.languages['html']
          }
          let func = data => {
            // if (lang) {
            //   return Prism.highlight(data, lang, extension)
            // } else {
            //   return `<pre>${data}</pre>`
            // }
            return Prism.highlight(data, lang, extension)
          }
          ret.push(func)
        }
      }
      return ret
    }
  },
  methods: {
    initData(files) {
      this.originalFiles = files
      if (files) {
        let dup = JSON.parse(JSON.stringify(files))
        dup.map((v, idx) => {
          if (idx === 0) {
            v.__collapsed = false
          } else {
            v.__collapsed = true
          }
        })
        this.form.files = dup
      }
    },
    collapseClick(index) {
      let file = this.form.files[index]
      file.__collapsed = !file.__collapsed
    },
    onCreate() {
      let newFileTemplate = {
        targetFile: '',
        encoding: 'utf-8',
        text: '',
        __collapsed: false
      }
      this.form.files.splice(0, 0, newFileTemplate)
    },
    onDelete(index) {
      this.form.files.splice(index, 1)
    },
    getFiles() {
      let dup = this.form.files ? JSON.parse(JSON.stringify(this.form.files)) : []
      dup.map(v => delete v['__collapsed'])
      return dup
    },
    reset() {
      this.initData(this.originalFiles)
    },
    validate() {
      if (this.$refs.fileTemplateForm) {
        return this.$refs.fileTemplateForm.validate()
      } else {
        return new Promise((resolve, reject) => {
          resolve(true)
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tmpl-files {
  .no-data {
    height: calc(
      100vh - 295px
    ); // headtop + navbar + scroll + padding + el-tabs__nav_scroll + footer = 55px + 40px + 34px + 60px + (41px + 15px) + 50px  = 295px
    background: url('../../../assets/img/nodata.png') no-repeat center;
  }
  .no-data-en {
    height: calc(
      100vh - 295px
    ); // headtop + navbar + scroll + padding + el-tabs__nav_scroll + footer = 55px + 40px + 34px + 60px + (41px + 15px) + 50px  = 295px
    background: url('../../../assets/img/nodata_en.png') no-repeat center;
  }
  .buttons {
    height: 43px;
    padding-bottom: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid $border-color-light;
  }
  .buttons,
  .tmpl-propmt {
    margin-bottom: 15px;
  }
  .file-item {
    width: 100%;
    padding: 20px 0;
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
    }
    .index {
      height: 28px;
      line-height: 28px;
      float: left;
      width: 50px;
      padding-left: 10px;
    }
    .info {
      float: left;
      width: calc(100% - 50px - 80px);
      .info-item {
        float: left;
        .label {
          float: left;
          width: 80px;
          font-size: 14px;
          height: 28px;
          line-height: 28px;
          font-weight: bold;
        }
        .input {
          float: left;
          width: calc(100% - 118px);
          min-height: 28px;
          line-height: 28px;
        }
        .collapse {
          display: inline-block;
          // padding-left: 10px;
          color: #367ae0;
          i {
            margin-left: 10px;
          }
          &:hover {
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }
      .target-file,
      .encoding {
        width: 50%;
        margin-bottom: 15px;
      }
      .content {
        width: 100%;
        margin-bottom: 15px;
      }
      .text {
        // margin-top: 10px;
        padding: 10px;
        margin: 0;
        // background-color: rgba(64, 158, 255, 0.1);
        // border: 2px dashed $border-color-dark;
        &.edit {
          border: 1px solid $border-color-dark;
          border-radius: 4px;
          // background: #ffffff;
        }
      }
    }
    .delete {
      float: left;
      width: 80px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      color: rgb(245, 108, 108);
      i {
        cursor: pointer;
        &:hover {
          color: rgba(245, 108, 108, 0.5);
        }
      }
    }
  }
}


</style>
