<template>
  <el-dialog
    class="ele-mod plugin-detail-dialog"
    :visible.sync="dialogVisible"
    width="580px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="title"
    top="30vh"
  >
    <el-form :model="formData" label-width="100px" label-position="right" size="small" :inline-message="true" :rules="rules">
      <el-form-item :label="$t('392')" prop="code">
        <el-input v-if="isEdit" :placeholder="$t('407')" v-model="formData.code" :disabled="isCreate === false"></el-input>
        <div v-else class="not-edit">{{ formData.code }}</div>
      </el-form-item>
      <el-form-item :label="$t('408')" prop="name">
        <el-input v-if="isEdit" :placeholder="$t('409')" v-model="formData.name"></el-input>
        <div v-else class="not-edit">{{ formData.name }}</div>
      </el-form-item>
      <el-form-item :label="$t('410')" prop="description">
        <el-input v-if="isEdit" v-model="formData.description" type="textarea" :rows="3"></el-input>
        <div v-else class="not-edit">{{ formData.description }}</div>
      </el-form-item>
      <el-form-item :label="$t('411')" prop="version">
        <el-input v-if="isEdit" :placeholder="$t('412')" v-model="formData.version"></el-input>
        <div v-else class="not-edit">{{ formData.version }}</div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" v-if="isEdit">
      <el-button size="mini" icon="el-icon-check" type="primary" @click="onConfirm()">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
      <div class="clear" />
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import { deepCopy } from '@/common/util'
export default {
  name: 'PluginEditDialog',
  components: {
    // 'cluster-logging-config': ClusterLoggingConfig
  },
  props: {
    successCallback: {
      type: Function
    }
  },
  data() {
    let rules = {
      code: [
        {
          required: true,
          message: locale.t('413'),
          trigger: 'blur'
        },
        {
          pattern: /^[0-9a-zA-Z-_\.]{1,80}$/,
          message: locale.t('414'),
          trigger: 'blur'
        }
      ],
      name: [
        {
          required: true,
          message: locale.t('415'),
          trigger: 'blur'
        },
        {
          pattern: /^.{1,80}$/,
          message: locale.t('416'),
          trigger: 'blur'
        }
      ],
      description: [
        {
          pattern: /^.{1,200}$/,
          message: locale.t('417'),
          trigger: 'blur'
        }
      ],
      version: [
        {
          pattern: /^.{1,100}$/,
          message: locale.t('418'),
          trigger: 'blur'
        }
      ]
    }
    return {
      dialogVisible: false,
      formData: {
        code: '',
        name: '',
        description: '',
        version: ''
      },
      isCreate: false,
      isEdit: false,
      rules
    }
  },
  computed: {
    title() {
      if (this.isEdit) {
        if (this.isCreate) {
          return locale.t('419')
        }
        return `${this.formData.name}${locale.t('242')}`
      }
      return `${this.formData.name}${locale.t('420')}`
    }
  },
  methods: {
    open(isEdit, data) {
      this.isEdit = isEdit
      if (data) {
        this.formData = deepCopy(data)
        this.isCreate = false
      } else {
        this.formData = {
          code: '',
          name: '',
          description: ''
        }
        this.isCreate = true
      }
      this.dialogVisible = true
    },
    onConfirm() {
      let thisVue = this
      if (this.isCreate) {
        this.$api.plugin
          .createPlugin(this.formData)
          .then(() => {
            thisVue.successCallback()
          })
          .finally(() => {
            thisVue.dialogVisible = false
          })
      } else {
        this.$api.plugin
          .updatePlugin(this.formData)
          .then(() => {
            thisVue.successCallback()
          })
          .finally(() => {
            thisVue.dialogVisible = false
          })
      }
    }
  }
}
</script>
<style lang="scss">
.plugin-detail-dialog {
  .not-edit {
    word-break: break-all;
  }
}
</style>
