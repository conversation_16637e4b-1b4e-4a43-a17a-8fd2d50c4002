<template>
  <div>
    <pre style="width:100%;white-space: pre-wrap;word-wrap: break-all;" v-html="content"></pre>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

// import request from '@/axios/request'
import axios from 'axios'
const timeout = process.env.VUE_APP_REQ_TIMEOUT ? parseInt(process.env.VUE_APP_REQ_TIMEOUT) : 30000
const request = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL,
  timeout // request timeout
})
export default {
  name: 'Fetcher',
  data() {
    return {
      url: '',
      content: '',
      windowTitle: null
    }
  },
  created() {
    this.url = window.sessionStorage.getItem(this.$route.params.md5)
    // this.url = this.$route.query.url
    this.windowTitle = this.$route.query.title
    if (this.windowTitle) {
      document.title = this.windowTitle
    }
  },
  watch: {
    url(val) {
      if (val) {
        request
          .get(val, {
            responseType: 'blob'
          })
          .then(resp => {
            let contentType = this.getContentType(resp.headers)
            if (this.isTextType(contentType)) {
              let reader = new FileReader()
              reader.readAsText(resp.data, 'utf-8')
              const __this = this
              reader.onload = function() {
                let data = __this.extactData(reader.result)
                __this.content = __this.toHtml(data)
              }
            } else {
              window.location = val
            }
          })
          .catch(error => {
            let httpStatus = error.response && 'status' in error.response ? '' + error.response.status : ''
            this.$message.error(`${locale.t('1051')}${httpStatus}`)
            console.error(error)
          })
      }
    }
  },
  methods: {
    isTextType(contentType) {
      let ret = false
      if (!contentType) {
        return ret
      }
      const keyWords = ['text', 'json', 'html']
      for (let k of keyWords) {
        if (contentType.indexOf(k) >= 0) {
          ret = true
          break
        }
      }
      return ret
    },
    getContentType(headers) {
      for (let k in headers) {
        if (k.toLocaleLowerCase() === 'content-type') {
          return headers[k]
        }
      }
      return null
    },
    extactData(httpBody) {
      let ret = httpBody
      if (typeof httpBody === 'string' && httpBody.startsWith('{')) {
        let jsonObj
        try {
          jsonObj = JSON.parse(httpBody)
        } catch (err) {
          jsonObj = null
        }
        if (jsonObj && jsonObj['code'] === 0 && 'data' in jsonObj) {
          ret = jsonObj['data']
        }
      }
      return ret
    },
    toHtml(text) {
      let htmlBody
      if (text) {
        if (typeof text === 'string') {
          text = text ? text.trim() : ''
          if (text.startsWith('{') || text.startsWith('[')) {
            try {
              let jsonObj = JSON.parse(text)
              htmlBody = JSON.stringify(jsonObj, null, 2)
            } catch (err) {
              htmlBody = text
            }
          } else {
            htmlBody = text
          }
        } else {
          try {
            htmlBody = JSON.stringify(text, null, 2)
          } catch (err) {
            console.warn('fail to stringfy json of %o', text)
            htmlBody = ''
          }
        }
      } else {
        htmlBody = ''
      }
      htmlBody = this.syntaxHightlight(htmlBody)
      return htmlBody
    },
    // 高亮
    syntaxHightlight(json) {
      json = json
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
      return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function(match) {
        var cls = 'number'
        if (/^"/.test(match)) {
          if (/:$/.test(match)) {
            cls = 'key'
          } else {
            cls = 'string'
          }
        } else if (/true|false/.test(match)) {
          cls = 'boolean'
        } else if (/null/.test(match)) {
          cls = 'null'
        }
        return '<span class="' + cls + '">' + match + '</span>'
      })
    }
  }
}
</script>
<style lang="scss">
.string {
  color: green;
}
.number {
  color: darkorange;
}
.boolean {
  color: blue;
}
.null {
  color: magenta;
}
.key {
  color: red;
}
</style>
