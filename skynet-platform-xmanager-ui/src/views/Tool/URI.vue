<template>
  <div class="uri-tool-container">
    <el-input type="textarea" :rows="10" :placeholder="$t('1117')" v-model="input"> </el-input>
    <div class="tool-operations">
      <el-button size="mini" type="primary" @click="operate('encode')">{{ $t('1132') }}</el-button>
      <el-button size="mini" type="primary" @click="operate('decode')">{{ $t('1133') }}</el-button>
      <el-button size="mini" type="primary" plain @click="reset()">{{ $t('120') }}</el-button>
      <el-button v-show="output" size="mini" type="primary" plain @click="copyOutput()">{{ $t('1120') }}</el-button>
    </div>
    <div v-show="output" class="output">{{ output }}</div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import { copyToClip } from '@/common/util'
export default {
  name: 'URI',
  data() {
    return {
      input: '',
      output: ''
    }
  },
  methods: {
    operate(funcName) {
      let func = funcName === 'encode' ? encodeURIComponent : decodeURIComponent
      let input = this.input.trim()
      if (input) {
        this.output = func(input)
      } else {
        this.output = ''
      }
    },
    reset() {
      this.input = ''
      this.output = ''
    },
    copyOutput() {
      copyToClip(this, this.output)
    }
  }
}
</script>
