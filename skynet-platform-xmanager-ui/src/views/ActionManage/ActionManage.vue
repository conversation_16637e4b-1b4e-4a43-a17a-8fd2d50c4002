<template>
  <div class="action-manage">
    <div class="top" ref="actionManageTopRef">
      <action-manage-header :clusterDeployment="clusterDeployment" @filter-change="onFilterChange" />
    </div>
    <div class="main">
      <div class="operations" ref="operationsRef">
        <div class="card-type">
          <span>{{ $t('serverManage.viewType') }}</span>
          <el-radio-group v-model="cardType" text-color="#091E42" size="mini">
            <el-radio-button label="server">{{ $t('serverManage.server') }}</el-radio-button>
            <el-radio-button label="action">{{ $t('serverManage.service') }}</el-radio-button>
          </el-radio-group>
        </div>
        <div class="display">
          <span>{{ $t('440') }}</span>
          <el-radio-group v-model="display" size="mini">
            <el-radio-button label="expanded">{{ $t('364') }}</el-radio-button>
            <el-radio-button label="collapsed">{{ $t('365') }}</el-radio-button>
          </el-radio-group>
        </div>
        <!-- <div class="adjust-sequence">
          <el-checkbox v-model="adjustingSequence" size="mini">调整卡片顺序</el-checkbox>
        </div> -->
        <div class="buttons">
          <el-button type="primary" icon="el-icon-refresh" @click="refreshAll()">{{ $t('147') }}</el-button>
          <el-button plain type="primary" icon="el-icon-s-comment" @click="showManagerLog()">{{ $t('441') }}</el-button>
          <!-- <el-button plain type="primary" icon="el-icon-s-operation" @click="alloc()">服务分配</el-button> -->
          <el-button plain type="primary" icon="el-icon-refresh-right" @click="rebootAction()">{{ $t('442') }}</el-button>
          <el-button plain type="primary" icon="el-icon-video-play" @click="startAction()">{{ $t('443') }}</el-button>
          <el-button plain type="danger" icon="el-icon-video-pause" @click="stopAction()">{{ $t('444') }}</el-button>
        </div>
        <div class="clear" />
      </div>
      <div ref="cardsViewRef" v-loading="loading" class="cards" :style="{ overflow: loading ? 'visible' : 'auto' }">
        <view-by-action
          v-if="showView && cardType === 'action'"
          :data="renderingData"
          :deployment="clusterDeployment"
          @alloc-success="onAllocSuccess"
        />
        <view-by-server
          v-else-if="showView"
          :data="renderingData"
          :deployment="clusterDeployment"
          @alloc-success="onAllocSuccess"
          @refresh="doRefresh"
        />
      </div>
    </div>
    <allocate-by-action ref="alloc" @success="onAllocSuccess" />
    <simple-ctx-menu ref="ctxMenu" :items="menuItems" @ctx-menu-select="onCtxMenuSelect" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
    <detail-popover ref="popover" />
    <!-- #startUpCmdDialog 为 StartupCmdDialog.vue 提供挂载点 -->
    <div id="startUpCmdDialog" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ActionBadge from './ActionBadge'
import Card from './Card'
import ViewByAction from './ViewByAction'
import ViewByServer from './ViewByServer'
import Header from './Header'
import EventBus from '@/components/event/EventBus'
import AllocateByAction from './allocate/AllocateByAction'
import SimpleContextMenu from '@/components/context-menu/SimpleContextMenu'
import baseMenuItems from './menu/context-menu.js'
import ActionDetailPopover from './popover/ActionDetailPopover'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import MenuHandlers from './menu/menu-handler'
import { mapGetters } from 'vuex'
import { merge, cloneDeep } from 'lodash'
export default {
  name: 'ActionManage',
  components: {
    'action-badge': ActionBadge,
    card: Card,
    'view-by-server': ViewByServer,
    'view-by-action': ViewByAction,
    'action-manage-header': Header,
    'allocate-by-action': AllocateByAction,
    'simple-ctx-menu': SimpleContextMenu,
    'detail-popover': ActionDetailPopover,
    'confirm-dialog': ConfirmDialog
  },
  created() {
    let thisRef = this
    this.initData()
    // 处理在服务图标上右键单击的事件
    EventBus.$on('badge-ctx-menu', event => {
      // console.log('badge-ctx-menu: %o', event)
      thisRef.currentBadgeEvent = event.badgeEvent
      thisRef.$refs.popover.close()
      thisRef.$refs.ctxMenu.open(event.mouseEvent.clientX, event.mouseEvent.clientY)
    })
    // 处理服务图标左键单击事件
    EventBus.$on('badge-click', event => {
      thisRef.$refs.ctxMenu.close()
      let popover = thisRef.$refs.popover
      popover.open(this.clusterDeployment, event)
    })
    // 处理服务图标checkbox值的变化
    EventBus.$on('badge-check-change', event => {
      let map = thisRef.actionsOnCheckedMap.get(event.ip)
      if (!map) {
        map = new Map()
        thisRef.actionsOnCheckedMap.set(event.ip, map)
      }
      if (event.checked) {
        map.set(event.actionPoint, event)
      } else {
        map.delete(event.actionPoint)
      }
    })
  },
  data() {
    let historyFilters = null
    try {
      let history = localStorage.getItem('action-manage-filters')
      if (history != null && history != undefined) {
        historyFilters = JSON.parse(history)
      }
    } catch (error) {
      console.error(error)
    }
    return {
      loading: true,
      cardType: 'server',
      // display: 'expanded',
      adjustingSequence: false,
      clusterDeployment: [],
      filters: historyFilters,
      currentBadgeEvent: null,
      actionsOnCheckedMap: new Map(),
      // 选中的服务实例,key: ip, value: Map类型。value的key： actionPoint, value的value：event
      showView: true // 用来刷新<view-by-server/>和 <view-by-action/>
    }
  },
  watch: {
    cardType() {
      // 当视图切换时，需要重置actionsOnCheckedMap,否则ViewBy*视图重新生成但是actionsOnCheckedMap还保留之前的状态
      this.actionsOnCheckedMap.clear()
    }
  },
  computed: {
    ...mapGetters(['cardCollapsed']),
    display: {
      get() {
        return this.cardCollapsed ? 'collapsed' : 'expanded'
      },
      set(val) {
        if (val === 'collapsed') {
          this.$store.dispatch('skynet/updateCardCollapsed', true)
        } else {
          this.$store.dispatch('skynet/updateCardCollapsed', false)
        }
      }
    },
    renderingData() {
      if (!this.filters) {
        localStorage.removeItem('action-manage-filters')
        return this.clusterDeployment
      } else {
        localStorage.setItem('action-manage-filters', JSON.stringify(this.filters))
      }
      let ret = []
      for (let agentDeploy of this.clusterDeployment) {
        // 按服务器标签过滤
        if (this.filters.select.serverTag && agentDeploy.serverTags.indexOf(this.filters.select.serverTag) === -1) {
          continue
        }
        // 按服务器状态过滤
        if (this.filters.select.serverStatus && agentDeploy.agentStatus !== this.filters.select.serverStatus) {
          continue
        }
        // 按服务器ip搜索过滤
        if (this.filters.search.input && this.filters.search.type === 'search-ip' && agentDeploy.ip.indexOf(this.filters.search.input) === -1) {
          continue
        }
        let newAgentDeploy = {
          loading: false,
          ...agentDeploy
        }
        newAgentDeploy.actions = []
        for (let action of agentDeploy.actions) {
          // 按应用系统过滤
          if (this.filters.select.plugin && action.pluginCode !== this.filters.select.plugin) {
            continue
          }
          // 按服务状态过滤
          if (this.filters.select.actionStatus && action.__view_status !== this.filters.select.actionStatus) {
            continue
          }
          // 按服务名过滤
          if (
            this.filters.search.input &&
            this.filters.search.type === 'search-action' &&
            action.actionName.toLocaleLowerCase().indexOf(this.filters.search.input.toLocaleLowerCase()) === -1
          ) {
            continue
          }
          newAgentDeploy.actions.push(action)
        }
        ret.push(newAgentDeploy)
      }
      return ret
    },
    menuItems() {
      let ret = []
      // let ret = Array.from(baseMenuItems)
      if (this.currentBadgeEvent) {
        if (this.currentBadgeEvent.agentStatus === 'UP') {
          ret = Array.from(baseMenuItems)
          let append =
            this.currentBadgeEvent.__view_status === 'disabled'
              ? {
                index: 'start',
                label: locale.t('445'),
                supports: ['server', 'kubernetes']
              }
              : {
                index: 'stop',
                label: locale.t('446'),
                supports: ['server', 'kubernetes']
              }
          ret.push(append)
        } else {
          // 当AGENT节点离线时，仅显示 '查看服务定义'
          ret = baseMenuItems.filter(v => v.index === 'showActionDef')
        }
      }
      return ret.filter(v => v.supports.includes(this.currentBadgeEvent.agentType))
    }
  },
  methods: {
    alloc() {
      this.$refs.alloc.open(this.clusterDeployment, null)
    },
    onFilterChange(event) {
      this.filters = event
    },
    onCtxMenuSelect(index) {
      if (index === 'start') {
        this.startAction(this.currentBadgeEvent)
      } else if (index === 'stop') {
        this.stopAction(this.currentBadgeEvent)
      } else if (index === 'reboot') {
        this.rebootAction(this.currentBadgeEvent)
      } else {
        let handler = MenuHandlers[index]
        if (handler) {
          handler(this.currentBadgeEvent)
        }
      }
    },
    startAction(event) {
      this.operate('start', event)
    },
    stopAction(event) {
      this.operate('stop', event)
    },
    rebootAction(event) {
      this.operate('reboot', event)
    },
    operate(operation, event) {
      let events = []
      if (event) {
        events.push(event)
      } else {
        for (let actionPoint2Event of Array.from(this.actionsOnCheckedMap.values())) {
          for (let eventInArray of Array.from(actionPoint2Event.values())) {
            events.push(eventInArray)
          }
        }
      }
      if (events.length === 0) {
        this.$message.info(locale.t('447'))
        return
      }
      const dict = {
        start: {
          api: this.$api.status.startAction,
          apiPara: events => {
            let ipAndActionPoint = new Set()
            events.forEach(v => {
              ipAndActionPoint.add(`${v.ip}-${v.actionPoint}`)
            })
            return Array.from(ipAndActionPoint.values()).map(v => {
              let idx = v.indexOf('-')
              return {
                ip: v.substring(0, idx),
                actionPoint: v.substring(idx + 1, v.length),
                enabled: true
              }
            })
          },
          operationChs: locale.t('443'),
          supportedStatus: ['disabled']
        },
        stop: {
          api: this.$api.status.stopAction,
          apiPara: events => {
            let ipAndActionPoint = new Set()
            events.forEach(v => {
              ipAndActionPoint.add(`${v.ip}-${v.actionPoint}`)
            })
            return Array.from(ipAndActionPoint.values()).map(v => {
              let idx = v.indexOf('-')
              return {
                ip: v.substring(0, idx),
                actionPoint: v.substring(idx + 1, v.length),
                enabled: false
              }
            })
          },
          operationChs: locale.t('444'),
          supportedStatus: ['running', 'starting', 'failed']
        },
        reboot: {
          api: this.$api.status.rebootAction,
          apiPara: events => {
            let ip2AidList = new Map()
            events.forEach(v => {
              let aidList = ip2AidList.get(v.ip)
              if (!aidList) {
                aidList = []
                ip2AidList.set(v.ip, aidList)
              }
              aidList.push(v.actionID)
            })
            return Array.from(ip2AidList.entries()).map(entry => {
              return {
                ip: entry[0],
                actionIdList: entry[1]
              }
            })
          },
          operationChs: locale.t('442'),
          supportedStatus: ['running', 'failed']
        }
      }
      const __this = this
      let { api, apiPara, operationChs, supportedStatus } = dict[operation]
      let filteredEvents = events.filter(v => supportedStatus.indexOf(v.__view_status) > -1)
      if (filteredEvents.length === 0) {
        this.$message.info(locale.t('448'))
        return
      }
      let confirmCallback = () => {
        let para = apiPara(filteredEvents)
        api(para)
          .then(resp => {
            if (operation !== 'reboot') {
              this.$message.success(`${locale.t('449')}${operationChs}${locale.t('450')}`)
            } else {
              if (resp instanceof Array) {
                if (resp.length === para.length) {
                  this.$message.success(locale.t('451'))
                } else {
                  let failedCount = para.length - resp.length
                  if (para.length === 1) {
                    this.$message.error(locale.t('452'))
                  } else {
                    this.$message.error(`${failedCount}${locale.t('453')}`)
                    console.error(locale.t('454'), resp)
                  }
                }
              } else {
                this.$message.error(locale.t('455'), 'error')
              }
            }
          })
          .catch(err => {
            this.$message.error(`${locale.t('449')}${operationChs}${locale.t('456')}`)
            console.error(err)
          })
          .finally(() => {
            __this.refreshByServer(filteredEvents[0].ip, 2000)
          })
      }
      let title = `${locale.t('75')}${operationChs}${locale.t('457')}`
      let names = filteredEvents.map(v => `${v.ip}${locale.t('458')}${v.actionName}`)
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    refreshAll(delay) {
      this.actionsOnCheckedMap.clear()
      if (delay) {
        const __this = this
        setTimeout(() => {
          __this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    initData() {
      let thisRef = this
      this.loading = true
      // 根据节点获取k8sNamespace
      Promise.all([this.$api.agent.getAgents(), this.$api.deployment.getClusterDeployment()])
        .then(res => {
          const [agentsRes, deploymentRes] = res
          deploymentRes.forEach(item => {
            let obj = agentsRes.find(el => el.ip === item.ip)
            item.k8sNamespace = obj ? obj.k8sNamespace : ''
            item.actions.forEach(el => (el.k8sNamespace = item.k8sNamespace))
          })
          thisRef.clusterDeployment = deploymentRes
          // 刷新<view-by-server/>和 <view-by-action/>
          thisRef.showView = false
          thisRef.$nextTick(() => {
            thisRef.showView = true
          })
        })
        .finally(() => {
          thisRef.loading = false
        })
    },
    refreshByServer(ip, delay) {
      this.actionsOnCheckedMap.clear()
      if (delay) {
        const __this = this
        setTimeout(() => {
          __this.doRefresh(ip)
        }, delay)
      } else {
        this.doRefresh(ip)
      }
    },
    doRefresh(ip) {
      let index = this.clusterDeployment.findIndex(item => item.ip === ip)
      let data = this.clusterDeployment[index]
      data.loading = true
      let k8sNamespace = data.k8sNamespace

      this.$set(this.clusterDeployment, index, data)
      this.$api.deployment
        .getDeploymentByServer(ip)
        .then(res => {
          if (res) {
            data = res
          }
        })
        .catch(() => {})
        .finally(() => {
          data.loading = false
          data.k8sNamespace = k8sNamespace
          data.actions.forEach(el => (el.k8sNamespace = data.k8sNamespace))
          this.$set(this.clusterDeployment, index, data)
        })
    },
    onAllocSuccess() {
      this.refreshAll(2000)
    },
    showManagerLog() {
      this.$router.push('/log/manager')
    }
  }
}
</script>
<style lang="scss">
.action-manage {
  height: 100%;
  .card__body {
    .badge-group-list {
      overflow: hidden;
    }
    .badge-group {
      padding: 10px 10px 0 0;
      float: left;
      margin-left: 10px;
      margin-bottom: 10px;
      margin-right: 0;
      border: 1px dashed $border-color-dark;
      legend {
        font-size: 12px;
        margin-left: 5px;
      }
    }
    .action-badge {
      margin: 0 0 10px 10px;
    }
  }
  .main {
    height: calc(100% - 86px - 10px);
    margin-top: 10px;
    background-color: #ffffff;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    .cards {
      height: calc(100% - 64px);
      padding: 0 10px 10px 10px;
      overflow: auto;
    }
    .card + .card {
      margin-top: 12px;
    }
  }
  .operations {
    padding: 18px 10px;
    .card-type,
    .display,
    .adjust-sequence {
      float: left;
    }
    .display,
    .adjust-sequence {
      margin-left: 50px;
    }
    .buttons {
      float: right;
    }
  }
  .el-icon-document {
    color: #367ae0;
  }
}
</style>
