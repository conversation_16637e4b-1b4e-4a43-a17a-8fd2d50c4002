<template>
  <div v-if="show" class="popover" :style="{ width: popoverWidth, height: popoverHeight, top: top + 'px', left: left + 'px' }" @click.stop="">
    <slot></slot>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'Popover',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    top: {
      type: Number,
      required: true
    },
    left: {
      type: Number,
      required: true
    },
    height: {
      type: String
    },
    width: {
      type: String
    }
  },
  data() {
    return {}
  },
  computed: {
    popoverWidth() {
      if (this.width) {
        return this.width
      }
      return 'auto'
    },
    popoverHeight() {
      if (this.height) {
        return this.height
      }
      return 'auto'
    }
  },
  watch: {
    show(val) {
      let thisRef = this
      let mouseEventHandler = function() {
        thisRef.$emit('update:show', false)
      }
      if (val) {
        document.addEventListener('click', mouseEventHandler)
      } else {
        document.removeEventListener('click', mouseEventHandler)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.popover {
  position: fixed;
  background-color: #ffffff;
  // border: 1px solid $border-color-light;
  box-shadow: 0px 0px 8px 1px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}
</style>
