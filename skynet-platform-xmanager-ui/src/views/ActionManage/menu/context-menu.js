import locale from '@/i18n/index.js'
export default [
  {
    index: 'showActionDef',
    label: locale.t('492'),
    supports: ['server', 'kubernetes']
  },
  {
    index: 'showProps',
    label: locale.t('493'),
    supports: ['server']
  },
  {
    index: 'showStartupCmd',
    label: locale.t('494'),
    supports: ['server', 'kubernetes']
  },
  {
    index: 'showDeployment',
    label: locale.t('495'),
    supports: ['kubernetes']
  },
  {
    index: 'showHealthEvent',
    label: locale.t('496'),
    supports: ['server']
  },
  {
    index: 'showStdoutLog',
    label: locale.t('497'),
    supports: ['server', 'kubernetes']
  },
  {
    index: 'showStdout',
    label: locale.t('498'),
    supports: ['server']
  },
  {
    index: 'showMonitGraph',
    label: locale.t('499'),
    supports: ['server']
  },
  {
    index: 'reboot',
    label: locale.t('500'),
    supports: ['server', 'kubernetes']
  }
]
