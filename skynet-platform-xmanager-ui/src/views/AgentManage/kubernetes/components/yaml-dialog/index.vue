<template>
  <el-dialog :visible.sync="dialogShowed" :title="title" top="5vh" width="80%" @close="onClose">
    <div class="yaml-editor-header" v-if="!create">
      <el-link :underline="false" type="primary" @click="onCopy">{{ $t('631') }}</el-link>
      <el-checkbox v-model="simple" :disabled="editMode" @change="onSimpleChange" style="margin-left: 50px; margin-right: 0;">
        <el-link :underline="false" type="primary">{{ $t('632') }}</el-link>
      </el-checkbox>
      <el-checkbox v-model="status" :disabled="editMode" @change="onStatusChange" style="margin-left: 50px; margin-right: 0;">
        <el-link :underline="false" type="primary">{{ $t('633') }}</el-link>
      </el-checkbox>
    </div>
    <div class="yaml-editor">
      <textarea ref="textarea" />
    </div>
    <div v-if="editMode || create" slot="footer" class="dialog-footer" style="padding-top: 10px; text-align: right">
      <div v-if="create" style="display: inline-block; margin-left: 10px; float: left;">
        <el-upload action="#" :auto-upload="false" :show-file-list="false" accept=".yaml" :on-change="onUpload">
          <el-button size="mini" type="primary" plain style="margin-left: -10px" icon="el-icon-upload">{{ $t('634') }}</el-button>
        </el-upload>
      </div>
      <el-button @click="onCancel" size="mini" type="primary" plain icon="el-icon-close">{{ $t('635') }}</el-button>
      <el-button
        v-loading="loading"
        @click="onConfirm"
        :disabled="!currentValue"
        size="mini"
        type="primary"
        icon="el-icon-check"
        style="margin-left: 10px"
        >{{ $t('636') }}</el-button
      >
    </div>
    <div v-else slot="footer" class="dialog-footer" style="padding-top: 10px; text-align: right">
      <div v-if="!readonly" style="display: inline-block; margin-left: 10px; float: left;">
        <el-button @click="onEdit" size="mini" type="primary" plain style="margin-left: -10px" icon="el-icon-edit">{{ $t('637') }}</el-button>
      </div>
      <el-button @click="onClose" size="mini" type="primary" plain style="margin-left: 30px" icon="el-icon-close">{{ $t('635') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/yaml/yaml'
import 'codemirror/theme/base16-light.css'
import 'codemirror/theme/darcula.css'
import 'codemirror/addon/lint/lint.css'
import 'codemirror/addon/lint/lint'
import 'codemirror/addon/lint/yaml-lint'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/addon/fold/indent-fold'
window.jsyaml = require('js-yaml')
import { copyToClip } from '@/common/util'
export default {
  name: 'YamlEditor',
  /* eslint-disable vue/require-prop-types */
  props: ['value', 'title', 'ip', 'create', 'readonly'],
  data() {
    return {
      dialogShowed: true,
      yamlEditor: false,
      editMode: false,
      simple: true,
      status: true,
      loading: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.yamlEditor = CodeMirror.fromTextArea(this.$refs.textarea, {
        lineNumbers: true,
        mode: 'yaml',
        theme: this.create ? 'darcula' : 'base16-light',
        foldGutter: true,
        styleActiveLine: true,
        foldGutter: true,
        lint: true,
        gutters: ['CodeMirror-lint-markers', 'CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        readOnly: !this.create
      })
      this.refreshYamlContent()
    })
  },
  computed: {
    currentValue() {
      return this.getValue()
    }
  },
  methods: {
    getValue() {
      return this.yamlEditor ? this.yamlEditor.getValue() : ''
    },
    onUpload(file) {
      let _this = this
      let reader = new FileReader()
      reader.onload = function(e) {
        _this.yamlEditor.setValue(e.target.result)
      }
      reader.readAsText(file.raw)
    },
    onEdit() {
      this.editMode = true
      this.yamlEditor.setOption('readOnly', false)
      this.yamlEditor.setOption('theme', 'darcula')
      // 编辑模式下，显示精简的 YAML
      this.simple = true
      this.status = true
      this.refreshYamlContent()
    },
    onClose() {
      this.dialogShowed = false
      this.$emit('close')
    },
    onCancel() {
      if (this.create) {
        this.onClose()
      } else {
        this.editMode = false
        this.yamlEditor.setOption('readOnly', true)
        this.yamlEditor.setOption('theme', 'base16-light')
      }
    },
    onConfirm() {
      this.loading = true
      this.$api.kubernetes
        .kubectlApply(this.ip, this.getValue())
        .then(res => {
          this.dialogShowed = false
          this.$emit('refresh')
        })
        .finally(() => {
          this.loading = false
        })
    },
    onCopy() {
      copyToClip(this, this.getValue())
    },
    onSimpleChange() {
      this.refreshYamlContent()
    },
    onStatusChange(status) {
      this.refreshYamlContent()
    },
    refreshYamlContent() {
      if (!this.simple && !this.status) {
        this.yamlEditor.setValue(this.value)
      } else {
        let json = jsyaml.load(this.value)
        if (this.simple) {
          delete json.metadata.managedFields
          delete json.metadata.creationTimestamp
          delete json.metadata.generation
          delete json.metadata.uid
          delete json.metadata.resourceVersion
          if (json.metadata.annotations) {
            delete json.metadata.annotations['kubectl.kubernetes.io/last-applied-configuration']
          }
        }
        if (this.status) {
          delete json.status
        }
        let yaml = jsyaml.dump(json)
        this.yamlEditor.setValue(yaml)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.yaml-editor-header {
  margin-bottom: 15px;
}

.yaml-editor {
  position: relative;
  border: 1px solid #ececec;
}

.yaml-editor{
  ::v-deep .CodeMirror {
  height: 550px;
  font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, 微软雅黑, monospace,
    serif !important;
}
::v-deep .CodeMirror-scroll {
  height: 550px;
}
::v-deep .cm-s-rubyblue span.cm-string {
  color: #f08047;
}
}
</style>
<style lang="scss">
.CodeMirror-lint-tooltip {
  z-index: 3000 !important;
}
</style>
