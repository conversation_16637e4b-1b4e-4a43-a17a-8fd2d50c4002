<template>
  <div class="sky-table-contanier node-contanier">
    <div class="toolbar">
      <el-input
        style="width:25%"
        :placeholder="$t('951')"
        suffix-icon="el-icon-search"
        v-model="filterKeyword"
        @clear="filterKeyword = null"
        clearable
      />
      <div class="right">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="onRefresh()">{{ $t('147') }}</el-button>
      </div>
    </div>
    <div class="body">
      <el-table :data="viewList" header-row-class-name="headbg" :height="_setTHeight(120)" v-loading="loading" :element-loading-text="$t('598')">
        <el-table-column align="center" type="index" :label="$t('228')" width="50"> </el-table-column>
        <el-table-column align="left" :label="$t('952')" min-width="130" prop="metadata.name" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link style="color: #3b74f0" @click="onObjectDetail(scope.row)">
              {{ scope.row.metadata.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="left" :label="$t('477')" width="80">
          <template slot-scope="scope">
            <i class="status-point" :status="scope.row.Ready ? 'enable' : 'failed'" />
            <span>{{ scope.row.Ready ? $t('9') : $t('10') }}</span>
            <el-tag v-if="scope.row.spec.unschedulable" type="danger" size="mini">{{ $t('953') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('954')" width="120" prop="InternalIP" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.InternalIP }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="CPU" width="80" prop="status.capacity.cpu" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ formatCpuCores(scope.row.status.cpuUsed) }}/{{ scope.row.status.capacity.cpu }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="MEM" width="120" prop="status.capacity.memory" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ formatMemoryBytes(scope.row.status.memUsed) }}/{{ formatMemory(scope.row.status.capacity.memory) }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" width="120" :label="$t('955')" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.status.nodeInfo.kubeletVersion }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('956')" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.status.nodeInfo.osImage }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('644')" width="100" prop="metadata.creationAt" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.metadata.creationAt" placement="top-start">
              <span> {{ scope.row.metadata.creationAge }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="$t('239')" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-dropdown
              split-button
              plain
              size="mini"
              icon="el-icon-view"
              @command="handleCommand($event, scope.row)"
              @click="onTerminal(scope.row)"
            >
              <span>{{ $t('769') }}</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="scope.row.spec.unschedulable" icon="el-icon-video-play" command="uncordon">{{ $t('957') }}</el-dropdown-item>
                <el-dropdown-item v-else icon="el-icon-video-pause" command="cordon">{{ $t('924') }}</el-dropdown-item>
                <el-dropdown-item icon="el-icon-delete" command="drain">{{ $t('925') }}</el-dropdown-item>
                <el-dropdown-item icon="el-icon-document" command="yaml">{{ $t('645') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" v-if="yamlShowDialog" @close="yamlShowDialog = false" @refresh="onRefresh" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import k8sUtil from '@/utils/k8s/com'
import { formatKibString } from '@/utils/index'
import { mapGetters } from 'vuex'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import { convertCpuCores, convertMemoryBytes } from '@/utils/quantity'
export default {
  name: 'nodes',
  components: {
    YamlDialog,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      filterKeyword: null,
      dataList: [],
      viewList: [],
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    ip() {
      return this.$route.params.ip
    },
    ...mapGetters(['skynetInfo', 'uiVars']),
    k8sConsoleEnabled() {
      return this.uiVars.k8s_console_enabled
    }
  },
  watch: {
    filterKeyword: {
      handler: function(val, oldVal) {
        this.filterList(val)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      this.$api.kubernetes
        .getNodes(this.ip)
        .then(res => {
          this.dataList = k8sUtil.parseK8sDataList(res)
          this.dataList.map(x => {
            x.InternalIP = this.dowithRow(x.status.addresses, 'type', 'InternalIP', 'address')
            x.Ready = this.dowithRow(x.status.conditions, 'type', 'Ready', 'status') === 'True'
            return x
          })
          this.showMetrics(this.dataList)
          this.filterList(this.filterKeyword)
        })
        .finally(() => {
          this.loading = false
        })
    },
    filterList(filterKeyword) {
      this.viewList = filterKeyword
        ? this.dataList.filter(item => {
          return item.metadata.name.indexOf(filterKeyword) >= 0 || item.InternalIP.indexOf(filterKeyword) >= 0
        })
        : this.dataList
    },
    onNamespaceSelectChange(val) {
      this.namespace = val
      this.filterList(this.filterKeyword)
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onObjectDetail(row) {
      this.$router.push({
        name: 'kubernetesNodeDetail',
        params: {
          ip: this.ip,
          nodename: row.metadata.name
        }
      })
    },
    handleCommand(cmd, row) {
      if (cmd === 'yaml') {
        this.onYAML(row)
      } else if (cmd === 'cordon') {
        this.onCordon(row)
      } else if (cmd === 'uncordon') {
        this.onUncordon(row)
      } else if (cmd === 'drain') {
        this.onDrain(row)
      }
    },
    onTerminal(row) {
      //  path: ':ip/node/:node/console'
      window.open(`/#/kubernetes/${this.ip}/node/${row.metadata.name}/console`)
    },
    onCordon(row) {
      let confirmCallback = () => {
        this.loading = true
        this.$api.kubernetes
          .cordonNode(this.ip, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('928')}`)
            this.initData()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('958')}`
      let names = [`${locale.t('959')}${row.metadata.name}`]
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onUncordon(row) {
      let confirmCallback = () => {
        this.loading = true
        this.$api.kubernetes
          .uncordonNode(this.ip, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('930')}`)
            this.initData()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('960')}`
      let names = [`${locale.t('959')}${row.metadata.name}`]
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onDrain(row) {
      let confirmCallback = () => {
        this.loading = true
        this.$api.kubernetes
          .drainNode(this.ip, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('932')}`)
            this.initData()
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('961')}`
      let names = [`${locale.t('959')}${row.metadata.name}`]
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onYAML(row) {
      this.loading = true
      this.$api.kubernetes
        .getNodeYaml(this.ip, row.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('934')}${row.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    dowithRow(list, prop, value, key) {
      const findObj = list.find(item => item[prop] === value)
      return findObj ? findObj[key] : ''
    },
    formatMemory(memory) {
      return formatKibString(memory)
    },
    showMetrics(items) {
      this.$api.kubernetes.getNodeMetrics(this.ip, {}).then(res => {
        res.forEach(x => {
          let nodes = items.filter(y => {
            return y.metadata.name === x.metadata.name
          })
          if (nodes.length === 1) {
            nodes[0].status.cpuUsed = convertCpuCores(x.usage.cpu)
            nodes[0].status.memUsed = convertMemoryBytes(x.usage.memory)
          }
        })
        this.dataList = items
        this.filterList(this.filterKeyword)
        // items.forEach(x => {
        //   x.status.cpuPercentage = this.getPercentage(x.status.cpuUsed, x.status.capacity.cpu)
        //   x.status.memPercentage = this.getPercentage(x.status.memUsed, x.status.capacity.memory)
        // })
      })
    },
    formatCpuCores(cpu) {
      return cpu ? cpu.toFixed(2) : 0
    },
    formatMemoryBytes(memory) {
      return memory ? (memory / 1024 / 1024 / 1024).toFixed(2) + 'GB' : 0
    },
    getPercentage(current, total) {
      return Number.parseFloat(((current / total) * 100).toFixed(2))
    }
  }
}
</script>
<style lang="scss">
.node-contanier {
  .el-select {
    width: 150px !important;
  }
}
</style>
