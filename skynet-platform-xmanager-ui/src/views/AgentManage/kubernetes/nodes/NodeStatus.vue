<template>
  <div>
    <div class="status-graph">
      <div class="status-graph-item" v-for="(item, index) in items" :key="index">
        <div class="status-graph-item-title">
          <div class="graph-title">{{ item.name }}</div>
          <div>
            <span class="graph-used"> {{ item.used }}</span> / <span class="graph-total"> {{ item.total }} </span>
          </div>
        </div>
        <el-progress :stroke-width="18" type="circle" :percentage="item.percentage" :format="formatPercentage" v-loading="loading"></el-progress>
      </div>
    </div>
    <el-table :data="conditions" header-row-class-name="headbg">
      <el-table-column align="center" type="index" width="50" :label="$t('228')" />
      <el-table-column align="left" prop="type" width="150" :label="$t('231')" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.desc" placement="top">
            <span>{{ scope.row.type }} </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="left" :label="$t('477')" width="150">
        <template slot-scope="scope">
          <i class="status-point" :status="scope.row.ok ? 'enable' : 'failed'" />
          <span>{{ scope.row.ok ? $t('487') : $t('488') }}</span>
          ({{ scope.row.status }})
        </template>
      </el-table-column>
      <el-table-column prop="reason" :label="$t('962')" show-overflow-tooltip />
      <el-table-column prop="message" :label="$t('585')" show-overflow-tooltip />
      <el-table-column :label="$t('963')" width="120" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="formatTime(scope.row.lastHeartbeatTime)" placement="top-start">
            <span>{{ diffNowTime(scope.row.lastHeartbeatTime) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column :label="$t('964')" width="150" align="center">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.lastTransitionTime) }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import k8sUtil from '@/utils/k8s/com'
import { convertCpuCores, convertMemoryBytes } from '@/utils/quantity'
export default {
  props: ['ip', 'nodename', 'status', 'metrics'],
  data() {
    return {
      conditionDescDict: {
        OutOfDisk: {
          desc: locale.t('965'),
          expect: 'False'
        },
        Ready: {
          desc: locale.t('966'),
          expect: 'True'
        },
        MemoryPressure: {
          desc: locale.t('967'),
          expect: 'False'
        },
        PIDPressure: {
          desc: locale.t('968'),
          expect: 'False'
        },
        DiskPressure: {
          desc: locale.t('969'),
          expect: 'False'
        },
        NetworkUnavailable: {
          desc: locale.t('970'),
          expect: 'False'
        }
      },
      items: [
        {
          name: 'CPU Core',
          percentage: 0,
          used: '0.00',
          total: '0.00'
        },
        {
          name: locale.t('971'),
          percentage: 0,
          used: '0.00',
          total: '0.00'
        },
        {
          name: locale.t('972'),
          percentage: 0,
          used: '0.00',
          total: '0.00'
        },
        {
          name: locale.t('973'),
          percentage: 0,
          used: '0.00',
          total: '0.00'
        },
        {
          name: locale.t('974'),
          percentage: 0,
          used: '0.00',
          total: '0.00'
        },
        {
          name: locale.t('975'),
          percentage: 0,
          used: '0.00',
          total: '0.00'
        },
        {
          name: locale.t('976'),
          percentage: 0,
          used: '0',
          total: '0'
        }
      ],
      conditions: [],
      pods: [],
      loading: false
    }
  },
  mounted() {
    // this.initData(this.initData)
  },
  watch: {
    status: {
      handler: function(val, oldVal) {
        if (val !== null) {
          this.initData(val)
        }
      },
      deep: true
    }
  },
  methods: {
    initData(status) {
      this.showConditions(status)
      this.loading = true
      this.$api.kubernetes
        .getPods(this.ip, {
          fieldSelector: `spec.nodeName=${this.nodename}`
        })
        .then(res => {
          this.pods = res
          this.showMetrics()
        })
        .finally(() => {
          this.loading = false
        })
    },
    showConditions(status) {
      this.conditions = status.conditions.map(condition => {
        const item = this.conditionDescDict[condition.type]
        condition.desc = item ? item.desc : condition.type
        condition.ok = item ? item.expect === condition.status : false
        return condition
      })
    },
    showMetrics(status) {
      let cpuTotal = this.getCpuTotal()
      let cpuUsed = this.getCpuUsed()
      let cpuRequests = this.getCpuRequests()
      let cpuLimits = this.getCpuLimits()
      let memoryTotal = this.getMemoryTotal()
      let memoryUsed = this.getMemoryUsed()
      let memoryRequests = this.getMemoryRequests()
      let memoryLimits = this.getMemoryLimits()

      // CPU Core
      this.items[0].percentage = this.getPercentage(cpuUsed, cpuTotal)
      this.items[0].used = this.formatCpuCores(cpuUsed)
      this.items[0].total = this.formatCpuCores(cpuTotal)
      // CPU Requests
      this.items[1].percentage = this.getPercentage(cpuRequests, cpuTotal)
      this.items[1].used = this.formatCpuCores(cpuRequests)
      this.items[1].total = this.formatCpuCores(cpuTotal)
      // CPU Limits
      this.items[2].percentage = this.getPercentage(cpuLimits, cpuTotal)
      this.items[2].used = this.formatCpuCores(cpuLimits)
      this.items[2].total = this.formatCpuCores(cpuTotal)
      // Memory
      this.items[3].percentage = this.getPercentage(memoryUsed, memoryTotal)
      this.items[3].used = this.formatMemoryBytes(memoryUsed)
      this.items[3].total = this.formatMemoryBytes(memoryTotal)
      // Memory Requests
      this.items[4].percentage = this.getPercentage(memoryRequests, memoryTotal)
      this.items[4].used = this.formatMemoryBytes(memoryRequests)
      this.items[4].total = this.formatMemoryBytes(memoryTotal)
      // Memory Limits
      this.items[5].percentage = this.getPercentage(memoryLimits, memoryTotal)
      this.items[5].used = this.formatMemoryBytes(memoryLimits)
      this.items[5].total = this.formatMemoryBytes(memoryTotal)
      // Pods
      this.items[6].percentage = this.getPercentage(this.getPodUsed(), this.getPodTotal())
      this.items[6].used = this.getPodUsed()
      this.items[6].total = this.getPodTotal()
    },
    formatTime(dateTime) {
      return k8sUtil.formatK8sDateTime(dateTime)
    },
    diffNowTime(dateTime) {
      return k8sUtil.getK8sDateTimeAge(dateTime)
    },
    getCpuTotal() {
      return convertCpuCores(this.status.capacity.cpu)
    },
    getCpuUsed() {
      return convertCpuCores(this.metrics.cpu)
    },
    getCpuRequests() {
      let cpuRequests = 0
      for (let pod of this.pods) {
        if (!pod.spec) continue
        for (let container of pod.spec.containers) {
          if (container.resources && container.resources.requests) {
            cpuRequests += convertCpuCores(container.resources.requests.cpu)
          }
        }
      }
      return cpuRequests
    },
    getCpuLimits() {
      let cpuLimits = 0
      for (let pod of this.pods) {
        if (!pod.spec) continue
        for (let container of pod.spec.containers) {
          if (container.resources && container.resources.limits) {
            cpuLimits += convertCpuCores(container.resources.limits.cpu)
            console.log(container.resources.limits.cpu, cpuLimits)
          }
        }
      }
      return cpuLimits
    },
    getMemoryTotal() {
      return convertMemoryBytes(this.status.capacity.memory)
    },
    getMemoryUsed() {
      return convertMemoryBytes(this.metrics.memory)
    },
    getMemoryRequests() {
      let memoryRequests = 0
      for (let pod of this.pods) {
        if (!pod.spec) continue
        for (let container of pod.spec.containers) {
          if (container.resources && container.resources.requests) {
            memoryRequests += convertMemoryBytes(container.resources.requests.memory)
          }
        }
      }
      return memoryRequests
    },
    getMemoryLimits() {
      let memoryLimits = 0
      for (let pod of this.pods) {
        if (!pod.spec) continue
        for (let container of pod.spec.containers) {
          if (container.resources && container.resources.limits) {
            memoryLimits += convertMemoryBytes(container.resources.limits.memory)
          }
        }
      }
      return memoryLimits
    },
    getPodTotal() {
      return this.status.capacity.pods
    },
    getPodUsed() {
      return this.pods.length
    },
    formatCpuCores(cpu) {
      return cpu.toFixed(2)
    },
    formatMemoryBytes(memory) {
      return (memory / 1024 / 1024 / 1024).toFixed(2)
    },
    getPercentage(current, total) {
      return (current / total) * 100
    },
    formatPercentage(percentage) {
      return percentage.toFixed(2) + '%'
    }
  }
}
</script>
<style scoped lang="scss">
.status-graph {
  display: inline-block;
  padding: 0 0 10px 0;
}

.status-graph-item {
  border: 1px solid #ccc;
  border-radius: 5px;
  float: left;
  margin: 0 16px 0 0;
  padding: 10px;
}

.graph-title {
  padding: 0 0 8px 0;
  color: #367ae0;
  font-weight: bold;
}

.status-graph-item-title {
  text-align: center;
  padding: 10px 0px;

  .graph-used {
    font-weight: bold;
    font-size: 16px;
  }
}

.status-graph-item:hover {
  background-color: #f5f7fa;
}
</style>
