@font-face {
  font-family: "iconfont"; /* Project id 3610921 */
  src: url('iconfont.woff2?t=1669790203883') format('woff2'),
       url('iconfont.woff?t=1669790203883') format('woff'),
       url('iconfont.ttf?t=1669790203883') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shoucang:before {
  content: "\e62a";
}

.icon-deploymentunit:before {
  content: "\e7d2";
}

.icon-share:before {
  content: "\e7e7";
}

.icon-fuwuguanli:before {
  content: "\e632";
}

.icon-jiankong:before {
  content: "\e61d";
}

.icon-rizhi:before {
  content: "\e668";
}

.icon-jichusheshi:before {
  content: "\e614";
}

.icon-kongzhitai:before {
  content: "\e651";
}

.icon-docker:before {
  content: "\e616";
}

.icon-shubiao:before {
  content: "\e776";
}

.icon-edit:before {
  content: "\e69e";
}

.icon-delete:before {
  content: "\e67e";
}

.icon-server:before {
  content: "\e67a";
}

.icon-kubernetes:before {
  content: "\ebee";
}

.icon-reeor-fill:before {
  content: "\e789";
}

.icon-success-fill:before {
  content: "\e78d";
}

.icon-terminal-fill:before {
  content: "\e863";
}

.icon-wenjian1:before {
  content: "\e62e";
}

.icon-wenjianjia:before {
  content: "\e601";
}

.icon-bianjishuru:before {
  content: "\e8cc";
}

.icon-shanchu:before {
  content: "\e718";
}

.icon-shuaxin:before {
  content: "\e65a";
}

.icon-xinjianwenjianjia:before {
  content: "\e656";
}

.icon-xiazai:before {
  content: "\e66d";
}

.icon-shangchuanfangshi:before {
  content: "\e6bb";
}

