let mockNodeStatus = {
  'uuid': 'APPTUV5RMCNGQI8E',
  'clusterName': 'skynet',
  'hostName': 'skynet',
  'os': {
    'availableProcessors': 72,
    'systemLoad': 4.84,
    'name': 'Linux',
    'arch': 'amd64',
    'version': '3.10.0-862.el7.x86_64'
  },
  'ssl': false,
  'ip': '*************',
  'port': 25254,
  'jmxPort': 0,
  'pid': 172641,
  'antId': 'mq-ist-v10-engl@tuling-stream-sample',
  'name': 'mq-ist-v10-engl@tuling-stream-sample',
  'actionTitle': '[STREAM-SAMPLE]节点3-英文转写',
  'actionDesc': 'skynet-xagent',
  'startTime': '2020-08-12T19:25:55.000+0800',
  'zkServers': '*************:2181',
  'eurekaServers': '',
  'skynetHome': '/iflytek/server/skynet',
  'reloadTime': '2020-08-12T19:25:55.000+0800',
  'nodeParam': null,
  'plugin': null,
  'projectVersion': null,
  'state': {
    'code': 0,
    'success': true,
    'ok': 1,
    'failed': null
  },
  'up': 'UP',
  'prefix': 'mq-ist-v10-engl@tuling-stream-sample@*************@pid:172641',
  'upTime': '1天14小时7分钟'
}

export default mockNodeStatus
