export default [
  {
    'description': '图文识别服务器1',
    'installDocker': false,
    'ip': '************',
    'serverTags': ['GPU', '教育'],
    'sshPassword': '',
    'sshPort': 22,
    'sshUser': 'root',
    'serverInfo': {
      'host': 'node1',
      'os': 'Linux(4.14.0-115.el7a.0.1.aarch64)(aarch64)',
      'cpu': 96,
      'mem': 254,
      'gpu': null
    }
  },
  {
    'description': '图文识别服务器2',
    'installDocker': false,
    'ip': '************',
    'serverTags': ['GPU', '教育'],
    'sshPassword': '',
    'sshPort': 22,
    'sshUser': 'root',
    'serverInfo': {
      'host': 'node2',
      'os': 'Linux(4.14.0-115.el7a.0.1.aarch64)(aarch64)',
      'cpu': 96,
      'mem': 254,
      'gpu': null
    }
  },
  {
    'description': '人脸引擎服务器',
    'installDocker': false,
    'ip': '************',
    'serverTags': ['GPU', 'ZF'],
    'sshPassword': '',
    'sshPort': 22,
    'sshUser': 'root',
    'serverInfo': {
      'host': 'node3',
      'os': 'Linux(4.14.0-115.el7a.0.1.aarch64)(aarch64)',
      'cpu': 96,
      'mem': 254,
      'gpu': null
    }
  },
  {
    'description': '电子卷宗服务器',
    'installDocker': false,
    'ip': '************',
    'serverTags': ['ZF'],
    'sshPassword': '',
    'sshPort': 22,
    'sshUser': 'root',
    'serverInfo': {
      'host': 'node4',
      'os': 'Linux(4.14.0-115.el7a.0.1.aarch64)(aarch64)',
      'cpu': 96,
      'mem': 254,
      'gpu': null
    }
  }
]
