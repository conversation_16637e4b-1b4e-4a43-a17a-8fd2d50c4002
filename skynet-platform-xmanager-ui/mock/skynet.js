import actionDef from './skynet/action-def'
import plugins from './skynet/plugin'
import actuator from './skynet/actuator'
import deployment from './skynet/deployment'
import nodestatus from './skynet/nodestatus'
import files from './skynet/repo'
import agents from './skynet/agent'
import actionTags from './skynet/action-tag'
import { clusterInfo, clusterProps, clusterLoggingLevels } from './skynet/cluster'
import switchLabels from './skynet/switch-labels'

const mapping = [
  {
    url: '/auth/login',
    data: {
      'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHBpcmVzIjoxNTUyNDYwMDUwMjY0LCJwYXNzd29yZCI6ImFkbWluIiwidXNlcm5hbWUiOiJhZG1pbiJ9.cu2lb1WU8PLhugYzAtGDdz1OLlPOuZIzKZ5Mqsnq4v0',
      'expires': 1552460050264,
      'tokenHader': 'basic'
    },
    type: 'post'
  },
  {
    url: 'skynet/api/v3/deployment',
    data: deployment
  },
  {
    url: 'skynet/api/v3/actions/runtime/_nodestatus',
    data: nodestatus
  },
  {
    url: 'skynet/api/v3/actions/runtime/actuator',
    data: actuator
  },
  {
    url: 'skynet/api/v3/actions/definition',
    response: req => {
      let data = actionDef
      let { pluginCode } = req.query
      if (pluginCode) {
        data = actionDef.filter(v => {
          return v.pluginCode === pluginCode
        })
      }
      return {
        code: 0,
        data: data
      }
    }
  },
  {
    url: 'skynet/api/v3/plugins$',
    data: plugins
  },
  {
    url: 'skynet/api/v3/plugins/.*',
    response: req => {
      return {
        code: 0,
        data: plugins[0]
      }
    }
  },
  {
    url: 'skynet/api/v3/repo/files/.*',
    data: files
  },
  {
    url: 'skynet/api/v3/agents',
    data: agents
  },
  {
    url: 'skynet/api/v3/action-tags',
    data: actionTags
  },
  {
    url: 'skynet/api/v3/cluster/info',
    data: clusterInfo
  },
  {
    url: 'skynet/api/v3/cluster/properties',
    data: clusterProps
  },
  {
    url: 'skynet/api/v3/cluster/logging-levels',
    data: clusterLoggingLevels
  },
  {
    url: 'skynet/api/v3/switch-labels',
    data: switchLabels
  }
]

export default mapping.map(v => {
  let response = null
  if (v.response) {
    response = v.response
  } else {
    response = {
      code: 0,
      msg: '',
      data: v['data']
    }
  }
  return {
    url: v.url,
    type: v.type || 'get',
    response
  }
})
