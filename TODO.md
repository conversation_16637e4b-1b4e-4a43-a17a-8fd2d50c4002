# Skynet 待办

- [ ] skynet服务启动本身，属性依赖主要使用本地.
  - [ ] 移除 ConfigServer 依赖
  - [ ] zkPath=/skynet/xmanager/action
  - [ ] zkPath=/skynet/xmanager/menu
  - [x] zkPath=/skynet/xmanager/setting
  - [ ] zkPath=/skynet/xmanager/users

- [ ] 用户登录带角色：
  - [ ] 角色：admin，editor，viewer

- [ ] actuator相关的设置：
  - [ ] management.endpoint.env.show-values=WHEN_AUTHORIZED

- [ ] 修改UI viewer 查看模式
  - [ ] admin：所有权限
  - [ ] editor：编辑权限
  - [ ] viewer：查看权限

- [x] 服务定义
  - [x] 增加服务定义归档功能，归档（包含依赖文件）和恢复。
  - [x] 已经分配服务增加标识。

- [ ] 支持MCP