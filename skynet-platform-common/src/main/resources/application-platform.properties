###################################################################
encrypt.key-store.location=classpath:/skynet-config-server.jks
encrypt.key-store.alias=skynet-config-server
encrypt.key-store.password=skynet2230
encrypt.key-store.secret=skynet2230
###################################################################
management.endpoints.web.exposure.include=*
management.health.defaults.enabled=false
management.metrics.distribution.percentilesHistogram.all=false
#------------------------------------------------------------------
server.tomcat.basedir=./tmp/tomcat
server.tomcat.threads.max=100
server.tomcat.threads.min-spare=10
server.tomcat.uri-encoding=UTF-8
server.tomcat.mbeanregistry.enabled=true
server.tomcat.connection-timeout=1000
server.tomcat.max-connections=1024
###################################################################
spring.aop.auto=true
spring.aop.proxy-target-class=true
spring.threads.virtual.enabled=true
###################################################################
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
server.servlet.context-path=
server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
###################################################################
# ignored docker0 and veth interfaces ipaddress
spring.cloud.inetutils.ignored-interfaces=docker0,veth.*
###################################################################
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.enabled=true
spring.cloud.zookeeper.discovery.register=true
spring.cloud.zookeeper.discovery.instance-host=${spring.cloud.client.ip-address}
spring.cloud.zookeeper.discovery.metadata.server.servlet.context-path=${server.servlet.context-path}
###################################################################
spring.main.allow-bean-definition-overriding=true
###################################################################
# LOGGING
logging.file.path=../log
logging.level.skynet.platform=INFO
logging.level.org.apache.curator=ERROR
logging.level.org.apache.zookeeper=ERROR
logging.level.org.springframework.security.web.DefaultSecurityFilterChain=ERROR
logging.level.org.springframework.context.support.PostProcessorRegistrationDelegate=ERROR
logging.level.org.springframework.cloud.context.scope.GenericScope=ERROR
logging.level.org.springframework.boot.actuate.endpoint.EndpointId=ERROR
###################################################################
