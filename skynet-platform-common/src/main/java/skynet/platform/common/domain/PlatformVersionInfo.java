package skynet.platform.common.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/13 9:52
 */
@Getter
@Setter
public class PlatformVersionInfo extends Jsonable {

    @JSONField(ordinal = 10)
    private String title = "skynet-platform-version";

    @JSONField(ordinal = 20, name = "ant-xagent")
    private Map<String, Object> agent;

    @JSONField(ordinal = 30, name = "ant-xmanager")
    private Map<String, Object> manager;

    @JSONField(ordinal = 40, format = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date created = new Date();


    @Override
    public String toString() {
        return JSON.toJSONString(this, JSONWriter.Feature.PrettyFormat);

    }
}