package skynet.platform.common.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public final class AntServiceParam {

    public final static String PROP_PREFIX = "skynet.action.service";

    @JSONField(ordinal = 10)
    private String name;
    @JSONField(ordinal = 20)
    private String desc;
    @JSONField(ordinal = 30)
    private Map<String, Object> context;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
