package skynet.platform.common;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.Assert;
import oshi.PlatformEnum;
import oshi.SystemInfo;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;

import java.nio.charset.StandardCharsets;


/**
 * Application Boot Environment Configuration
 *
 * <p>This utility class provides centralized configuration and initialization
 * for Skynet platform applications. It defines standard service identifiers,
 * action points, and performs essential system property setup during application
 * bootstrap.</p>
 *
 * <p>The class follows the Skynet naming convention where services are identified
 * by their action points in the format: {service-id}@{plugin-code}</p>
 *
 * <p>Key responsibilities:</p>
 * <ul>
 *   <li>Define standard service identifiers and action points</li>
 *   <li>Initialize system properties for JSON processing</li>
 *   <li>Configure security token settings</li>
 *   <li>Set up application-specific environment variables</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 3.4.15
 */
@Slf4j
public class AppBootEnvironment {

    /** Agent service type identifier */
    public static final String AGENT = "xagent";

    /** Manager service type identifier */
    public static final String MANAGER = "xmanager";

    /** Ant plugin code - the core platform plugin identifier */
    public static final String ANT_CODE = "ant";

    /** Complete agent service identifier: ant-xagent */
    public static final String AGENT_SERVICE_ID = ANT_CODE + "-" + AGENT;

    /** Complete manager service identifier: ant-xmanager */
    public static final String MANAGER_SERVICE_ID = ANT_CODE + "-" + MANAGER;

    /** Agent action point in Skynet format: ant-xagent@ant */
    public static final String AGENT_ACTION_POINT = AGENT_SERVICE_ID + "@" + ANT_CODE;

    /** Manager action point in Skynet format: ant-xmanager@ant */
    public static final String MANAGER_ACTION_POINT = MANAGER_SERVICE_ID + "@" + ANT_CODE;

    /**
     * Initialize the application boot environment with the specified action point.
     *
     * <p>This method performs essential system configuration including:</p>
     * <ul>
     *   <li>Disabling Jackson annotations for FastJSON2 to avoid conflicts</li>
     *   <li>Setting the security token key for authentication</li>
     *   <li>Configuring other platform-specific system properties</li>
     * </ul>
     *
     * <p>This method should be called early in the application startup process,
     * typically from the main method of Bootstrap classes.</p>
     *
     * @param actionPoint the action point identifier for this application instance
     *                   (e.g., "ant-xagent@ant" or "ant-xmanager@ant")
     * @throws IllegalArgumentException if actionPoint is null or empty
     */
    public static void init(String actionPoint) {
        if (actionPoint == null || actionPoint.trim().isEmpty()) {
            throw new IllegalArgumentException("Action point cannot be null or empty");
        }

        log.info("Initializing Skynet application environment for action point: {}", actionPoint);
        System.out.printf("Initialize %s ...%n", actionPoint);

        // Configure FastJSON2 to not use Jackson annotations to avoid conflicts
        System.setProperty("fastjson2.useJacksonAnnotation", "false");
        log.debug("Disabled Jackson annotations for FastJSON2");

        // Set the security token key for authentication
        System.setProperty("skynet.token.key", "skynet_token");
        log.debug("Configured security token key");

        Assert.isTrue(SystemInfo.getCurrentPlatform() != PlatformEnum.UNKNOWN, "Not Support Current Platform.");
        log.debug("Init actionPoint = {} begin ...", actionPoint);
        MDC.put("actionId", actionPoint);
        System.setProperty(SkynetProperties.SKYNET_ACTION_POINT_KEY, actionPoint);
        System.setProperty("skynet.pid", String.valueOf(OsUtil.getCurrentPid()));
        System.setProperty("file.encoding", System.getProperty("file.encoding", StandardCharsets.UTF_8.toString()));
        System.setProperty("spring.profiles.active", actionPoint.replace("ant-", "").replace("@ant", "").replace("-", ""));
        log.debug("Init actionPoint={} end.", actionPoint);
    }
}
