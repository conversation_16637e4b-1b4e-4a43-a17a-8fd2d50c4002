package skynet.platform.common.config;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.HashMap;
import java.util.Map;

/**
 * Skynet Platform Configuration Properties
 *
 * <p>This class contains platform-wide configuration properties that control
 * various aspects of the Skynet platform behavior, including service discovery
 * metadata and monitoring integration settings.</p>
 *
 * <p>Configuration properties are typically loaded from application.properties
 * or application.yml files with the prefix "skynet.platform".</p>
 *
 * <p>Example configuration:</p>
 * <pre>
 * skynet.platform.disabledSkynet2PrometheusTargetEndpoint=false
 * skynet.platform.discoveryMetadata.region=us-west-1
 * skynet.platform.discoveryMetadata.zone=zone-a
 * </pre>
 *
 * <AUTHOR>
 * @since 3.4.15
 * @see skynet.platform.common.config.PlatformCommonAutoConfiguration
 */
@Getter
@Setter
public class PlatformProperties extends Jsonable {

    /**
     * Service discovery metadata map.
     *
     * <p>This map contains key-value pairs that will be included in service
     * registration metadata. Common use cases include:</p>
     * <ul>
     *   <li>Region and zone information for geographic routing</li>
     *   <li>Environment tags (dev, test, prod)</li>
     *   <li>Custom service attributes for load balancing</li>
     * </ul>
     *
     * <p>Default: empty HashMap</p>
     */
    private Map<String, Object> discoveryMetadata = new HashMap<>();

    /**
     * Controls whether skynet-agent and skynet-manager services should be
     * excluded from Prometheus target endpoints.
     *
     * <p>When set to {@code true}, the platform services (agent and manager)
     * will not be automatically registered as Prometheus scraping targets.
     * This is useful in environments where you want to monitor only business
     * services and exclude platform infrastructure services.</p>
     *
     * <p>When set to {@code false}, all services including platform services
     * will be available for Prometheus monitoring.</p>
     *
     * <p>Default: {@code true} (platform services excluded from monitoring)</p>
     */
    private boolean disabledSkynet2PrometheusTargetEndpoint = true;
}
