package skynet.platform.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import skynet.boot.annotation.EnableSkynetSecurity;
import skynet.boot.security.config.SkynetBaseAuthAutoConfiguration;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.common.endpoint.PrometheusTargetEndpoint;
import skynet.platform.common.endpoint.SkynetOsInfoContributor;
import skynet.platform.common.env.BootEnvironmentBuilder;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetSettingManager;


/**
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableSkynetSecurity
@AutoConfigureAfter(SkynetBaseAuthAutoConfiguration.class)
public class PlatformCommonAutoConfiguration {

    @Bean
    @ConfigurationProperties("skynet.platform")
    public PlatformProperties platformProperties() {
        return new PlatformProperties();
    }


    @Bean
    public BootEnvironmentBuilder bootEnvironmentBuilder(Environment environment, IAntConfigService antConfigService,
                                                         ManagerEncryptor managerEncryptor,
                                                         SkynetSettingManager skynetSettingManager,
                                                         LoadBalancerClient loadBalancerClient) throws Exception {
        return new BootEnvironmentBuilder(environment, antConfigService, managerEncryptor,
                skynetSettingManager, loadBalancerClient);
    }

    @Bean
    public SkynetOsInfoContributor skynetOsInfoContributor() {
        return new SkynetOsInfoContributor();
    }

    @Bean
    public PrometheusTargetEndpoint prometheusTargetEndpoint(IAntConfigService configSrv4Server, PlatformProperties platformProperties) {
        return new PrometheusTargetEndpoint(configSrv4Server, platformProperties);
    }
}