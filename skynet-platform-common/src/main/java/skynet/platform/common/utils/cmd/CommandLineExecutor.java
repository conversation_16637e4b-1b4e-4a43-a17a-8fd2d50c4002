package skynet.platform.common.utils.cmd;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.StringUtils;
import org.codehaus.plexus.util.cli.*;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class CommandLineExecutor {

    public static String executeCommandLine(Commandline cmd) throws CommandLineException {
        CommandLineUtils.StringStreamConsumer stdout = new CommandLineUtils.StringStreamConsumer();
        CommandLineUtils.StringStreamConsumer stderr = new CommandLineUtils.StringStreamConsumer();
        CommandLineCallable commandLineCallable = CommandLineExecutor.executeCommandLine(cmd, null, stdout, stderr, -1);
        int returnCode = commandLineCallable.call();
        log.info("exec cmd [{}] returnCode={}.\n{}", cmd, returnCode, stdout.getOutput());
        if (returnCode != 0) {
            throw new CommandLineException(stderr.getOutput());
        }
        return stdout.getOutput();
    }

    public static CommandLineCallable executeCommandLine(final Commandline cl, final InputStream systemIn, final StreamConsumer systemOut, final StreamConsumer systemErr, final int timeoutInSeconds) throws CommandLineException {

        if (cl == null) {
            throw new IllegalArgumentException("cl cannot be null.");
        }
        System.out.println("starting the program in shell");
        System.out.println("=========================================================");
        if (cl.getWorkingDirectory() != null && StringUtils.isNotEmpty(cl.getWorkingDirectory().getAbsolutePath())) {
            executeShell("cd " + cl.getWorkingDirectory().getAbsolutePath() + "\n chmod -R 777 *");
        }
        System.out.println("start to executable " + cl);
        final Process p = cl.execute();
        System.out.println("=========================================================");
        final StreamFeeder inputFeeder = systemIn != null ? new StreamFeeder(systemIn, p.getOutputStream()) : null;

        final StreamPumper outputPumper = new StreamPumper(p.getInputStream(), systemOut);

        final StreamPumper errorPumper = new StreamPumper(p.getErrorStream(), systemErr);

        if (inputFeeder != null) {
            inputFeeder.start();
        }
        outputPumper.start();
        errorPumper.start();
        final ProcessHook processHook = new ProcessHook(p);
        return new CommandLineCallable() {
            @Override
            public Integer call() throws CommandLineException {
                try {
                    int returnValue;
                    if (timeoutInSeconds <= 0) {
                        returnValue = p.waitFor();
                    } else {
                        long now = System.currentTimeMillis();
                        long timeoutInMillis = 1000L * timeoutInSeconds;
                        long finish = now + timeoutInMillis;
                        while (CommandLineUtils.isAlive(p) && (System.currentTimeMillis() < finish)) {
                            Thread.sleep(50);
                        }
                        if (CommandLineUtils.isAlive(p)) {
                            throw new InterruptedException("Process timeout out after " + timeoutInSeconds + " seconds");
                        }
                        returnValue = p.exitValue();
                    }

                    waitForAllPumpers(inputFeeder, outputPumper, errorPumper);
                    return returnValue;
                } catch (InterruptedException ex) {
                    /*
                     * if (inputFeeder != null) { inputFeeder.disable(); } outputPumper.disable(); errorPumper.disable();
                     */
                    throw new CommandLineException("Error while executing external command, process killed.", ex);
                } finally {
                    //内存泄露风险 by lyhu2019年08月16日18:02:02
                    //ShutdownHookUtils.removeShutdownHook(processHook);

                    processHook.start();

                    if (inputFeeder != null) {
                        inputFeeder.close();
                    }

                    outputPumper.close();
                    errorPumper.close();
                }
            }

            @Override
            public void destroy() {
                // logger.debug("stop tail process", "CommandLineCallable");
                p.destroy();
                if (CommandLineUtils.isAlive(p)) {
                    p.destroyForcibly();
                }
            }

            @Override
            public long getPid() {
                try {
                    Object obj = getFieldValue(p, "pid");
                    assert obj != null;
                    return Long.parseLong(obj.toString());
                } catch (NoSuchFieldException e) {
                    try {
                        Object obj = getFieldValue(p, "handle");
                        assert obj != null;
                        return Long.parseLong(obj.toString());
                    } catch (NoSuchFieldException ex) {
                        throw new RuntimeException("cannot get pid", ex);
                    }
                }
            }

            private Object getFieldValue(Object obj, String fieldName) throws NoSuchFieldException {
                try {
                    Field field = obj.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (NoSuchFieldException e) {
                    throw e;
                } catch (Exception e) {
                    return null;
                }
            }
        };

    }

    /**
     * 执行命令，并等待执行结束
     *
     * @param cl
     * @param systemIn
     * @param systemOut
     * @param systemErr
     * @throws Exception
     */
    public static void executeCommandLine(final Commandline cl, final InputStream systemIn, final StreamConsumer systemOut, final StreamConsumer systemErr) throws Exception {
        if (cl == null) {
            throw new IllegalArgumentException("Commandline cannot be null.");
        }

        log.debug("starting the program in shell");
        log.debug("=========================================================");
        if (cl.getWorkingDirectory() != null && StringUtils.isNotEmpty(cl.getWorkingDirectory().getAbsolutePath())) {
            executeShell("cd " + cl.getWorkingDirectory().getAbsolutePath() + "\n chmod -R 777 *");
        }
        log.debug("start to executable  {}", cl);

        Process p = null;
        try {
            p = cl.execute();
        } catch (CommandLineException e) {
            throw new CommandLineException(String.format("%s, [%s]", e.getMessage(), cl));
        }

        if (log.isDebugEnabled()) {
            log.debug("return process information={}", JSON.toJSONString(p));
        }
        log.debug("=========================================================");

        final StreamFeeder inputFeeder = systemIn != null ? new StreamFeeder(systemIn, p.getOutputStream()) : null;

        final StreamPumper outputPumper = new StreamPumper(p.getInputStream(), systemOut);
        final StreamPumper errorPumper = new StreamPumper(p.getErrorStream(), systemErr);

        if (inputFeeder != null) {
            inputFeeder.start();
        }

        outputPumper.start();
        errorPumper.start();
        outputPumper.join();
        errorPumper.join();

//        final ProcessHook processHook = new ProcessHook(p);// 152s
//        ShutdownHookUtils.addShutDownHook(processHook);
    }

    public static List<String> execute(Commandline commandline) throws Exception {

        log.debug("CMD={}", commandline);

        List<String> lines = new ArrayList<>(10);

        CommandLineExecutor.executeCommandLine(commandline, null, line -> {
            lines.add(line);
            log.debug(line);
        }, log::error);
        return lines;
    }

    private static void waitForAllPumpers(StreamFeeder inputFeeder, StreamPumper outputPumper, StreamPumper errorPumper) throws InterruptedException {
        /*
         * if (inputFeeder != null) { inputFeeder.waitUntilDone(); }
         *
         * outputPumper.waitUntilDone(); errorPumper.waitUntilDone();
         */
    }

    public static String executeShell(String shellCommand) {
        log.debug("Exec command={}", shellCommand);

        StringBuilder result = new StringBuilder();
        try {
            // 执行Shell命令
            String[] cmd = {"/bin/sh", "-c", shellCommand};
            Process process = Runtime.getRuntime().exec(cmd);
            if (process != null) {
                // bufferedReader用于读取Shell的输出内容
                log.debug("process number={}", process);
                try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()), 1024)) {
                    try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()), 1024)) {
                        process.waitFor();
                        log.debug("shell exec end.");
                        // 读取Shell的输出内容，并添加到stringBuffer中
                        String line;
                        while ((line = bufferedReader.readLine()) != null) {
                            log.debug("result={}", line);
                            result.append(line).append(System.lineSeparator());
                        }
                        while ((line = errorReader.readLine()) != null) {
                            log.debug("error information={}", line);
                            result.append("execute fail:");
                            result.append(line).append(System.lineSeparator());
                        }
                    }
                }
            } else {
                log.debug("pid does not exist");
            }
        } catch (Exception ioe) {
            log.error("Exec Shell=[{}] command Error={}", shellCommand, ioe.getMessage(), ioe);
        }
        return result.toString();
    }
}
