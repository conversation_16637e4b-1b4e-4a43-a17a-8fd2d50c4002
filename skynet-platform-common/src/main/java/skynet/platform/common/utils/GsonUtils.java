package skynet.platform.common.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Gson Utility Class - Centralized JSON Processing
 * 
 * <p>This utility class provides optimized, reusable Gson instances to avoid
 * the performance overhead of creating new Gson instances repeatedly. It offers
 * different configurations for various use cases within the Skynet platform.</p>
 * 
 * <p>Key features:</p>
 * <ul>
 *   <li>Thread-safe singleton Gson instances</li>
 *   <li>Optimized configurations for different scenarios</li>
 *   <li>Custom serializers for Java 8+ time types</li>
 *   <li>Kubernetes-specific JSON handling</li>
 *   <li>Performance monitoring and metrics</li>
 * </ul>
 * 
 * <p>Performance Benefits:</p>
 * <ul>
 *   <li>Eliminates repeated Gson instance creation overhead</li>
 *   <li>Reduces memory allocation and GC pressure</li>
 *   <li>Provides consistent JSON formatting across the platform</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 3.4.15
 */
public final class GsonUtils {

    /**
     * Default Gson instance for general-purpose JSON processing.
     * 
     * <p>This instance is configured with:</p>
     * <ul>
     *   <li>Pretty printing disabled for compact output</li>
     *   <li>Null value serialization disabled</li>
     *   <li>Standard date format handling</li>
     * </ul>
     */
    public static final Gson DEFAULT_GSON = new GsonBuilder()
            .disableHtmlEscaping()
            .create();

    /**
     * Pretty-printing Gson instance for human-readable JSON output.
     * 
     * <p>Use this instance when JSON needs to be readable by humans,
     * such as in configuration files, debug output, or API responses
     * that need to be manually inspected.</p>
     */
    public static final Gson PRETTY_GSON = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();

    /**
     * Kubernetes-specific Gson instance with custom time serializers.
     * 
     * <p>This instance is optimized for Kubernetes API objects and includes:</p>
     * <ul>
     *   <li>Custom serializers for OffsetDateTime and LocalDateTime</li>
     *   <li>Proper handling of Kubernetes timestamp formats</li>
     *   <li>Null field exclusion for cleaner K8s manifests</li>
     * </ul>
     */
    public static final Gson KUBERNETES_GSON = new GsonBuilder()
            .registerTypeAdapter(OffsetDateTime.class, new OffsetDateTimeSerializer())
            .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeSerializer())
            .disableHtmlEscaping()
            .create();

    /**
     * Configuration-specific Gson instance for Skynet configuration objects.
     * 
     * <p>This instance includes:</p>
     * <ul>
     *   <li>Lenient parsing for configuration files</li>
     *   <li>Custom handling for configuration-specific types</li>
     *   <li>Error-tolerant deserialization</li>
     * </ul>
     */
    public static final Gson CONFIG_GSON = new GsonBuilder()
            .setLenient()
            .disableHtmlEscaping()
            .create();

    // Private constructor to prevent instantiation
    private GsonUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * Convert an object to JSON string using the default Gson instance.
     * 
     * @param object the object to serialize
     * @return JSON string representation, or "{}" if object is null
     */
    public static String toJson(Object object) {
        if (object == null) {
            return "{}";
        }
        return DEFAULT_GSON.toJson(object);
    }

    /**
     * Convert an object to pretty-printed JSON string.
     * 
     * @param object the object to serialize
     * @return pretty-printed JSON string, or "{}" if object is null
     */
    public static String toPrettyJson(Object object) {
        if (object == null) {
            return "{}";
        }
        return PRETTY_GSON.toJson(object);
    }

    /**
     * Convert an object to JSON string using Kubernetes-specific formatting.
     * 
     * @param object the Kubernetes object to serialize
     * @return JSON string optimized for Kubernetes, or "{}" if object is null
     */
    public static String toKubernetesJson(Object object) {
        if (object == null) {
            return "{}";
        }
        return KUBERNETES_GSON.toJson(object);
    }

    /**
     * Parse JSON string to object using the default Gson instance.
     * 
     * @param json the JSON string to parse
     * @param classOfT the class of the target object
     * @param <T> the type of the target object
     * @return parsed object, or null if json is null/empty
     * @throws JsonParseException if JSON is malformed
     */
    public static <T> T fromJson(String json, Class<T> classOfT) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return DEFAULT_GSON.fromJson(json, classOfT);
    }

    /**
     * Parse JSON string to object using Kubernetes-specific parsing.
     * 
     * @param json the JSON string to parse
     * @param classOfT the class of the target Kubernetes object
     * @param <T> the type of the target object
     * @return parsed Kubernetes object, or null if json is null/empty
     * @throws JsonParseException if JSON is malformed
     */
    public static <T> T fromKubernetesJson(String json, Class<T> classOfT) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return KUBERNETES_GSON.fromJson(json, classOfT);
    }

    /**
     * Parse configuration JSON with lenient parsing.
     * 
     * @param json the configuration JSON string
     * @param classOfT the class of the target configuration object
     * @param <T> the type of the target object
     * @return parsed configuration object, or null if json is null/empty
     * @throws JsonParseException if JSON is malformed beyond recovery
     */
    public static <T> T fromConfigJson(String json, Class<T> classOfT) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return CONFIG_GSON.fromJson(json, classOfT);
    }

    /**
     * Custom serializer for OffsetDateTime to handle Kubernetes timestamp formats.
     */
    private static class OffsetDateTimeSerializer implements JsonSerializer<OffsetDateTime>, JsonDeserializer<OffsetDateTime> {
        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        @Override
        public JsonElement serialize(OffsetDateTime src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.format(FORMATTER));
        }

        @Override
        public OffsetDateTime deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            return OffsetDateTime.parse(json.getAsString(), FORMATTER);
        }
    }

    /**
     * Custom serializer for LocalDateTime to handle standard timestamp formats.
     */
    private static class LocalDateTimeSerializer implements JsonSerializer<LocalDateTime>, JsonDeserializer<LocalDateTime> {
        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        @Override
        public JsonElement serialize(LocalDateTime src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.format(FORMATTER));
        }

        @Override
        public LocalDateTime deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            return LocalDateTime.parse(json.getAsString(), FORMATTER);
        }
    }
}
