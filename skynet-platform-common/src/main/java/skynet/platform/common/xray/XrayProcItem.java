package skynet.platform.common.xray;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.common.domain.BootType;

@Getter
@Setter
public class XrayProcItem extends Jsonable {

    private long pid;
    private String name;
    private String desc;
    private BootType bootType;

    public XrayProcItem(long pid, String name, String desc, BootType bootType) {
        this.pid = pid;
        this.name = name;
        this.desc = desc;
        this.bootType = bootType;
    }

    public XrayProcItem() {

    }

    @Override
    public String toString() {
        return super.toString();
    }
}
