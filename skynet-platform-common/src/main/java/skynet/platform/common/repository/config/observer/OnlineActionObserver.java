package skynet.platform.common.repository.config.observer;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public abstract class OnlineActionObserver implements Observer {

    @SneakyThrows
    @Override
    public void update(Observable o, Object arg) {
        @SuppressWarnings("unchecked")
        Map<String, String> settings = (Map<String, String>) arg;

        List<String> result = new ArrayList<>();

        if (settings != null) {
            for (Map.Entry<String, String> entry : settings.entrySet()) {
                String value = entry.getValue();
                String key = StringUtils.substringAfterLast(entry.getKey(), "/");
                if (StringUtils.isBlank(value) && !key.startsWith("_")) {
                    result.add(key);
                }
            }
        }
        this.update(result);
    }

    public abstract void update(List<String> arg) throws Exception;

}
