package skynet.platform.common.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import skynet.boot.common.OsUtil;
import skynet.boot.metrics.SkynetMetricsService;
import skynet.boot.metrics.domain.MetricsLabel;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Online Action Manager - Service Discovery and Load Balancing Component
 *
 * <p>This manager provides real-time tracking and discovery of online services
 * within the Skynet cluster. It maintains an up-to-date view of all running
 * service instances and provides load balancing capabilities for service
 * selection.</p>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li>Real-time service discovery based on ZooKeeper status reporting</li>
 *   <li>Automatic service health monitoring via ZooKeeper heartbeat mechanism</li>
 *   <li>Load balancing with multiple selection strategies</li>
 *   <li>Service metrics collection and reporting</li>
 *   <li>Fault tolerance and automatic failover support</li>
 * </ul>
 *
 * <p>Implementation Details:</p>
 * <ul>
 *   <li>Services automatically register/deregister on startup/shutdown</li>
 *   <li>ZooKeeper ephemeral nodes ensure automatic cleanup on service failure</li>
 *   <li>Heartbeat mechanism provides real-time health status</li>
 *   <li>Thread-safe operations for concurrent access</li>
 * </ul>
 *
 * <p>This component is essential for microservice communication and cluster
 * coordination in the Skynet platform.</p>
 *
 * <AUTHOR> [2016年12月8日上午9:11:30]
 * @since 3.4.15
 * @see IAntConfigService
 * @see AntActionStatus
 * @see SkynetMetricsService
 */
@Slf4j
public class OnlineActionManager {

    private final IAntConfigService antConfigService;
    private final SkynetMetricsService skynetMetricsService;

    public OnlineActionManager(IAntConfigService antConfigService, SkynetMetricsService skynetMetricsService) {
        this.antConfigService = antConfigService;
        this.skynetMetricsService = skynetMetricsService;
    }

    /**
     * Retrieve all online service nodes for the specified action point.
     *
     * <p>This method queries ZooKeeper to get all currently online instances
     * of a specific service. The results include detailed status information
     * for each instance including health status, network endpoints, and
     * runtime metadata.</p>
     *
     * <p>The method is synchronized to ensure thread safety when accessing
     * ZooKeeper and processing the results. Performance metrics are collected
     * to monitor query response times.</p>
     *
     * @param actionPoint the service action point in format "service@plugin"
     *                   (e.g., "user-service@business", "ant-xagent@ant")
     * @return list of AntActionStatus objects representing all online instances
     *         of the specified service. Returns empty list if no instances are online.
     * @throws IllegalArgumentException if actionPoint is null or empty
     * @throws RuntimeException if ZooKeeper communication fails
     * @see AntActionStatus
     */
    public synchronized List<AntActionStatus> getAllNodes(String actionPoint) {

        Assert.hasText(actionPoint, "actionPoint is blank.");
        long cost = System.currentTimeMillis();
        log.debug("getOnlineActionNodeStatus actionPoint:{};", actionPoint);
        Map<String, AntActionStatus> onlineNodes = antConfigService.getOnlineActionNodeStatus(actionPoint, null);
        cost = (System.currentTimeMillis() - cost);
        log.debug("getOnlineActionNodeStatus actionPoint:{};size:{};cost:{}ms;", actionPoint, onlineNodes.size(), cost);

        skynetMetricsService.timer("fetch.online.action.cost.ms", cost);
        skynetMetricsService.counterIncrement("fetch.online.action", new MetricsLabel("action", actionPoint));

        return new ArrayList<>(onlineNodes.values());
    }

    /**
     * 随机获取Action节点
     *
     * @param actionPoint
     * @return
     */
    public synchronized AntActionStatus getNode(String actionPoint) throws Exception {
        Assert.hasText(actionPoint, "actionPoint is blank.");
        return getNode(actionPoint, false);
    }

    /**
     * 获取随机的一个代理服务
     *
     * @param actionPoint
     * @param isFirstLocalIP 是否本机优先
     * @return
     */
    public synchronized AntActionStatus getNode(String actionPoint, boolean isFirstLocalIP) {
        Assert.hasText(actionPoint, "actionPoint is blank.");

        List<AntActionStatus> nodeList = this.getAllNodes(actionPoint);
        // 优先本地IP服务
        List<AntActionStatus> localList = isFirstLocalIP ? nodeList.stream().filter(x -> OsUtil.isLocalIP(x.getIp())).toList() : nodeList;
        if (localList.isEmpty()) {
            localList = nodeList;
        }
        return (!localList.isEmpty()) ? localList.get(new Random().nextInt(localList.size())) : null;
    }
}
