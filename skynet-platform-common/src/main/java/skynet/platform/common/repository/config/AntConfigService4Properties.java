/**
 *
 */
package skynet.platform.common.repository.config;

import java.util.Map;
import java.util.Observer;

/**
 * Skynet 配置服务接口契约
 *
 * <AUTHOR>
 */
public interface AntConfigService4Properties {

    Map<String, String> getGlobalProperties() throws Exception;

    /**
     * 获取属性配置列表
     *
     * <pre>
     * plugin  如果为空时，将是全局级；
     * action  如果为空时，将是插件级
     * </pre>
     *
     * @param plugin   插件名称 如 ant
     * @param action   服务名称 如 ant-cloudadmin-v10
     * @param observer
     * @return
     */
    Map<String, String> getProperties(String plugin, String action, Observer observer);

    /**
     * 设置属性配置
     *
     * <pre>
     * plugin  如果为空时，将是全局级；
     * action  如果为空时，将是插件级
     * </pre>
     *
     * @param plugin   插件名称 如 ant
     * @param action   服务名称 如 ant-cloudadmin-v10
     * @param property 属性键 如 fastjson.enabled
     * @param value    属性值 如 true
     */
    void setProperty(String plugin, String action, String property, String value) throws Exception;

    /**
     * 删除属性配置
     *
     * <pre>
     * plugin  如果为空时，将是全局级；
     * action  如果为空时，将是插件级
     * </pre>
     *
     * @param plugin   插件名称 如 ant
     * @param action   服务名称 如 ant-cloudadmin-v10
     * @param property 属性键 如 fastjson.enabled
     */
    void delProperty(String plugin, String action, String property) throws Exception;

}
