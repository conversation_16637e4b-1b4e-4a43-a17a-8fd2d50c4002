package skynet.platform.common.repository.config.setting;

import java.io.IOException;
import java.util.Map;
import java.util.Observer;

/**
 * <AUTHOR> by jianwu6 on 2020/7/8 9:32
 */
public interface SkynetConfigService {

    Map<String, String> getPropMap(String plugin, String action, Observer observer) throws IOException;

    String getGlobalProps();

    String getProps(String plugin, String action, Observer observer);

    String getProps(String plugin);

    void setProps(String plugin, String value);

    void setProps(String plugin, String action, String value);

    void setProp(String plugin, String action, String property, String value) throws Exception;

    void delProp(String plugin, String action, String property) throws Exception;
}
