package skynet.platform.common.repository.config.observer;

import org.apache.commons.lang3.StringUtils;

import java.util.Observable;
import java.util.Observer;

/**
 * Int配置项 订阅器， 无效数据，将返回 Integer.MAX_VALUE
 *
 * <AUTHOR>
 */
public abstract class IntegerObserver implements Observer {

    public abstract void update(int value);

    @Override
    public void update(Observable o, Object arg) {

        String strValue = (String) arg;
        int r = (StringUtils.isBlank(strValue) || !StringUtils.isNumeric(strValue)) ? Integer.MAX_VALUE : Integer.parseInt(strValue);

        update(r);
    }
}
