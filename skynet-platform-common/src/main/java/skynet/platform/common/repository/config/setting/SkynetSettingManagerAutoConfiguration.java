package skynet.platform.common.repository.config.setting;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.block.ConfigBlockService;
import skynet.platform.common.repository.config.setting.block.support.ConfigBlockService4zk;

/**
 * <AUTHOR> by jianwu6 on 2020/7/27 16:40
 */
@Configuration(proxyBeanMethods = false)
public class SkynetSettingManagerAutoConfiguration {

    @Bean
    public ConfigBlockService configBlockService(IAntConfigService antConfigService) throws Exception {
        return new ConfigBlockService4zk(antConfigService);
    }
//
//    @Bean
//    public LegacyConfigBlockService legacyConfigBlockService(IAntConfigService antConfigService) throws Exception {
//        return new LegacyConfigBlockService(antConfigService);
//    }

//    @Bean
//    public ConfigItemService configItemService(ConfigBlockService configBlockService, LegacyConfigBlockService legacyConfigBlockService) throws Exception {
//        return new ConfigItemService(configBlockService, legacyConfigBlockService);
//    }

    @Primary
    @Bean
    public SkynetSettingManager skynetSettingManager(IAntConfigService antConfigService) throws Exception {
        return new SkynetSettingManager(antConfigService);
    }
}
