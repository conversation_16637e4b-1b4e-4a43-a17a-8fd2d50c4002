/**
 *
 */
package skynet.platform.common.repository.config;


import skynet.platform.common.repository.config.observer.BooleanObserver;
import skynet.platform.common.repository.config.observer.IntegerObserver;
import skynet.platform.common.repository.config.observer.TObserver;

import java.util.Map;
import java.util.Observer;

/**
 * Skynet 配置服务接口契约
 *
 * <AUTHOR>
 */
public interface AntConfigService4Setting {

    /**
     * 获取 集群级别 Setting 路径
     *
     * @return
     */
    String getSettingPath();

    /**
     * 获取 Setting 路径
     *
     * @return
     */
    String getSettingPath(String plugin, String action);

    /**
     * 过滤 Setting 引用，替换Setting中相应的值
     *
     * @param plugin    如果为空，将采用 全局Setting中配置 进行替换
     * @param srcString
     * @param path
     * @param observer
     * @return
     */
    String filterRefSetting(String plugin, String srcString, final String path, final Observer observer);

    String filterRefSetting(String srcString, final String path, final Observer observer);


    /**
     * 获取 配置信息
     *
     * <pre>
     * 由于是Setting采用继承机制，优先从当前插件的Setting中获取，如果没有将从全局中获取
     * </pre>
     *
     * @param key
     * @param observer
     * @return
     */
    String getSetting(String key, Observer observer);

    /**
     * @param plugin   如果为空，将采用 全局Setting中的配置
     * @param key
     * @param observer
     * @return
     */
    String getSetting(String plugin, String key, Observer observer);

    /**
     * 设置 配置
     *
     * @param plugin 如果为空，将采用 全局Setting中的配置
     * @param key    配置项
     * @param value  配置项值
     */
    void setSetting(String plugin, String key, String value);

    /**
     * 获取配置值 如果key 值为空，或没有配置，直接返回 false
     *
     * <pre>
     * 由于是Setting采用继承机制，优先从当前插件的Setting中获取，如果没有将从全局中获取
     * </pre>
     *
     * @param key 配置
     * @return 值
     */

    boolean getBoolSetting(String key, BooleanObserver observer);

    /**
     * 获取 某插件下得 配置项 值 如果key 值为空，或没有配置，直接返回 false
     *
     * @param plugin
     * @param key
     * @param observer
     * @return
     */
    boolean getBoolSetting(String plugin, String key, BooleanObserver observer);

    /**
     * 获取配置值 如果key 值为空，或没有配置，直接返回 Integer.MAX_VALUE
     *
     * <pre>
     * 由于是Setting采用继承机制，优先从当前插件的Setting中获取，如果没有将从全局中获取
     * </pre>
     *
     * @param key 配置
     * @return 值
     */
    int getIntegerSetting(String key, IntegerObserver observer);

    /**
     * 获取 某插件下得 配置项 值 如果key 值为空，或没有配置，直接返回 Integer.MAX_VALUE
     *
     * @param plugin
     * @param key
     * @param observer
     * @return
     */
    int getIntegerSetting(String plugin, String key, IntegerObserver observer);

    /**
     * 获取配置值
     *
     * <pre>
     * 由于是Setting采用继承机制，优先从当前插件的Setting中获取，如果没有将从全局中获取
     * </pre>
     *
     * @param key
     * @param clazz
     * @param observer
     * @return
     */
    <T> T getTSetting(String key, Class<T> clazz, TObserver<T> observer);

    /**
     * 获取 某插件下得 配置项 的对象
     *
     * @param plugin
     * @param key
     * @param clazz
     * @param observer
     * @return
     */
    <T> T getTSetting(String plugin, String key, Class<T> clazz, TObserver<T> observer);

    Map<String, String> getSettings(Observer observer);

    Map<String, String> getSettings(String plugin, Observer observer);
}
