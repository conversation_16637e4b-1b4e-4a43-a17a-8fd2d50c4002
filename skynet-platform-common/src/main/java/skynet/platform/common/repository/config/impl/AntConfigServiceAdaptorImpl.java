package skynet.platform.common.repository.config.impl;

import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.common.repository.config.AntConfigServiceAdaptor;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.observer.AllOnlineActionStatusObserver;
import skynet.platform.common.repository.config.observer.OnlineActionObserver;
import skynet.platform.common.repository.config.observer.OnlineActionStatusObserver;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.*;

/**
 * 该类用于扩充IAntConfigService的功能, 获取所有在线服务的状态</br>
 * 由于服务xxx的运行时状态存储在/skynet/cluster/online/action/xxx下面，xxx上线前/skynet/cluster/online/action/xxx并不存在，
 * 需要在/skynet/cluster/online/action和/skynet/cluster/online/action/xxx上都监听数据变化， AntConfigServiceAdaptorImpl用于封装该复杂逻辑。
 *
 * <AUTHOR>
 */
@Slf4j
public class AntConfigServiceAdaptorImpl implements AntConfigServiceAdaptor {

    private final IAntConfigService antConfigService;
    private final Set<String> watchedNodeName = new TreeSet<>();
    private final Map<String, List<AntActionStatus>> allActionStatusCache = new HashMap<>();
    private final Set<AllOnlineActionStatusObserver> registeredObservers = new HashSet<>();

    private final OnlineActionObserver actionNameObserver = new OnlineActionObserver() {
        @Override
        public void update(List<String> arg) throws Exception {
            handleOnlineActionNames(this, arg);
        }

    };
    private volatile boolean actionNameObserverSet = false;

    private class CustomOnlineActionStatusObserver extends OnlineActionStatusObserver {
        private final String actionName;

        CustomOnlineActionStatusObserver(String actionName) {
            this.actionName = actionName;
        }

        @Override
        public void update(Map<String, AntActionStatus> actionStatusMap) {
            handleOnlineActionStatusMap(this, this.actionName, actionStatusMap);
        }
    }

    public AntConfigServiceAdaptorImpl(IAntConfigService antConfigService) {
        super();
        this.antConfigService = antConfigService;
    }

    @SneakyThrows
    @Override
    public synchronized Map<String, List<AntActionStatus>> getAllOnlineActionStatus(AllOnlineActionStatusObserver o) {
        try {
            if (o != null) {
                this.registeredObservers.add(o);
            }
            OnlineActionObserver observerToSet = (this.actionNameObserverSet) ? null : this.actionNameObserver;
            List<String> onlineActionNames = this.antConfigService.getOnlineActionNames(observerToSet);
            this.actionNameObserverSet = true;
            this.handleOnlineActionNames(this, onlineActionNames);
        } catch (Throwable e) {
            if (o != null) {
                this.registeredObservers.remove(o);
            }
            throw e;
        }
        return dupCache();
    }

    private synchronized void handleOnlineActionNames(Object caller, List<String> nameList) throws Exception {
        for (String nameInList : nameList) {
            CustomOnlineActionStatusObserver observer = null;
            //避免重复设置监听器(修复2.1.1011-RELEASE之前版本的内存泄漏问题)
            if (!this.watchedNodeName.contains(nameInList)) {
                observer = new CustomOnlineActionStatusObserver(nameInList);
                this.watchedNodeName.add(nameInList);
            }
            Map<String, AntActionStatus> statusMap = this.antConfigService.getOnlineActionNodeStatus(nameInList, observer);
            this.handleOnlineActionStatusMap(caller, nameInList, statusMap);
        }
        Set<String> tmp = new TreeSet<>(nameList);
        for (String watchedNodeName : this.watchedNodeName) {
            if (!tmp.contains(watchedNodeName)) {
                this.allActionStatusCache.remove(watchedNodeName);
            }
        }
    }

    private synchronized void handleOnlineActionStatusMap(Object caller, String actionName,
                                                          Map<String, AntActionStatus> statusMap) {

        /*
         * 更新本地缓存
         */
        List<AntActionStatus> statusList = new ArrayList<>();
        for (Map.Entry<String, AntActionStatus> entry : statusMap.entrySet()) {
            AntActionStatus status = entry.getValue();
            statusList.add(status);
        }
        if (statusList.isEmpty()) {
            this.allActionStatusCache.remove(actionName);
        } else {
            this.allActionStatusCache.put(actionName, statusList);
        }

        /*
         * caller != this 意味着监听到了数据变化，该方法由回调匿名类触发，是而不是AntConfigServiceAdaptorImpl的方法主动调用。
         * 这种情况下，需要调用所有已注册AllOnlineActionStatusObserver的回调方法
         */
        if (caller != this) {
            for (AllOnlineActionStatusObserver registeredObserver : this.registeredObservers) {
                //使用本地缓存的复制作为回调参数，避免回调方法内部修改本地缓存。
                Map<String, List<AntActionStatus>> dup = dupCache();
                if (registeredObserver != null) {
                    registeredObserver.update(null, dup);
                }
            }
        }
    }

    /**
     * 复制缓存
     *
     * @return
     */
    private Map<String, List<AntActionStatus>> dupCache() {
        Map<String, List<AntActionStatus>> ret = new HashMap<>();
        for (Map.Entry<String, List<AntActionStatus>> entry : this.allActionStatusCache.entrySet()) {
            List<AntActionStatus> statusList = entry.getValue();
            List<AntActionStatus> dupList = new ArrayList<>();
            if (statusList != null && !statusList.isEmpty()) {
                for (AntActionStatus status : statusList) {
                    String json = JSON.toJSONString(status);
                    AntActionStatus dup = JSON.parseObject(json, AntActionStatus.class);
                    dupList.add(dup);
                }
            }
            ret.put(entry.getKey(), dupList);
        }
        return ret;
    }
}
