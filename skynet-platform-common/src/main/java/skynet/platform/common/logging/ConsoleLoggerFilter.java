package skynet.platform.common.logging;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @date 2020/5/13 11:34
 */
public class ConsoleLoggerFilter extends Filter<ILoggingEvent> {

    public static final String LOG_COLLECT_DISABLED_KEY = "log-collect-disabled";


    @Override
    public FilterReply decide(ILoggingEvent event) {
        return StringUtils.isNoneBlank(MDC.get(LOG_COLLECT_DISABLED_KEY)) ? FilterReply.DENY : FilterReply.ACCEPT;
    }
}