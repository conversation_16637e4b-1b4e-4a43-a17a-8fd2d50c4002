package skynet.platform.common.auth;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import skynet.boot.security.auth.AuthUtils;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 */
@Slf4j
public class AuthWsClient implements AutoCloseable {
    private final AuthClientProperties authClientProperties;

    private WebSocketClient webSocketClient;

    public AuthWsClient(AuthClientProperties authClientProperties) {
        this.authClientProperties = authClientProperties;
    }

    public void connect(URI uri, WsMessageHandler wsMessageHandler) throws URISyntaxException {
        log.debug("uri={}", uri);
        uri = new URI(AuthUtils.assembleRequestUrl(uri.toString(), authClientProperties.getApiKey(), authClientProperties.getApiSecret()));
        log.debug("auth url={}", uri);

        this.webSocketClient = new WebSocketClient(uri) {
            @Override
            public void onOpen(ServerHandshake serverHandshake) {
                try {
                    log.debug("onOpen[url={}]", uri);
                    wsMessageHandler.onOpen();
                } catch (Exception e) {
                    log.error("Remote ws open error", e);
                }
            }

            @Override
            public void onMessage(String message) {
                try {
                    log.trace("onMessage={} [url={}]", message, uri);
                    wsMessageHandler.onMessage(message);
                } catch (IllegalStateException e) {
                    log.error("onMessage err=" + e.getMessage());
                } catch (Exception e) {
                    log.error("onMessage error", e);
                }
            }

            @Override
            public void onClose(int code, String reason, boolean remote) {
                try {
                    log.debug("OnClose code={} reason={} remote={} ", code, reason, remote);
                    wsMessageHandler.onClose(code, reason, remote);
                } catch (Exception e) {
                    log.error("onClose={};{}", reason, e.getMessage());
                }
            }

            @Override
            public void onError(Exception ex) {
                try {
                    log.debug("onError {}", ex.getMessage());
                    wsMessageHandler.onError(ex);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        };
        webSocketClient.connect();
        log.debug("webSocketClient.connect()");
    }

    public void send(String message) {

        log.debug("send message={}", message);
        webSocketClient.send(message);
    }

    @Override
    public void close() throws Exception {
        if (this.webSocketClient != null) {
            this.webSocketClient.close();
            this.webSocketClient = null;
        }
    }
}