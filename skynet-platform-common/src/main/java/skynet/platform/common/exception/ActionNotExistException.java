package skynet.platform.common.exception;

import lombok.Getter;

import java.io.Serial;

/**
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年10月30日 下午8:28:00]
 */
@Getter
public class ActionNotExistException extends RuntimeException {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 133424423L;

    /**
     * -- GETTER --
     *
     * @return the plugin
     */
    private final String plugin;

    /**
     * -- GETTER --
     *
     * @return the actionName
     */
    private final String actionName;

    /**
     * @param plugin
     * @param actionName
     */
    public ActionNotExistException(String plugin, String actionName) {
        super(String.format("the action [%s@%s] is not exist", actionName, plugin));
        this.plugin = plugin;
        this.actionName = actionName;
    }

    public String getActionPoint() {
        return String.format("%s@%s", actionName, plugin);
    }
}
