package skynet.platform.common;

import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.env.MockEnvironment;
import skynet.boot.SkynetProperties;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.boot.zookeeper.impl.ZkConfigServiceImpl;
import skynet.platform.common.repository.config.impl.AntConfigServiceImpl;
import skynet.platform.common.repository.config.setting.SkynetConfigServiceImpl;
import skynet.platform.common.repository.config.setting.SkynetConfigType;

import java.util.Map;

/**
 * <AUTHOR> by jianwu6 on 2020/7/15 13:52
 */
public class SkynetConfigServiceImplTest {

    private final String plugin = "turing-test";
    private final String action = "test-svc";

    private SkynetConfigServiceImpl skynetConfigService;

    @Before
    public void setUp() throws Exception {
        System.setProperty("skynet.zookeeper.cluster_name", "skynet");
        System.setProperty("skynet.zookeeper.server_list", "172.31.128.153:2181");

        MockEnvironment mockEnvironment = new MockEnvironment();
        SkynetProperties skynetProperties = new SkynetProperties(mockEnvironment);
        skynetProperties.setActionTitle("[TEST]测试服务");
        skynetProperties.setActionDesc("测试服务");
        skynetProperties.setActionPoint("test-svc@turing-test");
        skynetProperties.setHome("/iflytek/server/skynet");

        SkynetZkProperties skynetZkProperties = new SkynetZkProperties();
        ZkConfigService zkConfigService = new ZkConfigServiceImpl(skynetZkProperties);

        AntConfigServiceImpl antConfigService = new AntConfigServiceImpl(zkConfigService, skynetProperties);
        skynetConfigService = new SkynetConfigServiceImpl(antConfigService, SkynetConfigType.PROPERTY);
    }

    @Test
    public void getGlobalProps() throws Exception {
        System.out.println(skynetConfigService.getGlobalProps());
    }

    @Test
    public void getProps() throws Exception {
        System.out.println(skynetConfigService.getProps(plugin, action, null));
    }

    @Test
    public void getPropMap() throws Exception {
        Map<String, String> propMap = skynetConfigService.getPropMap(plugin, action, null);
        System.out.println(propMap.toString());
    }

    @Test
    public void setProps() throws Exception {
        String value = "# test logger\n" +
                "skynet.boot=INF0\n" +
                "skynet.boot=INF0";
        skynetConfigService.setProps(plugin, action, value);
    }

    @Test
    public void setProp() throws Exception {
        skynetConfigService.setProp(plugin, action, "a.key", "a.value");
    }

    @Test
    public void delProp() throws Exception {
        skynetConfigService.delProp(plugin, action, "a.key");
    }

}
